# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(3, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID="pacto-datalake-dev"
DATASET_ID='dados_churn'

credentials = service_account.Credentials.from_service_account_info(
{
  "type": "service_account",
  "project_id": "pacto-datalake-dev",
  "private_key_id": "4c683d60ff607745a7d694c06b7fff7715d153e7",
  "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
  "client_email": "<EMAIL>",
  "client_id": "109488522443674963556",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/pacto-zw-upload-storage%40pacto-datalake-dev.iam.gserviceaccount.com"
}
)


# land_zone_db_data


util = Util(PROJECT_ID, DATASET_ID, base_path='ipnet', chave_rede=None, credentials=credentials)
util.generate_data_all_zones('acessogeral', parallel=True, parquet_zw=True)
util.generate_data_all_zones('contrato', parallel=True, parquet_zw=True)
util.generate_data_all_zones('contratoduracao', parallel=True, parquet_zw=True)
util.generate_data_all_zones('contratomodalidade', parallel=True, parquet_zw=True)
util.generate_data_all_zones('modalidade', parallel=True, parquet_zw=True)
util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True, parquet_zw=True)



#util.generate_data_all_zones('acessocliente', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('contrato', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('contratoduracao', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('contratomodalidade', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('contratooperacao', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('empresa', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('modalidade', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('tpv', parallel=True, parquet_zw=True)
#
#util.generate_data_all_zones('trancamentocontrato', parallel=True, parquet_zw=True)
# NOVO ##########
# raw-rfv-churn/stage
# temos de ajustar esta função para ele poder receber em qual buckt os dados subiram....
# util.generate_data_all_zones('agregacao_geral', parallel=True, parquet_zw=True)

###################################################################################################################################


