import os
from datetime import datetime, timedelta
import re

def find_max_path(path: str):
    max_path = 0
    for file in os.listdir('./sql/kpis_produtos_pacto'):
        if file.startswith(path):
            try:
                index = int(file.split('_')[-1].split('.')[0])
            except ValueError:
                continue
            if index > max_path:
                max_path = index
    return max_path

# Define the path to the SQL file
def update_date_interval(file_path: str = "receita_compensado", days: int = 730, future_days: int = 0):
    index = find_max_path(file_path)
    sql_file_path = f'./sql/kpis_produtos_pacto/{file_path}_{index}.sql'

    # Read the SQL file
    with open(sql_file_path, 'r') as file:
        sql_content = file.read()

    # Extract the date range from the SQL content
    actual_start_date_str = re.search(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', sql_content).group()
    start_date = datetime.strptime(actual_start_date_str, '%Y-%m-%d %H:%M:%S')

    end_date = (start_date + timedelta(days=days-1)).replace(second=59, minute=59, hour=23)
    new_end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
    new_start_date_str = (end_date + timedelta(seconds=1)).strftime('%Y-%m-%d %H:%M:%S')
    
    current_date_str = datetime.now().replace(second=59, minute=59, hour=23).strftime('%Y-%m-%d %H:%M:%S')
    current_date = datetime.strptime(current_date_str, '%Y-%m-%d %H:%M:%S')

    if end_date > current_date:
        if future_days >= 0:
            updated_sql_content = sql_content.replace("now()", f"now() + interval '{future_days} day'")
            with open(sql_file_path, 'w') as new_file:
                new_file.write(updated_sql_content)
            print(f"Data atualizada para {future_days} dias no futuro.")
        else:
            print(end_date)
            print(current_date)
            print("Não é possível atualizar a data para um dia futuro.")
        return True

    # Check if the date range is greater than 2 years
    if ((end_date + timedelta(seconds=1)) - start_date).days >= days:
        # Create a new SQL file with the updated date range
        updated_sql_content = sql_content.replace("now()", f"'{new_end_date_str}'")
        
        with open(sql_file_path, 'w') as new_file:
            new_file.write(updated_sql_content)
        
        # Update the original SQL file with the new date range
        new_sql_content = sql_content.replace(actual_start_date_str, new_start_date_str)
        new_sql_file_path = f'./sql/kpis_produtos_pacto/{file_path}_{index+1}.sql'
        with open(new_sql_file_path, 'w') as file:
            file.write(new_sql_content)

    else:
        print(f"The date range is less than {days} days.")
        print(f"Start date: {start_date}")
        print(f"End date: {end_date}")

    return False


if __name__ == '__main__':
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='Update the date interval in a SQL file.')
    parser.add_argument('--file_path', '-f', type=str, help='The path to the SQL file.')
    parser.add_argument('--days', '-d', type=int, help='The number of days to add to the date interval.')
    parser.add_argument('--future_days', '-fd', type=int, help='The number of days to add to the date interval.')
    parser.add_argument('--iterative', '-i', action='store_true', help='Update the date interval iteratively.')
    args = parser.parse_args()

    if not any(vars(args).values()):
        parser.print_help()

    if args.iterative:
        if not args.future_days:
            stop = False
            while not stop:
                stop = update_date_interval(args.file_path, args.days)
        else:
            stop = False
            while not stop:
                stop = update_date_interval(args.file_path, args.days, args.future_days)
        sys.exit(1)

    elif not args.future_days:
        update_date_interval(args.file_path, args.days)
    else:
        update_date_interval(args.file_path, args.days, args.future_days)