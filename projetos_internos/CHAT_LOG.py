from gitlab import Gitlab
import pandas as pd
from json import dumps
from gitlab.exceptions import GitlabAuthenticationError, GitlabGetError
from httplib2 import Http
import re
import time

# keys
gitlab_url = "https://gitlab.com"
access_token = "**************************"
project_path = "Plataformazw/pacto-pydata-eng"

# ----------------------------------------------------------------------------------------------------------------------

def main(massage):
    url = "https://chat.googleapis.com/v1/spaces/AAAA9LftAb4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=30mn-zRxXmcApdeLdFSVMD0U2eFp6Mri7IN3kPhXUyM"
    app_message = {"text": massage}
    message_headers = {"Content-Type": "application/json; charset=UTF-8"}
    http_obj = Http()
    response = http_obj.request(
        uri=url,
        method="POST",
        headers=message_headers,
        body=dumps(app_message),
    )
    print(response)

# ----------------------------------------------------------------------------------------------------------------------

def last_job(gitlab_url, access_token, project_path):
    """
    Retrieve information about the last job executed in a GitLab pipeline.

    Parameters:
        gitlab_url (str): URL of the GitLab instance.
        access_token (str): Personal access token for GitLab API.
        project_path (str): Path of the project in the format 'owner/project'.
    Returns:
        pd.DataFrame: DataFrame containing information about the last job,
                      or None if no pipelines are found.
    """
    try:
        # Connecting to GitLab
        gl = Gitlab(gitlab_url, private_token=access_token)

        # Obtaining the latest pipeline
        project = gl.projects.get(project_path)
        latest_pipeline = project.pipelines.list(order_by='id', sort='desc', per_page=1, get_all=False)[0]

        if not latest_pipeline:
            print("No pipelines found.")
            return None

        # Retrieving job details for the latest pipeline
        latest_job = latest_pipeline.jobs.list()[0]
        full_job = gl.projects.get(project_path).jobs.get(latest_job.id)

        # Getting user information
        user = gl.users.get(full_job.user['id'])
        user_email = user.username

        job_info = {
            'Pipeline_ID': latest_pipeline.id,
            'Pipeline_Date': latest_pipeline.created_at,
            'Job_ID': full_job.id,
            'Job_Date': full_job.created_at,
            'Job_Name': full_job.name,
            'Job_Logs': full_job.trace(),
            'Job_Status': full_job.status,
            'User_Email': user_email
        }

        return pd.DataFrame([job_info])

    except GitlabAuthenticationError:
        print("Authentication failed. Please check your access token.")
        return None
    except GitlabGetError as e:
        print(f"Error occurred while fetching data from GitLab: {e}")
        return None

# result = last_job(gitlab_url, access_token, project_path)
# print(result)

# ----------------------------------------------------------------------------------------------------------------------
# extract from id pipeline & project...

def pipeline_job(pipeline_id):
    # Connecting to GitLab
    gl = Gitlab(gitlab_url, private_token=access_token)

    # Obtaining the pipeline
    pipeline = gl.projects.get("Plataformazw/pacto-pydata-eng").pipelines.get(pipeline_id)

    job_info = {
        'Pipeline_ID': [],
        'Pipeline_Date': [],
        'Job_ID': [],
        'Job_Date': [],
        'Job_Name': [],
        'Job_Logs': [],
        'Job_Status': [],
    }

    # Iterating over the jobs in the pipeline
    for job in pipeline.jobs.list():
        full_job = gl.projects.get("Plataformazw/pacto-pydata-eng").jobs.get(job.id)

        job_info['Pipeline_ID'].append(pipeline.id)
        job_info['Pipeline_Date'].append(pipeline.created_at)
        job_info['Job_ID'].append(full_job.id)
        job_info['Job_Date'].append(full_job.created_at)
        job_info['Job_Name'].append(full_job.name)
        job_info['Job_Logs'].append(full_job.trace())
        job_info['Job_Status'].append(full_job.status)

    df = pd.DataFrame(job_info)
    return df


# run ..

# pipeline_id = "1137544558"  # ID da pipeline que você deseja analisar
#
# job_info = pipeline_job(pipeline_id)
# print(job_info)

# ----------------------------------------------------------------------------------------------------------------------

# job = last_job(gitlab_url, access_token, project_path)
# job = pipeline_job(1161149341)
job["Job_Logs"] = job["Job_Logs"].str.decode('utf-8')

#-----------------------------------------------------------------------------
# PEGA ZONAS MOVIMENTADAS...
padrao_regex = re.compile(r'={29}\s*Uploading...(.*?)={29}', re.DOTALL)
correspondencias = job["Job_Logs"].str.extractall(padrao_regex)
values = correspondencias.values

#-----------------------------------------------------------------------------
# PEGA ERROR MASSAGE...
padrao_regex = re.compile(r'(Traceback.*?)$', re.MULTILINE | re.DOTALL)
correspondencias = job["Job_Logs"].str.extract(padrao_regex)

# ----------------------------------------------------------------------------------------------------------------------

# if job["Job_Status"] == "failed":
message = (f"⚠️ **PIPELINE FAILED** ⚠️\n\n👤 Responsável: @{job['User_Email'][0]}\n🔴 **STATUS:** {job['Job_Status'][0]}\n📅 DATA DA PIPELINE: {job['Pipeline_Date'][0]}\n📋 PROJECT: {job['Job_Name'][0]}\n❌ **ERROR:**\n{correspondencias}\n🌐 ZONAS:\n{values}")

# print(message)
# main(message)
