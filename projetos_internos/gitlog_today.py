
import sys
sys.path.insert(3, '/usr/src/app')

# from google.cloud.exceptions import NotFound
from google.oauth2 import service_account
from google.cloud import bigquery
from termcolor import colored
import pandas_gbq
import datetime
import requests
import json
import os
import pytz
import gitlab 
# ------------------
# PROJECT DEPENDENCE...
import pandas as pd
from gitlab import Gitlab
from datetime import datetime

# ----------------------------------------------------------------------------------------------------------------------
# KEYS TO GIT-LAB...

gitlab_url = "https://gitlab.com"
access_token = "**************************"
project_path = "Plataformazw/pacto-pydata-eng"

# --------------------------------------------
# KEYS TO GCP...

LGN=os.environ.get('LGN_KEY')
PROJECT_ID = "oamd-e-financeiro-pacto"
DATASET_ID = "GIT_LOG"
TABLE_ID = "HIST_LOG"



# ---------------------------------------------------------------------------------------------------------------------
# EXTRACT ALL TODAY'S JOBS...


def all_jobs():
    gitlab = Gitlab(gitlab_url, private_token=access_token)

    project = gitlab.projects.get(project_path, lazy=True)
    project_id = project.id

    print(f"ID do Projeto '{project_path}': {project_id}")

    pipelines = project.pipelines.list(get_all=True)
    today = datetime.now().date()

    data = {
        'Pipeline_ID': [],
        'Pipeline_Date': [],
        'Job_ID': [],
        'Job_Date': [],
        'Job_Name': [],
        'Job_Logs': [],  # Como lidar com os registros de logs depende da estrutura e do tamanho dos dados.
        'Job_Status': [],
    }

    for pipeline in pipelines:
        pipeline_date = datetime.strptime(pipeline.created_at, '%Y-%m-%dT%H:%M:%S.%fZ').date()

        if pipeline_date == today:
            jobs = pipeline.jobs.list(get_all=True)

            for job in jobs:
                full_job = project.jobs.get(job.id)
                job_date = datetime.strptime(full_job.created_at, '%Y-%m-%dT%H:%M:%S.%fZ').date()

                if job_date == today:
                    data['Pipeline_ID'].append(pipeline.id)
                    data['Pipeline_Date'].append(str(pipeline_date))  # Convertendo para string
                    data['Job_ID'].append(full_job.id)
                    data['Job_Date'].append(str(job_date))  # Convertendo para string
                    data['Job_Name'].append(full_job.name)

                    # Tratamento dos logs: você pode querer truncá-los ou armazená-los de maneira diferente, dependendo dos requisitos.
                    job_logs = full_job.trace()
                    # Aqui, você pode fazer o que for necessário com os logs, como truncá-los ou converter para um formato compatível com o BigQuery.
                    data['Job_Logs'].append(job_logs)

                    data['Job_Status'].append(full_job.status)

    df = pd.DataFrame(data)
    return df


df = all_jobs()

# --------------------------------------------------------------------------------------------------------------------
# UPLOAD TO BIGQUERY...
from pandas_gbq import gbq

# credentials = service_account.Credentials.from_service_account_file("Gitloge251ccf10f2b.json")
credentials = service_account.Credentials.from_service_account_info(
    *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
)
destination_table = "git_log.logs"
project_id = "pacto-datalake-dev"

# Tente enviar o DataFrame para o BigQuery
try:
    # Envie o DataFrame para o BigQuery, criando o conjunto de dados e a tabela se não existirem
    gbq.to_gbq(df, destination_table, if_exists="append", credentials=credentials)

except Exception as e:
    print("Erro ao enviar dados para o BigQuery:", e)
