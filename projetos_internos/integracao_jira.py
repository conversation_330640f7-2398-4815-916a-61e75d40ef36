# -*- coding: utf-8 -*-
# Descrição: Script para fazer requisições na API do Jira, filtrar os dados e enviar ao BQ.
import os
import sys
import argparse
import requests
from requests.auth import HTTPBasicAuth
from pandas import DataFrame, Timestamp
import pandas_gbq
from google.oauth2 import service_account

sys.path.insert(3, '/usr/src/app')

def print_log(message, color='white'):
    """Printa os logs"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'white': '\033[0m'
    }
    print(f"{Timestamp.now()} - {colors.get(color)}{message}{colors['white']}")

class JiraData:
    """Classe para lidar com os dados do Jira."""
    def __init__(self, table_name, jql, columns: list) -> None:
        self.url = "https://pacto.atlassian.net/rest/api/3/search"
        self.auth = HTTPBasicAuth(
            "<EMAIL>",
            "ATATT3xFfGF0GwJ3l7KzpknOaB-Qh_BYDV7jnlLTRjhf8hbz8UQOLnpwPlElodSMCH6IVPTs8E4eYsHOAw8qMhWVjMlcWuzIYMbIzksom4Us3jwPRuxo35-YYzraXWmT4RzTczoTVYj-YYsBynsm9ZET3gDDnbKshF2b-6blxeMpnsWfERGhH3o=9FA04069"
            )
        self.headers = {
            "Accept": "application/json"
        }
        self.query = {
            'jql': jql
        }
        self.columns = columns
        self.issues = None
        self.table_name = table_name

    def translate_columns(self):
        """Traduz os índices das colunas para os nomes das colunas."""
        column_map = {
            0: 'issuekey',
            1: 'customfield_10099',
            2: 'customfield_11117',
            3: 'customfield_11118',
            4: 'reporter',
            5: 'assignee',
            6: 'created',
            7: 'issuetype',
            8: 'timeoriginalestimate',
            9: 'priority',
            10: 'project',
            11: 'resolutiondate',
            12: 'status',
            13: 'aggregatetimespent',
            14: 'workratio',
            15: 'endDate',
            16: 'progress',
            17: 'parent',
            18: 'customfield_11116',
            19: 'description',
            20: 'customfield_10069',
            21: 'summary',
            22: 'aggregatetimeoriginalestimate',
            23: 'aggregatetimeestimate',
            24: 'duedate',
            25: 'startDate',
            26: 'statusCategory',
            27: 'nomeempresa',
            28: 'customfield_10020'
        }
        self.columns = [column_map.get(col, None) for col in self.columns if col in column_map]
        print_log(f"Colunas selecionadas: {', '.join(self.columns)}")

    def process_issues(self):
        """Processa as issues e envia ao BQ."""
        self.issues = self.get_all_issues()
        filtered_issues = self.filter_issues()
        project_id = "oamd-e-financeiro-pacto"
        dataset_id = "dados_jira"
        credentials = service_account.Credentials.from_service_account_info(
        {
            "type": "service_account",
            "project_id": project_id,
            "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
            "private_key": os.environ.get('KEY_GCP_DATALAKE').replace('\\n', '\n'), 
            "client_email": "<EMAIL>",
            "client_id": "117321030275837789997",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
        })
        pandas_gbq.to_gbq(
            dataframe=filtered_issues,
            destination_table=f"{dataset_id}.{self.table_name}",
            project_id=project_id,
            credentials=credentials,
            if_exists="replace",
            location="southamerica-east1"
            )


    def get_number_of_issues(self):
        """Busca a quantidade de issues retornadas pela query."""
        self.query['maxResults'] = 0
        response = requests.request(
            "GET",
            self.url,
            headers=self.headers,
            params=self.query,
            auth=self.auth,
            timeout=60
        )
        return int(response.json()['total'])

    def get_all_issues(self):
        """Faz requisições para obter todas as issues da query."""
        number_of_issues = self.get_number_of_issues()
        print_log(f"{number_of_issues} issues encontradas.", "green")
        number_of_requests = number_of_issues // 100 + 1
        issues = []

        for i in range(number_of_requests):
            self.query['maxResults'] = 100
            self.query['startAt'] = i * 100
            response = requests.request(
                "GET",
                self.url,
                headers=self.headers,
                params=self.query,
                auth=self.auth,
                timeout=60
            )
            issues.extend(response.json()['issues'])

        return {'issues': issues}


    def filter_issues(self):
        """Filtra as issues de acordo com as colunas selecionadas."""
        filtered_issues = []

        for issue in self.issues['issues']:
            this_issue = {}
            fields: dict = issue['fields']

            try:
                if 'customfield_10099' in self.columns:
                    customfield_10099 = fields.get('customfield_10099')
                    this_issue['Category'] = customfield_10099.get(
                        'value',
                        'None'
                        ) if customfield_10099 else 'None'

                if 'customfield_11117' in self.columns:
                    customfield_11117 = fields.get('customfield_11117')
                    this_issue['Desenvolvedores'] = ', '.join(
                        [dev['displayName'] for dev in customfield_11117]
                        ) if customfield_11117 else 'None'

                if 'customfield_11118' in self.columns:
                    customfield_11118 = fields.get('customfield_11118')
                    this_issue['QAs'] = ', '.join(
                        [qa['displayName'] for qa in customfield_11118]
                        ) if customfield_11118 else 'None'

                if 'reporter' in self.columns:
                    reporter = fields.get('reporter')
                    this_issue['Reporter'] = reporter.get(
                        'displayName',
                        'None'
                        ) if reporter else 'None'

                if 'assignee' in self.columns:
                    assignee = fields.get('assignee')
                    this_issue['Assignee'] = assignee.get(
                        'displayName',
                        'None'
                        ) if assignee else 'None'

                if 'created' in self.columns:
                    created = fields.get('created')
                    this_issue['Created'] = Timestamp(created) if created else None

                if 'issuetype' in self.columns:
                    issuetype = fields.get('issuetype')
                    this_issue['Type'] = issuetype.get('name', 'None') if issuetype else 'None'

                if 'timeoriginalestimate' in self.columns:
                    this_issue['Estimate'] = fields.get('timeoriginalestimate', 0.0)

                if 'priority' in self.columns:
                    priority = fields.get('priority')
                    this_issue['Priority'] = priority.get('name', 'None') if priority else 'None'

                if 'project' in self.columns:
                    project = fields.get('project')
                    this_issue['ProjectName'] = project.get('name', 'None') if project else 'None'

                if 'resolutiondate' in self.columns:
                    resolutiondate = fields.get('resolutiondate')
                    this_issue['Resolved'] = Timestamp(resolutiondate) if resolutiondate else None

                if 'status' in self.columns:
                    status = fields.get('status')
                    this_issue['Status'] = status.get('name', 'None') if status else 'None'

                if 'aggregatetimespent' in self.columns:
                    this_issue['TimeSpent'] = fields.get('aggregatetimespent', 0.0)

                if 'workratio' in self.columns:
                    this_issue['WorkRatio'] = fields.get('workratio', 0)

                if 'endDate' in self.columns:
                    end_date = fields.get('customfield_10052')
                    this_issue['EndDate'] = end_date if end_date else 'None'

                if 'progress' in self.columns:
                    progress = fields.get('progress')
                    this_issue['Progress'] = progress.get('percent', 0) / 100 if progress else 0.0

                if 'parent' in self.columns:
                    parent = fields.get('parent')
                    this_issue['Parent'] = parent.get('key', 'None') if parent else 'None'

                if 'customfield_11116' in self.columns:
                    customfield_11116 = fields.get('customfield_11116')
                    this_issue['DiasAtraso'] = customfield_11116 if customfield_11116 else None

                if 'customfield_10069' in self.columns:
                    customfield_10069 = fields.get('customfield_10069')
                    this_issue['FailedTests'] = customfield_10069 if customfield_10069 else 'None'

                if 'description' in self.columns:
                    try:
                        description = fields.get('description')
                        this_issue['Description'] = self.process_description(
                            description
                            ) if description else 'None'
                    except Exception as e:
                        print_log(
                            f'Erro ao processar descrição da issue {issue["key"]}: {e}',
                            'red'
                            )
                        this_issue['Description'] = 'None'

                if 'summary' in self.columns:
                    this_issue['Summary'] = fields.get(
                        'summary',
                        'None'
                        ) if fields else 'None'

                if 'issuekey' in self.columns:
                    this_issue['Key'] = issue['key'] if issue['key'] else 'None'

                if 'aggregatetimeoriginalestimate' in self.columns:
                    this_issue['OriginalEstimate'] = fields.get(
                        'aggregatetimeoriginalestimate',
                        0.0
                        )

                if 'aggregatetimeestimate' in self.columns:
                    this_issue['RemainingEstimate'] = fields.get(
                        'aggregatetimeestimate',
                        0.0
                        )

                if 'duedate' in self.columns:
                    duedate = fields.get('duedate')
                    this_issue['DueDate'] = duedate if duedate else 'None'

                if 'startDate' in self.columns:
                    start_date = fields.get('customfield_10015')
                    this_issue['StartDate'] = start_date if start_date else 'None'

                if 'statusCategory' in self.columns:
                    status_category = fields.get('statusCategory')
                    this_issue['StatusCategory'] = status_category.get(
                        'name',
                        'None'
                        ) if status_category else 'None'

                if 'nomeempresa' in self.columns:
                    nome_empresa = fields.get('customfield_10046')
                    this_issue['NomeEmpresa'] = nome_empresa if nome_empresa else 'None'

                if 'customfield_10020' in self.columns:
                    sprint = ', '.join(
                        [sprint['name'] for sprint in fields.get('customfield_10020')]
                        ) if fields.get('customfield_10020') else 'None'
                    this_issue['Sprint'] = sprint if sprint else 'None'

            except Exception as e:
                print_log(f'Erro ao processar issue {issue["key"]}: {e}', 'red')
                continue

            filtered_issues.append(this_issue)

        if not filtered_issues:
            # Handle case where there are no issues
            print_log('Nenhuma issue filtrada.', 'yellow')
            return DataFrame()

        df = DataFrame(filtered_issues)

        # Ensure all columns have compatible types for Parquet
        fillna_values = {
            'Desenvolvedores': 'None',
            'QAs': 'None',
            'Estimate': 0.0,
            'Resolved': 'None',
            'TimeSpent': 0.0,
            'WorkRatio': 0,
            'EndDate': 'None',
            'DiasAtraso': 0,
            'Description': 'None',
            'FailedTests': 'None',
            'Parent': 'None',
            'OriginalEstimate': 0.0,
            'RemainingEstimate': 0.0,
            'DueDate': 'None',
            'Category': 'None',
            'Reporter': 'None',
            'Assignee': 'None',
            'Type': 'None',
            'Priority': 'None',
            'ProjectName': 'None',
            'Status': 'None',
            'Summary': 'None',
            'Key': 'None',
            'Progress': 0.0,
            'StartDate': 'None',
            'StatusCategory': 'None',
            'NomeEmpresa': 'None',
            'Sprint': 'None'
        }
        df = df.fillna({col: fillna_values.get(col, 'None') for col in df.columns})

        types_map = {
            'Estimate': 'float64',
            'TimeSpent': 'float64',
            'WorkRatio': 'int64',
            'Progress': 'float64',
            'DiasAtraso': 'int64',
            'FailedTests': 'str',
            'Created': 'str',
            'Resolved': 'str',
            'EndDate': 'str',
            'Description': 'str',
            'Category': 'str',
            'Desenvolvedores': 'str',
            'QAs': 'str',
            'Reporter': 'str',
            'Assignee': 'str',
            'Type': 'str',
            'Priority': 'str',
            'ProjectName': 'str',
            'Status': 'str',
            'Parent': 'str',
            'Summary': 'str',
            'Key': 'str',
            'OriginalEstimate': 'float64',
            'RemainingEstimate': 'float64',
            'DueDate': 'str',
            'StartDate': 'str',
            'StatusCategory': 'str',
            'NomeEmpresa': 'str',
            'Sprint': 'str'
        }

        df = df.astype({col: types_map.get(col, 'str') for col in df.columns})

        return df

    def process_description(self, description):
        """Processa a descrição da issue, que pode conter formatação em markdown."""
        result = ''
        for part in description['content']:
            if part['type'] == 'text':
                result += part['text']
            elif part['type'] == 'paragraph':
                result += self.process_description(part)
            elif part['type'] == 'hardBreak':
                result += '\n'
            elif 'list' in part['type'].lower():
                for item in part['content']:
                    result += f"- {self.process_description(item)}\n"
            result += '\n'
        return result


if __name__ == '__main__':
    input_table_name = sys.argv[1]
    input_jql = sys.argv[2]
    input_columns = list(map(int, sys.argv[3].split(',')))

    jira_data = JiraData(input_table_name, input_jql, input_columns)
    jira_data.translate_columns()
    jira_data.process_issues()
