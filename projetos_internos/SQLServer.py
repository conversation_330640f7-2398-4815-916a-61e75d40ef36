from google.oauth2 import service_account
from termcolor import colored
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.exceptions import RequestException
from google.cloud import bigquery
from pandas.io import gbq
import pandas as pd
import pandas_gbq
import datetime
import requests
import argparse
import json
import pytz
import os


LGN = os.environ.get("LGN_KEY")
ENDPOINT_BASE = "https://fin.pactosolucoes.com.br/ifinan/updateServlet?"
ENDPOINT_IFINAN_GENERATE = f"{ENDPOINT_BASE}op=encriptar&lgn={LGN}"
ENDPOINT_IFINAN_QUERY = f"{ENDPOINT_BASE}op=selectONE&lgn={LGN}"

PROJECT_ID = "controladoria-446718"
DATASET_ID = "controladoria"
CREDENTIALS = service_account.Credentials.from_service_account_info(
    json.loads(os.environ["KEY_GCP_DATALAKE"])
)

page_size = 5000
max_workers = 15
session = requests.Session()

type_map = {
    "bigint": "Int64",
    "binary": "object",
    "bit": "bool",
    "char": "string",
    "date": "datetime64[ns]",
    "datetime": "datetime64[ns]",
    "datetime2": "datetime64[ns]",
    "datetimeoffset": "string",
    "decimal": "float",
    "float": "float",
    "image": "object",
    "int": "Int64",
    "money": "float",
    "nchar": "string",
    "ntext": "string",
    "numeric": "float",
    "nvarchar": "string",
    "real": "float",
    "smalldatetime": "datetime64[ns]",
    "smallint": "Int64",
    "smallmoney": "float",
    "sql_variant": "string",
    "text": "string",
    "time": "datetime64[ns]",
    "timestamp": "datetime64[ns]",
    "tinyint": "Int64",
    "uniqueidentifier": "string",
    "varbinary": "object",
    "varchar": "string",
    "xml": "string",
}


def post_query(query, database):
    data = {
        "url": f"****************************************/{database}",
        "user": "sa",
        "senha": "pactodb",
        "format": "json",
        "sql": query,
    }
    try:
        r = session.post(ENDPOINT_IFINAN_GENERATE, json=data)
        r.raise_for_status()
        r_json_data = r.json()
        r2 = session.post(
            f'{ENDPOINT_IFINAN_QUERY}&params={r_json_data["sucesso"]}&format=json'
        )
        r2.raise_for_status()
        return json.loads(r2.text)["result"]

    except RequestException as e:
        print(
            colored(
                f"\nErro na requisição: {e}, Resposta: {e.response.text if e.response else 'Sem resposta'}",
                "red",
            )
        )

    except json.JSONDecodeError:
        print(colored(f"\nJSON decode error in response: {r.text}", "red"))


def test_sqlserver_connection(database):
    if post_query("SELECT @@VERSION", database) is not None:
        print(colored("\nConexão bem-sucedida!\n", "green"))
    else:
        print(colored("\nFalha na conexão.\n", "red"))
        exit()


def add_identity_column(table_name, database):
    print(colored("Adicionando order_column...", "yellow"))
    query_add_identity = f"""
        IF OBJECT_ID('{table_name}_temp', 'U') IS NOT NULL
        DROP TABLE {table_name}_temp;

        SELECT IDENTITY(int, 1, 1) AS order_column, *
        INTO {table_name}_temp
        FROM {table_name}
    """
    post_query(query_add_identity, database)
    print(colored("adicionado order_column com sucesso!\n", "green"))


def fetch_sqlserver_schema(table_name, database):
    print(colored("Capturando schema...", "yellow"))
    schema_query = f"""
        SELECT COLUMN_NAME, DATA_TYPE 
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
    """
    result_schema_query = post_query(schema_query, database)
    schema_df = pd.DataFrame(result_schema_query)
    sqlserver_schema = dict(zip(schema_df["COLUMN_NAME"], schema_df["DATA_TYPE"]))
    print(colored("Schema capturado com sucesso!\n", "green"))
    return sqlserver_schema


def calculate_page_numbers(table_name, database, page_size):
    print(colored("Calculando número de páginas...", "yellow"))
    count_query = f"SELECT COUNT(*) as Total FROM {table_name}"
    total_rows = post_query(count_query, database)[0]["Total"]
    total_pages = (total_rows + page_size - 1) // page_size
    print(
        colored(
            f"Número de linhas: {total_rows}\nTotal de páginas: {total_pages}\n",
            "green",
        )
    )
    return total_pages


def stream_pages_to_bq(
    page_size,
    total_pages,
    order_by_column,
    sqlserver_schema,
    query,
    table_name,
    database,
    table_name_bq,
):
    def process_page(page):
        print(colored(f"Page {page} processing...", "yellow"))
        paginated_query = f"{query} ORDER BY {order_by_column} OFFSET {page * page_size} ROWS FETCH NEXT {page_size} ROWS ONLY"
        data_to_send = post_query(paginated_query, database)

        if data_to_send:
            df = pd.DataFrame(data_to_send)
            print(colored(f"Page {page}/{total_pages} processed.", "green"))
            return df
        return pd.DataFrame()

    with ThreadPoolExecutor(max_workers) as executor:
        futures = [executor.submit(process_page, page) for page in range(total_pages)]
        dfs = [future.result() for future in as_completed(futures)]

    merged_df = pd.concat(dfs, ignore_index=True)
    send_to_bigquery(merged_df, DATASET_ID, table_name_bq, sqlserver_schema, "replace")

    print(colored(f"\n`{table_name}` salvo  em `{DATASET_ID} com sucesso!`", "green"))


def send_to_bigquery(
    data, dataset_id, table_id, sqlserver_type_map, if_exists="replace"
):
    df = pd.DataFrame(data)

    for coluna in df.columns:
        type_sqlserver = sqlserver_type_map.get(coluna)
        type_pandas = type_map.get(type_sqlserver, "str")

        if type_pandas == "float":
            df[coluna] = pd.to_numeric(df[coluna], errors="coerce")
        elif type_pandas in ["Int64", "bool"]:
            df[coluna] = pd.to_numeric(df[coluna], errors="coerce").astype(type_pandas)
        elif type_pandas == "datetime":
            df[coluna] = pd.to_datetime(df[coluna], errors="coerce")
        else:
            try:
                df[coluna] = df[coluna].astype(type_pandas)
            except Exception as e:
                print(colored(f"Erro ao converter coluna {coluna}: {e}", "red"))
                df[coluna] = pd.NaT if type_pandas == "datetime" else None

    df.to_gbq(
        f"{dataset_id}.{table_id}",
        project_id=PROJECT_ID,
        credentials=CREDENTIALS,
        if_exists=if_exists,
    )


def register_execution(table_id):
    client = bigquery.Client(credentials=CREDENTIALS, project=PROJECT_ID)
    table_ref = client.dataset(DATASET_ID).table('datas_execucoes')

    try:
        client.get_table(table_ref)

    except:
        print(f"\nA tabela {table_ref.path} não existe. Criando tabela...")

        schema = [
            bigquery.SchemaField("tabela", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("data_ultima_execucao", "TIMESTAMP", mode="REQUIRED"),
            bigquery.SchemaField("data_ultima_execucao_up", "STRING", mode="REQUIRED"),
        ]

        table = bigquery.Table(table_ref, schema=schema)
        client.create_table(table)
        print(f"Tabela {table_ref.path} criada com sucesso!\n")
        
    df = pandas_gbq.read_gbq(f"select * from {DATASET_ID}.datas_execucoes", project_id=PROJECT_ID)
    df.loc[df['tabela'] == table_id, 'data_ultima_execucao'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    df.loc[df['tabela'] == table_id, 'data_ultima_execucao_up'] = datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")       
    
    if not df['tabela'].isin([table_id]).any():
        new_row = {'tabela': table_id, 'data_ultima_execucao': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'data_ultima_execucao_up': datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")}      
        
        if df.empty:
            df = pd.DataFrame(new_row, index=[0])                
        else:                               
            df = pd.concat([df, pd.DataFrame(new_row, index=[0])], ignore_index=True)

    df['data_ultima_execucao'] = pd.to_datetime(df['data_ultima_execucao'], utc=False)
    df.to_gbq(
        f"{DATASET_ID}.datas_execucoes",
        project_id=PROJECT_ID,
        credentials=CREDENTIALS,
        if_exists="replace",
    )
    print(f"\nData e hora '{table_id}' atualizado com sucesso em datas_execucoes", "green")


def main(table_name, database):
    query = f"SELECT * FROM {table_name}_temp"

    print(f"\nEnviando `{table_name}` de `{database}` para o Bigquery...")
    test_sqlserver_connection(database)
    add_identity_column(table_name, database)
    sqlserver_schema = fetch_sqlserver_schema(f"{table_name}_temp", database)
    total_pages = calculate_page_numbers(f"{table_name}_temp", database, page_size)
    order_by_column = "order_column"

    stream_pages_to_bq(
        page_size,
        total_pages,
        order_by_column,
        sqlserver_schema,
        query,
        f"{table_name}_temp",
        database,
        table_name,
    )

    query_drop = f"""
        IF OBJECT_ID('{table_name}_temp', 'U') IS NOT NULL
        DROP TABLE {table_name}_temp;
    """
    post_query(query_drop, database)

    session.close()
    register_execution(table_name)
    print(colored(f"\nConcluído com sucesso!", "green"))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Transferir dados do SQLServer para o BigQuery."
    )
    parser.add_argument(
        "--table_name",
        type=str,
        default="BQ_FINAN_Favorecido_PACTO_WAGI",
        help="Nome da tabela para transferir",
    )
    parser.add_argument(
        "--database", type=str, default="viewsintegracao", help="Nome do banco de dados"
    )

    args = parser.parse_args()

    main(args.table_name, args.database)
