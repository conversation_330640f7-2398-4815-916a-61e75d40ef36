# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(3, '/usr/src/app')

import os
from google.oauth2 import service_account
from google.cloud import bigquery
import pandas as pd
from trello import TrelloClient


class TrelloDataExtractor:
    def __init__(self, API_KEY, API_SECRET, TOKEN, BOARD_ID):
        self.client = TrelloClient(
            API_KEY=API_KEY,
            API_SECRET=API_SECRET,
            TOKEN=TOKEN,
        )
        self.BOARD_ID = BOARD_ID

    @staticmethod
    def lista_para_string(lista):
        return ', '.join(lista)

    def get_board_data(self):
        board = self.client.get_board(self.board_id)
        all_lists = board.open_lists()
        df = pd.DataFrame()

        for list in all_lists:
            for card in list.list_cards():
                if not card.closed:
                    card_data = {
                        'Card Name': [card.name],
                        'List Name': [list.name],
                        'Card Label': [card.labels],
                        'Create Date': [card.created_date],
                        'Last Activity': [card.dateLastActivity],
                        'URL': [card.url],
                        'Card Closed': [card.closed]
                    }

                    for custom_field in card.custom_fields:
                        card_data[custom_field.name] = [custom_field.value]

                    df1 = pd.DataFrame(card_data)
                    df = pd.concat([df, df1], ignore_index=True, sort=False)

        df['Create Date'] = df['Create Date'].dt.strftime('%Y-%m-%d %H:%M:%S')
        df['Last Activity'] = df['Last Activity'].dt.strftime('%Y-%m-%d %H:%M:%S')
        df['Card Label'] = df['Card Label'].apply(lambda x: [item.name for item in x])
        df = df.applymap(lambda x: ' ' if x == [] else x)
        df['Card Label'] = df['Card Label'].apply(self.lista_para_string)

        return df
# API TRELLO
API_KEY = '********************************'
API_SECRET = 'cb90293c86e4eba4716e666e5c468c45853c6eaaf6663bce0ff1b6c191cd5875'
TOKEN = 'ATTAce16241068c532fef0db52b4b4bfe60e1731dbfa865d607fbc3014627efebaa165A4AF1C'

# API BIGQUERY
BOARD_ID = "60f6e7df9cf9848637971b09"
PROJECT_ID = "oamd-e-financeiro-pacto"
DATASET_ID = "trello_south"
CREDENTIALS = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "oamd-e-financeiro-pacto",
        "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
        "private_key": os.getenv("KEY_GCP_DATALAKE"),
        "client_email": "<EMAIL>",
        "client_id": "117321030275837789997",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com",
    }
)
CLIENT = bigquery.Client(project=PROJECT_ID, credentials=CREDENTIALS)



if __name__ == "__main__":
    extractor = TrelloDataExtractor(API_KEY, API_SECRET, TOKEN, BOARD_ID)
    df = extractor.get_board_data()
    df.to_gbq(
            project_id=PROJECT_ID,
            destination_table=DATASET_ID + ".trello_data",
            if_exists="replace",
            credentials=CREDENTIALS,
        )
    
