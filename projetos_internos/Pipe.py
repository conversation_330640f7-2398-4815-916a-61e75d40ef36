# **************************
# -*- coding: utf-8 -*-
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab_BR import *
import requests
import pandas as pd
from google.cloud import bigquery
from google.oauth2 import service_account

# Configurações de acesso ao GitLab
ACCESS_TOKEN = '**************************'
GITLAB_API_URL = 'https://gitlab.com/api/v4'
PROJECT_NAME = 'Plataformazw/pacto-pydata-eng'
dataset_id = "log"
table_id = 'log_new'

# Arquivo CSV onde os dados serão salvos temporariamente
FINAL_CSV = "gitlab_pipelines_data.csv"

def get_project_id(project_name):
    """Obtém o ID de um projeto pelo seu nome."""
    headers = {'Private-Token': ACCESS_TOKEN}
    url = f"{GITLAB_API_URL}/projects/{requests.utils.quote(project_name, safe='')}"
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()['id']
    else:
        raise Exception("Não foi possível encontrar o projeto ou acessar a API do GitLab.")

def extract_data(project_id):
    """Extrai dados de pipelines e jobs do GitLab."""
    headers = {'Private-Token': ACCESS_TOKEN}
    pipelines_url = f"{GITLAB_API_URL}/projects/{project_id}/pipelines"
    pipelines_response = requests.get(pipelines_url, headers=headers).json()
    data = []
    for pipeline in pipelines_response:
        jobs_url = f"{GITLAB_API_URL}/projects/{project_id}/pipelines/{pipeline['id']}/jobs"
        jobs_response = requests.get(jobs_url, headers=headers).json()
        for job in jobs_response:
            data.append({
                'Pipeline ID': pipeline['id'],
                'Pipeline Status': pipeline['status'],
                'Job ID': job['id'],
                'Job Status': job['status'],
                'Job Name': job['name'],
                'Job Started At': job['started_at'],
                'Job Finished At': job['finished_at'],
                'Job Duration': job['duration']
            })
    return data

def load_to_bigquery(data):
    """Carrega dados no BigQuery."""
    df = pd.DataFrame(data)
    df.to_csv(FINAL_CSV, index=False)  # Salvar em CSV
    df = pd.read_csv(FINAL_CSV, parse_dates=['Job Started At', 'Job Finished At'])
    table_ref = f"{dataset_id}.{table_id}"
    job = client.load_table_from_dataframe(df, table_ref)
    job.result()  # Aguarda a conclusão do job
    print("Dados carregados com sucesso no BigQuery.")

# Execução
colab = Colab()
project_id = get_project_id(PROJECT_NAME)
data = extract_data(project_id)

df = pd.read_csv(data, delimiter=";")
colab.upload_to_gbq(df, TABLE_ID)


