# -*- coding: utf-8 -*-
# Descrição: Script para fazer requisições na API do Jira e salvar os dados em um arquivo json. Depois, filtrar os dados e salvar em um arquivo csv.
import requests
from requests.auth import HTTPBasicAuth
from pandas import DataFrame, Timestamp

import sys
sys.path.insert(3, '/usr/src/app')

from extras.Util import *


# Essa função retorna o número total de issues que atendem a query
def get_number_of_issues(url, headers, query, auth):
  query['maxResults'] = 0
  response = requests.request(
    "GET",
    url,
    headers=headers,
    params=query,
    auth=auth
  )
  return int(response.json()['total'])


# Essa função retorna todas as issues que atendem a query
def get_all_issues(url, headers, query, auth, number_of_issues: int):
  print(f"{number_of_issues} issues encontradas.")
  # O número máximo de resultados permitidos pela API do Jira é 100, então precisamos fazer múltiplas solicitações com base no número de problemas.
  number_of_requests = number_of_issues // 100 + 1
  issues = []

  for i in range(number_of_requests):
    query['maxResults'] = number_of_issues
    query['startAt'] = i * 100
    response = requests.request(
      "GET",
      url,
      headers=headers,
      params=query,
      auth=auth
    )
    issues.append(response.json())

  issues = {'issues': [issue for response in issues for issue in response['issues']]}
  return issues


# Essa função filtra as issues e salva em um arquivo csv
def filter_issues(issues):
  filtered_issues = []

  for issue in issues['issues']:
      this_issue = {}

      try:
        if issue['fields']['customfield_10099'] is None:
          issue['fields']['customfield_10099'] = {'value': 'None'}
      except Exception as e:
        print(f'\n________________\nError: {e}\n________________\n')
        print("Criando a coluna Categoria e setando valor 'None' para os campos vazios.")
        issue['fields']['customfield_10099'] = {'value': 'None'}

      try:                
        if issue['fields']['customfield_11117'] is None:
          issue['fields']['customfield_11117'] = {'displayName': 'None'}
        else:
          for i, dev in enumerate(issue['fields']['customfield_11117']):
            this_issue[f'Desenvolvedor{i+1}'] = dev['displayName']

        if issue['fields']['customfield_11118'] is None:
          issue['fields']['customfield_11118'] = {'displayName': 'None'}
        else:
          for i, qa in enumerate(issue['fields']['customfield_11118']):
            this_issue[f'QA{i+1}'] = qa['displayName']

        this_issue['Reporter'] = issue['fields']['reporter']['displayName']
        this_issue['Assignee'] = issue['fields']['assignee']['displayName']
        this_issue['Created'] = Timestamp(issue['fields']['created'])
        this_issue['Category'] = issue['fields']['customfield_10099']['value']
        this_issue['Key'] = issue['key']
        this_issue['Type'] = issue['fields']['issuetype']['name']
        this_issue['Estimate'] = issue['fields']['timeoriginalestimate']
        this_issue['Priority'] = issue['fields']['priority']['name']
        this_issue['ProjectName'] = issue['fields']['project']['name']
        this_issue['Resolved'] = Timestamp(issue['fields']['resolutiondate'])
        this_issue['Status'] = issue['fields']['status']['name']
        this_issue['TimeSpent'] = issue['fields']['aggregatetimespent']
        try:
          this_issue['WorkRatio'] = issue['fields']['workratio'] / 100
        except:
          this_issue['WorkRatio'] = None
          
        filtered_issues.append(this_issue)

      except Exception as e:
        print(f'\n________________\nErro de chave: {e}\n________________\n')
        print(f'Erro ao tentar acessar a chave {e} do dicionário. A solução é tratar o erro e setar um valor padrão para a chave.')
        continue
  
  filtered_issues = DataFrame(filtered_issues)

  return filtered_issues


def issues_to_big_query(issues):
  PROJECT_ID="oamd-e-financeiro-pacto"
  DATASET_ID="time_produto_south"
  TABLE_ID="metricas_time_produto"
  credentials = service_account.Credentials.from_service_account_info(
    {
      "type": "service_account",
      "project_id": "{PROJECT_ID}",
      "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
      "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
      "client_email": "<EMAIL>",
      "client_id": "117321030275837789997",
      "auth_uri": "https://accounts.google.com/o/oauth2/auth",
      "token_uri": "https://oauth2.googleapis.com/token",
      "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
      "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
    }
    )
  util = Util(PROJECT_ID, DATASET_ID, chave_rede=None, credentials=credentials, base_path=None, dev=False, extract_context=None)
  util.to_gbq(issues, DATASET_ID, TABLE_ID, PROJECT_ID, credentials)


def main():
  url = "https://pacto.atlassian.net/rest/api/3/search"

  auth = HTTPBasicAuth("<EMAIL>", "ATATT3xFfGF0GwJ3l7KzpknOaB-Qh_BYDV7jnlLTRjhf8hbz8UQOLnpwPlElodSMCH6IVPTs8E4eYsHOAw8qMhWVjMlcWuzIYMbIzksom4Us3jwPRuxo35-YYzraXWmT4RzTczoTVYj-YYsBynsm9ZET3gDDnbKshF2b-6blxeMpnsWfERGhH3o=9FA04069")

  headers = {
    "Accept": "application/json"
  }

  query = {
    'jql': 'created>=startOfMonth(-1) AND resolutiondate<=endOfMonth(-1) AND timespent >= 15m'
  }

  number_of_issues = get_number_of_issues(url, headers, query, auth)

  issues = get_all_issues(url, headers, query, auth, number_of_issues)

  filtered_issues = filter_issues(issues)

  issues_to_big_query(filtered_issues)


if __name__ == "__main__":
  main()
