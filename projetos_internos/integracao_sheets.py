import pandas as pd
import pandas_gbq
import time
from google.oauth2 import service_account
from google.auth.transport.requests import AuthorizedSession
from google.cloud.exceptions import GoogleCloudError
import os

PROJECT_ID = "oamd-e-financeiro-pacto"
DATASET_ID = "dados_sheets"
credentials = service_account.Credentials.from_service_account_info(
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)


sheets_credentials = service_account.Credentials.from_service_account_info(
    *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************,
    scopes=["https://www.googleapis.com/auth/spreadsheets.readonly"]  # Use the appropriate scope
)

# Create an authorized session
authed_session = AuthorizedSession(sheets_credentials)

spreadsheet_id = '1tbwIiwejkayPCASCJhT-BLBnmsF-76NRmbKdIyZlU-w'
SPREADSHEETS_API_V4_BASE_URL = "https://sheets.googleapis.com/v4/spreadsheets"
SPREADSHEET_URL = SPREADSHEETS_API_V4_BASE_URL + f"/{spreadsheet_id}"
SPREADSHEET_VALUES_URL = SPREADSHEETS_API_V4_BASE_URL + "/%s/values/%s" 
# Make the request using the authorized session
response = authed_session.get(SPREADSHEET_URL, params={"includeGridData": "false"})

metadata = response.json()

def get_sheet_values(sheet_name: str) -> dict:   
    response = authed_session.get(SPREADSHEET_VALUES_URL % (spreadsheet_id, f"{sheet_name}!A1:Z"), timeout=600)
    return response.json().get("values", [[]])

def data_to_big_query(df: pd.DataFrame, table_id: str) -> None:

    FULL_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.{table_id}'

    retries = 3
    delay = 5  # Delay in seconds

    for attempt in range(retries):
        try:
            pandas_gbq.to_gbq(dataframe=df, destination_table=f"{DATASET_ID}.{table_id}", project_id=PROJECT_ID, credentials=credentials, if_exists="replace", location="southamerica-east1")
            print(f"Data successfully uploaded to {FULL_TABLE_ID}.")
            break  # Exit the loop if upload is successful
        except GoogleCloudError as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            time.sleep(delay)  # Wait before retrying
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            break


# Inicializa uma lista para armazenar DataFrames
df_list = []

# Itera sobre todas as folhas
for worksheet in metadata["sheets"]:
    sheet_name = worksheet.get("properties", {}).get("title", "")

    # Verifica se o nome da folha contém "Suporte"
    if "Suporte" in sheet_name:
        # Obtém todos os valores da folha
        data = get_sheet_values(sheet_name)
        # Cria um DataFrame e adiciona à lista
        df = pd.DataFrame(data[1:], columns=data[0])  # Assumindo que a primeira linha é o cabeçalho
        df_list.append(df)

    elif sheet_name == "Página1":
        # Obtém todos os valores da folha
        data = get_sheet_values(sheet_name)
        # Transpõe os dados e cria um DataFrame
        df_transposed = pd.DataFrame(list(data)).T  # Cria o DataFrame transposto
        df_transposed.columns = df_transposed.iloc[0]  # Define a primeira linha como cabeçalho
        df_transposed = df_transposed.drop(0).reset_index(drop=True)  # Remove a primeira linha usada como cabeçalho

# Concatena todos os DataFrames na lista em um único DataFrame
combined_df = pd.concat(df_list, ignore_index=True)

combined_df.columns = ["Numero", "AbertoEm", "ChaveJira", "ResolvidoEm", "Origem", "ResponsavelEquipe", "Responsavel", "Assunto", "ClienteCompleto"]
combined_df['AbertoEm'] = pd.to_datetime(combined_df['AbertoEm'], errors='coerce')
combined_df['ResolvidoEm'] = pd.to_datetime(combined_df['ResolvidoEm'], errors='coerce')

if None in df_transposed.columns:
    df_transposed = df_transposed.drop(columns=[None])

df_transposed.columns = [
    "DadosDatas",
    "TotalVisualizacoesCentralAjuda",
    "TotalCliquesInterrogacao",
    "CHAT",
    "MAXGPT",
    "CentralAjuda",
    "LINK",
    "SUPORTE",
    "VERMAIS",
    "VIDEO",
    "DiasUteisNoMes",
    "MediaDiaUtilCentralAjuda",
    "MediaDiaUtilInterrogacao"
]

df_transposed['DadosDatas'] = pd.to_datetime(df_transposed['DadosDatas'], errors='coerce')


columns = [
    "TotalVisualizacoesCentralAjuda",
    "TotalCliquesInterrogacao",
    "CHAT",
    "MAXGPT",
    "CentralAjuda",
    "LINK",
    "SUPORTE",
    "VERMAIS",
    "VIDEO",
    "DiasUteisNoMes",
    "MediaDiaUtilCentralAjuda",
    "MediaDiaUtilInterrogacao"
]
# turn these cols to float
for col in columns:
    df_transposed[col] = df_transposed[col].apply(lambda x: x.replace('.', ''))
    df_transposed[col] = df_transposed[col].apply(lambda x: x.replace(',', '.'))
    df_transposed[col] = df_transposed[col].astype(float)

data_to_big_query(combined_df, "dados_suporte")
data_to_big_query(df_transposed, "dados_utilizacao")
