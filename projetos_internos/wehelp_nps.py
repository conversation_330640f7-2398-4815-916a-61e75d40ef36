# pylint: skip-file
import requests
import os
from datetime import datetime, timedelta
from google.cloud import bigquery
from google.oauth2 import service_account
from math import ceil

client_id = 'NWY5MmNmMzJlNmU5Njk4'
client_secret = 'df68edc7d011ea6a7bb72622c8328cef58d68c38'  
auth_url = 'https://app.wehelpsoftware.com/api/v2/auth/access_token'
survey_url = 'https://app.wehelpsoftware.com/api/v2/survey-responses/{destination}'
destination = 'CUSTOMER'

# Esquema da tabela BigQuery
schema = [
    bigquery.SchemaField("id", "INTEGER"),
    bigquery.Schema<PERSON>ield("send_date", "TIMESTAMP"),
    bigquery.SchemaField("date_reply", "TIMESTAMP"),
    bigquery.SchemaField("evaluation", "STRING"),
    bigquery.<PERSON>hema<PERSON>ield("comment", "STRING"),
    bigquery.<PERSON><PERSON><PERSON><PERSON>("answered_type", "STRING"),
    bigquery.<PERSON><PERSON>("experience_date", "TIMESTAMP"),
    bigquery.<PERSON><PERSON>a<PERSON><PERSON>("person_name", "STRING"),
    bigquery.SchemaField("person_email", "STRING"),
    bigquery.SchemaField("survey_name", "STRING"),
    bigquery.SchemaField("empresa", "STRING"),
    bigquery.SchemaField("chave", "STRING"),
    bigquery.SchemaField("cod_empresa", "STRING"),
    bigquery.SchemaField("rede", "STRING")
]

# Autenticação
def get_access_token(client_id, client_secret):
    response = requests.get(auth_url, params={
        'client_id': client_id,
        'client_secret': client_secret
    }, headers={'Content-Type': 'application/json'})
    
    if response.status_code == 200:
        return response.json()['result']['access_token']
    else:
        raise Exception('Failed to authenticate: {}'.format(response.json()['result']['message']))

# Extrair dados das respostas NPS com paginação e filtros
def get_survey_responses(access_token, destination, start_date, end_date, after_id=None, after_date=None):
    params = {
        'period[start]': start_date,
        'period[end]': end_date
    }
    
    if after_id:
        params['after_id'] = after_id
    if after_date:
        params['after_date'] = after_date
    
    response = requests.get(survey_url.format(destination=destination), headers={
        'Authorization': 'Bearer {}'.format(access_token),
        'Content-Type': 'application/json'
    }, params=params)
    
    if response.status_code == 200:
        return response.json()['result']
    else:
        raise Exception('Failed to fetch survey responses: {}'.format(response.json()['result']['message']))

# Selecionar e formatar os campos desejados
def format_survey_responses(responses):
    formatted_responses = []
    for response in responses:
        try:
            empresa = response.get('custom_fields', {}).get('custom_field_id_342', {}).get('value')
        except:
            empresa = None
        try:
            chave = response.get('custom_fields', {}).get('custom_field_id_344', {}).get('value')
        except:
            chave = None
        try:
            cod_empresa = response.get('custom_fields', {}).get('custom_field_id_345', {}).get('value')
        except:
            cod_empresa = None
        try:
            rede = response.get('custom_fields', {}).get('custom_field_id_490', {}).get('value')
        except:
            rede = None
        formatted_responses.append({
            'id': response.get('id'),
            'send_date': response.get('send_date'),
            'date_reply': response.get('date_reply'),
            'evaluation': response.get('evaluation'),
            'comment': response.get('comment'),
            'answered_type': response.get('answered_type'),
            'experience_date': response.get('experience_date'),
            'person_name': response.get('person', {}).get('name'),
            'person_email': response.get('person', {}).get('email'),
            'survey_name': response.get('survey', {}).get('name'),
            'empresa': empresa,
            'chave': chave,
            'cod_empresa': cod_empresa,
            'rede': rede
        })
    return formatted_responses

# Carregar dados no BigQuery
def load_to_bigquery(data, table_id, schema):
    credentials = service_account.Credentials.from_service_account_info({
        "type": "service_account",
        "project_id": "dadosmercado-415119",
        "private_key_id": "1ad17cf6a3324b511b5e936be7b587f2b3471595",
        "private_key": os.environ.get("KEY_GCP_DATALAKE").replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "104771537170357748391",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/dados-mercado%40dadosmercado-415119.iam.gserviceaccount.com"
    })

    client = bigquery.Client(credentials=credentials, project=credentials.project_id)
    
    dataset_id, table_name = table_id.rsplit('.')
    dataset_ref = client.dataset(dataset_id)
    table_ref = dataset_ref.table(table_name)

    try:
        client.get_table(table_ref)
    except Exception:
        table = bigquery.Table(table_ref, schema=schema)
        client.create_table(table)
        print(f"Table {table_id} created.")

    if not data:
        print("No survey responses to load into BigQuery.")
        return

    CHUNK_SIZE = 5000
    total_chunks = ceil(len(data) / CHUNK_SIZE)

    for i in range(total_chunks):
        chunk = data[i * CHUNK_SIZE:(i + 1) * CHUNK_SIZE]
        print(f"📦 Enviando chunk {i + 1}/{total_chunks} com {len(chunk)} registros...")

        job_config = bigquery.LoadJobConfig()
        job_config.schema = schema
        job_config.write_disposition = "WRITE_TRUNCATE" if i == 0 else "WRITE_APPEND"
        job_config.ignore_unknown_values = True

        try:
            job = client.load_table_from_json(chunk, table_ref, job_config=job_config)
            job.result()  # Espera finalização
        except Exception as e:
                print(f"❌ Erro ao carregar chunk {i + 1}: {e}")
                raise

    print(f"✅ Todos os {len(data)} registros foram carregados com sucesso para {table_id}")

def main():
    try:
        access_token = get_access_token(client_id, client_secret)
        all_survey_responses = []
        after_id = None
        after_date = None

        while True:
            final_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            response_data = get_survey_responses(
                access_token,
                destination,
                '2024-01-01',
                final_date,
                after_id,
                after_date
                )
            survey_responses = response_data['survey_responses']
            if not survey_responses:
                break

            formatted_responses = format_survey_responses(survey_responses)
            all_survey_responses.extend(formatted_responses)

            # Atualizar after_id e after_date para a próxima página
            after_id = response_data['pagination'].get('after_id')
            after_date = response_data['pagination'].get('after_date')

            if not after_id or not after_date:
                break

        # Carregue as respostas no BigQuery
        table_id = 'sistema_pacto_geral.nps_responses'
        load_to_bigquery(all_survey_responses, table_id, schema)
        print(f'{len(all_survey_responses)} survey responses loaded into BigQuery.')

    except Exception as e:
        print(e)

if __name__ == '__main__':
    main()
