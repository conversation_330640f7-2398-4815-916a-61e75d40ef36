import asyncio
from collections import defaultdict
import aiohttp
import pandas as pd
import time

async def get_url_async(session: aiohttp.ClientSession, chave: str) -> str:
    """
    Busca a URL de discovery de forma assíncrona.
    """
    api_url = f'https://discovery.ms.pactosolucoes.com.br/find/{chave}'
    async with session.get(api_url) as resp:
        if resp.status == 200:
            json_data = await resp.json()
            return json_data.get('content', {}).get('serviceUrls', {}).get('zwUrl', '')
        return ''

async def get_comissao_consultor_async(
    session: aiohttp.ClientSession,
    semaphore: asyncio.Semaphore,
    error_summary: dict,
    chave: str,
    data_inicio_ts: pd.Timestamp,
    data_fim: pd.Timestamp,
    retries: int = 3,
    wait_time: int = 2,
    request_delay: float = 0.5  # Delay entre requisições
) -> list:
    """
    Consulta o relatório de comissão para consultor de forma assíncrona com limitação de threads,
    coleta de erros, tentativas de repetição e delay entre requisições.
    """
    async with semaphore:
        base_url = await get_url_async(session, chave)
        if not base_url:
            error_summary[chave] += 1
            print(f"Erro: Não foi possível obter a URL base para a chave {chave}")
            return []

        url = f"{base_url}/prest/relatorios/comissao-consultor"
        all_rows = []

        # Ajustar para consultar por trimestre em vez de mês a mês
        dias = pd.date_range(start=data_inicio_ts, end=data_fim, freq='QS')  # Início de cada trimestre
        dias = dias.union(pd.DatetimeIndex([data_fim]))

        for i, dia in enumerate(dias):
            data_inicio = dia.strftime('%d/%m/%Y')
            # Definir o fim do período como o último dia do trimestre
            if i < len(dias) - 1:
                data_fim_mes = (dias[i + 1] - pd.Timedelta(days=1)).strftime('%d/%m/%Y')
            else:
                data_fim_mes = data_fim.strftime('%d/%m/%Y')

            querystring = {
                "chave": chave,
                "dataInicioLancamento": data_inicio,
                "dataFimLancamento": data_fim_mes,
                "codigoEmpresa": "1",
                "tipoRelatorio": "COMPETENCIA",
                "dataCompetencia": data_inicio
            }

            headers = {"Authorization": f"Bearer {chave}"}

            attempt = 0
            while attempt <= retries:
                try:
                    async with session.get(
                        url,
                        headers=headers,
                        params=querystring,
                        timeout=aiohttp.ClientTimeout(total=60)  # Aumentar timeout para 60 segundos
                    ) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            data = data.get('data', {}).get('data', [])
                            for item in data:
                                if not isinstance(item, dict):
                                    continue
                                all_rows.append({
                                    'dia': dia,
                                    'chave': chave,
                                    'formaPagamento': item.get('formaPagamento', None),
                                    'codigoResponsavelRecebimento': item.get('codigoResponsavelRecebimento', None),
                                    'qtdParcelas': item.get('qtdParcelas', None),
                                    'nomePlano': item.get('nomePlano', None),
                                    'consultorResponsavel': item.get('consultorResponsavel', None),
                                    'valorContrato': item.get('valorContrato', None),
                                    'dataCompensacao': item.get('dataCompensacao', None),
                                    'situacaoContrato': item.get('situacaoContrato', None),
                                    'valorDaComissao': item.get('valorDaComissao', None),
                                    'tipoContrato': item.get('tipoContrato', None),
                                    'nomePessoa': item.get('nomePessoa', None),
                                    'codigoContrato': item.get('codigoContrato', None),
                                    'codigoResponsavelLancamento': item.get('codigoResponsavelLancamento', None),
                                    'codigoConsultorResponsavel': item.get('codigoConsultorResponsavel', None),
                                    'dataPagamento': item.get('dataPagamento', None),
                                    'responsavelRecebimento': item.get('responsavelRecebimento', None),
                                    'valor': item.get('valor', None),
                                    'configuracao': item.get('configuracao', None),
                                    'codigoRecibo': item.get('codigoRecibo', None),
                                    'valorProduto': item.get('valorProduto', None),
                                    'responsavelLancamento': item.get('responsavelLancamento', None),
                                    'contratoAgendadoEspontaneo': item.get('contratoAgendadoEspontaneo', None),
                                    'codigoPessoa': item.get('codigoPessoa', None),
                                    'matriculaCliente': item.get('matriculaCliente', None),
                                    'duracaoContrato': item.get('duracaoContrato', None),
                                    'codigoCliente': item.get('codigoCliente', None),
                                    'codigoProduto': item.get('codigoProduto', None),
                                    'nomeProduto': item.get('nomeProduto', None),
                                    'produtosPagos': item.get('produtosPagos', None),
                                    'dataLancamentoContrato': item.get('dataLancamentoContrato', None),
                                    'dataCompetencia': item.get('dataCompetencia', None)
                                })
                            break
                        else:
                            error_summary[chave] += 1
                            error_message = await resp.text()
                            print(f"{resp.status} - {chave} - {data_inicio} - {data_fim_mes}")
                            print(f"Resposta da API: {error_message}")
                            if "Dados Não Encontrados" in error_message:
                                break
                            attempt += 1
                            await asyncio.sleep(wait_time)
                except Exception as e:
                    error_summary[chave] += 1
                    print(f"Exceção na tentativa {attempt+1} para chave {chave}: {e}")
                    attempt += 1
                    await asyncio.sleep(wait_time)

            # Adicionar delay entre requisições para evitar sobrecarga
            await asyncio.sleep(request_delay)

        return all_rows

async def generate_comissao_consultor_async(chaves: list) -> pd.DataFrame:
    """
    Cria um DataFrame de forma assíncrona com limitação de threads simultâneas, coleta de erros
    e delay entre requisições.
    """
    dataframe_columns = [
        'dia', 'chave', 'formaPagamento', 'codigoResponsavelRecebimento', 'qtdParcelas', 'nomePlano',
        'consultorResponsavel', 'valorContrato', 'dataCompensacao', 'situacaoContrato',
        'valorDaComissao', 'tipoContrato', 'nomePessoa', 'codigoContrato',
        'codigoResponsavelLancamento', 'codigoConsultorResponsavel', 'dataPagamento',
        'responsavelRecebimento', 'valor', 'configuracao', 'codigoRecibo',
        'valorProduto', 'responsavelLancamento', 'contratoAgendadoEspontaneo',
        'codigoPessoa', 'matriculaCliente', 'duracaoContrato', 'codigoCliente',
        'codigoProduto', 'nomeProduto', 'produtosPagos', 'dataLancamentoContrato', 'dataCompetencia'
    ]

    data_inicio = '01/01/2025'
    data_inicio_ts = pd.to_datetime(data_inicio, format='%d/%m/%Y')
    data_fim = pd.to_datetime('today')

    semaphore = asyncio.Semaphore(2)  # Reduzir para 2 requisições simultâneas
    error_summary = defaultdict(int)

    async with aiohttp.ClientSession() as session:
        tasks = [
            get_comissao_consultor_async(
                session,
                semaphore,
                error_summary,
                chave,
                data_inicio_ts,
                data_fim
            )
            for chave in chaves
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

    print("\nResumo de erros por chave:")
    for chave, count in error_summary.items():
        print(f"Chave: {chave}, Erros: {count}")

    flat_results = [row for sublist in results if isinstance(sublist, list) for row in sublist]

    if flat_results:
        dataframe = pd.DataFrame(flat_results, columns=dataframe_columns)
        if 'dataCompetencia' in dataframe.columns:
            dataframe['dataCompetencia'] = pd.to_datetime(dataframe['dataCompetencia'], errors='coerce').dt.date
    else:
        dataframe = pd.DataFrame(columns=dataframe_columns)
    
    print(f"\nTotal de registros adicionados ao DataFrame: {len(flat_results)}")

    return dataframe

def generate_comissao_consultor(chaves: list) -> pd.DataFrame:
    """
    Função principal que executa a geração do DataFrame de comissões.
    """
    start_time = time.monotonic()
    delay_between_batches = 5  # Delay de 5 segundos entre lotes de chaves

    # Dividir chaves em lotes para evitar muitas requisições simultâneas
    batch_size = 10
    results = []

    for i in range(0, len(chaves), batch_size):
        batch_chaves = chaves[i:i + batch_size]
        print(f"Processando lote de {len(batch_chaves)} chaves...")
        df_batch = asyncio.run(generate_comissao_consultor_async(batch_chaves))
        results.append(df_batch)

        # Adicionar delay entre lotes
        elapsed_time = time.monotonic() - start_time
        if len(chaves) > i + batch_size:  # Apenas se houver mais lotes
            remaining_delay = delay_between_batches - elapsed_time % delay_between_batches
            if remaining_delay > 0:
                print(f"Aguardando {remaining_delay:.2f} segundos antes do próximo lote...")
                time.sleep(remaining_delay)

    # Combinar todos os DataFrames
    if results:
        final_df = pd.concat(results, ignore_index=True)
    else:
        final_df = pd.DataFrame(columns=[
            'dia', 'chave', 'formaPagamento', 'codigoResponsavelRecebimento', 'qtdParcelas', 'nomePlano',
            'consultorResponsavel', 'valorContrato', 'dataCompensacao', 'situacaoContrato',
            'valorDaComissao', 'tipoContrato', 'nomePessoa', 'codigoContrato',
            'codigoResponsavelLancamento', 'codigoConsultorResponsavel', 'dataPagamento',
            'responsavelRecebimento', 'valor', 'configuracao', 'codigoRecibo',
            'valorProduto', 'responsavelLancamento', 'contratoAgendadoEspontaneo',
            'codigoPessoa', 'matriculaCliente', 'duracaoContrato', 'codigoCliente',
            'codigoProduto', 'nomeProduto', 'produtosPagos', 'dataLancamentoContrato', 'dataCompetencia'
        ])

    return final_df

if __name__ == "__main__":
    chaves = ['8cac29a56e4ccd7a6a72a7e5bf432d66']
    df_comissao_consultor = generate_comissao_consultor(chaves)
    print(df_comissao_consultor)
    df_comissao_consultor.to_csv('comissao_consultor.csv', index=False)