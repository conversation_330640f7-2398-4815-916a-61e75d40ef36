import asyncio
from collections import defaultdict
import aiohttp
import pandas as pd
import numpy as np

async def get_url_async(session: aiohttp.ClientSession, chave: str) -> str:
    """
    Busca a URL de discovery de forma assíncrona.
    """
    api_url = f'https://discovery.ms.pactosolucoes.com.br/find/{chave}'
    async with session.get(api_url) as resp:
        if resp.status == 200:
            json_data = await resp.json()
            return (
                json_data
                .get('content', {})
                .get('serviceUrls', {})
                .get('zwUrl', '')
            )
        else:
            return ''

async def get_ticket_medio_async(
    session: aiohttp.ClientSession,
    semaphore: asyncio.Semaphore,
    error_summary: dict,
    chave: str,
    database: str,
    dia: np.datetime64,
    retries: int = 3,
    wait_time: int = 2
) -> list:
    """
    Consulta o ticket médio de forma assíncrona com limitação de threads,
    coleta de erros e tentativas de repetição.
    """
    async with semaphore:
        base_url = await get_url_async(session, chave)
        url = f"{base_url}/prest/ticket-medio"

        mes = database.split('/')[1]
        ano = database.split('/')[2]

        querystring = {
            "chave": chave,
            "mes": mes,
            "ano": ano,
            "database": database,
            "empresa": "1",
            "atualizar": "false"
        }

        headers = {
            "Authorization": f"Bearer {chave}"
        }

        attempt = 0
        while attempt <= retries:
            try:
                async with session.get(url, headers=headers, params=querystring) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        content = data.get('content', {})

                        return [
                            chave,
                            database,
                            dia,
                            content.get('ticketCompetencia', None),
                            content.get('caixaCompetencia', None),
                            content.get('ticketReceita', None),
                            content.get('caixaReceita', None),
                            content.get('despesaPorAluno', None),
                            content.get('despesaTotalMes', None),
                            content.get('pagantes', None),
                            content.get('bolsas', None),
                            content.get('ativos', None)
                        ]
                    else:
                        # Incrementa o contador de erros
                        error_summary[chave] += 1
                        print(f"Erro na tentativa {attempt+1} para chave {chave}: {resp.status}")
                        attempt += 1
                        await asyncio.sleep(wait_time)
            except Exception as e:
                # Incrementa o contador de erros e tenta novamente
                error_summary[chave] += 1
                print(f"Exceção na tentativa {attempt+1} para chave {chave}: {e}")
                attempt += 1
                await asyncio.sleep(wait_time)

        # Se todas as tentativas falharem, retorna valores nulos
        return [chave, database, dia, None, None, None, None, None, None, None, None, None]

async def generate_ticket_medio_async(chaves: list) -> pd.DataFrame:
    """
    Cria um DataFrame de forma assíncrona com limitação de threads simultâneas e coleta de erros.
    """
    dataframe = pd.DataFrame(columns=[
        'chave',
        'datareferencia',
        'dia',
        'ticket_medio_competencia',
        'caixa_competencia',
        'ticket_medio_receita',
        'caixa_receita',
        'despesa_por_aluno',
        'despesa',
        'pagantes',
        'bolsas',
        'ativos'
    ])

    data_inicio = '31/01/2023'
    data_inicio_ts = pd.to_datetime(data_inicio, format='%d/%m/%Y')
    data_fim = pd.to_datetime('today')
    dias = pd.date_range(start=data_inicio_ts, end=data_fim, freq='M')

    if data_fim not in dias:
        dias = dias.append(pd.DatetimeIndex([data_fim]))

    calls = [(chave, dia.strftime('%d/%m/%Y'), dia) for chave in chaves for dia in dias]

    # Cria o semáforo dentro do contexto do loop
    semaphore = asyncio.Semaphore(5)

    # Dicionário para rastrear erros por chave
    error_summary = defaultdict(int)

    async with aiohttp.ClientSession() as session:
        tasks = [
            get_ticket_medio_async(session, semaphore, error_summary, chave, database, dia)
            for (chave, database, dia) in calls
        ]

        results = await asyncio.gather(*tasks)

    # Resumo de erros
    print("\nResumo de erros por chave:")
    for chave, count in error_summary.items():
        print(f"Chave: {chave}, Erros: {count}")

    dataframe = pd.DataFrame(results, columns=[
        'chave',
        'datareferencia',
        'dia',
        'ticket_medio_competencia',
        'caixa_competencia',
        'ticket_medio_receita',
        'caixa_receita',
        'despesa_por_aluno',
        'despesa',
        'pagantes',
        'bolsas',
        'ativos'
    ])

    return dataframe

def generate_ticket_medio(chaves: list) -> pd.DataFrame:
    return asyncio.run(generate_ticket_medio_async(chaves))


# Exemplo de uso:
if __name__ == "__main__":
    chaves = ['d05669da2a7839ee4c4a6011029d7e91', '871763cff107b8a0ef2cf42a9f164c3', '1d40882bc4bd32e41d71d18381755f93', '5c367450890ebad1a8752bbd7060b820', 'f0be27fc6698fedfddc8e2afe89acf63']
    df_ticket_medio = generate_ticket_medio(chaves)
    print(df_ticket_medio)
