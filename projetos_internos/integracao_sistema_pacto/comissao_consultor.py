"""Integração com o relatório de Comissão para Consultor do Sistema Pacto"""
import asyncio
from collections import defaultdict
import aiohttp
import pandas as pd

async def get_url_async(session: aiohttp.ClientSession, chave: str) -> str:
    """
    Busca a URL de discovery de forma assíncrona.
    """
    api_url = f'https://discovery.ms.pactosolucoes.com.br/find/{chave}'
    async with session.get(api_url) as resp:
        if resp.status == 200:
            json_data = await resp.json()
            return (
                json_data
                .get('content', {})
                .get('serviceUrls', {})
                .get('zwUrl', '')
            )
        else:
            return ''

async def get_comissao_consultor_async(
    session: aiohttp.ClientSession,
    semaphore: asyncio.Semaphore,
    error_summary: dict,
    chave: str,
    data_inicio_ts: pd.Timestamp,
    data_fim: pd.Timestamp,
    retries: int = 3,
    wait_time: int = 2
) -> list:
    """
    Consulta o relatório de comissão para consultor de forma assíncrona com limitação de threads,
    coleta de erros e tentativas de repetição.
    """
    async with semaphore:
        base_url = await get_url_async(session, chave)
        url = f"{base_url}/prest/relatorios/comissao-consultor"

        # Gerar o conjunto de datas sem duplicatas
        dias = pd.date_range(start=data_inicio_ts, end=data_fim, freq='MS')
        dias = dias.union(pd.DatetimeIndex([data_fim]))

        all_rows = []

        for dia in dias:
            data_inicio = dia.strftime('%d/%m/%Y')
            data_fim_mes = (
                (dia + pd.DateOffset(months=1)).replace(day=1) - pd.Timedelta(days=1)
            ).strftime('%d/%m/%Y')
            querystring = {
                "chave": chave,
                "dataInicioLancamento": data_inicio,
                "dataFimLancamento": data_fim_mes,
                "codigoEmpresa": "1",
                "tipoRelatorio": "RECEITA"
            }

            headers = {
                "Authorization": f"Bearer {chave}"
            }

            attempt = 0
            while attempt <= retries:
                try:
                    async with session.get(
                        url,
                        headers=headers,
                        params=querystring,
                        timeout=30
                    ) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            if data:
                                data = data.get('data', {}).get('data', [])
                            else:
                                data = []
                            for item in data:
                                if not isinstance(item, dict):
                                    continue
                                # Tratar strings de data diretamente ao coletar os dados
                                data_compensacao = item.get('dataCompensacao', None)
                                data_pagamento = item.get('dataPagamento', None)
                                data_lancamento = item.get('dataLancamentoContrato', None)
                                # Remover 'BRT' das strings de data
                                if data_compensacao and isinstance(data_compensacao, str):
                                    data_compensacao = data_compensacao.replace(' BRT', '')
                                if data_pagamento and isinstance(data_pagamento, str):
                                    data_pagamento = data_pagamento.replace(' BRT', '')
                                if data_lancamento and isinstance(data_lancamento, str):
                                    data_lancamento = data_lancamento.replace(' BRT', '')
                                all_rows.append({
                                    'dia': dia,
                                    'chave': chave,
                                    'formaPagamento': item.get('formaPagamento', None),
                                    'codigoResponsavelRecebimento': item.get('codigoResponsavelRecebimento', None),
                                    'qtdParcelas': item.get('qtdParcelas', None),
                                    'nomePlano': item.get('nomePlano', None),
                                    'consultorResponsavel': item.get('consultorResponsavel', None),
                                    'valorContrato': item.get('valorContrato', None),
                                    'dataCompensacao': data_compensacao,
                                    'situacaoContrato': item.get('situacaoContrato', None),
                                    'valorDaComissao': item.get('valorDaComissao', None),
                                    'tipoContrato': item.get('tipoContrato', None),
                                    'nomePessoa': item.get('nomePessoa', None),
                                    'codigoContrato': item.get('codigoContrato', None),
                                    'codigoResponsavelLancamento': item.get('codigoResponsavelLancamento', None),
                                    'codigoConsultorResponsavel': item.get('codigoConsultorResponsavel', None),
                                    'dataPagamento': data_pagamento,
                                    'responsavelRecebimento': item.get('responsavelRecebimento', None),
                                    'valor': item.get('valor', None),
                                    'configuracao': item.get('configuracao', None),
                                    'codigoRecibo': item.get('codigoRecibo', None),
                                    'valorProduto': item.get('valorProduto', None),
                                    'responsavelLancamento': item.get('responsavelLancamento', None),
                                    'contratoAgendadoEspontaneo': item.get('contratoAgendadoEspontaneo', None),
                                    'codigoPessoa': item.get('codigoPessoa', None),
                                    'matriculaCliente': item.get('matriculaCliente', None),
                                    'duracaoContrato': item.get('duracaoContrato', None),
                                    'codigoCliente': item.get('codigoCliente', None),
                                    'codigoProduto': item.get('codigoProduto', None),
                                    'nomeProduto': item.get('nomeProduto', None),
                                    'produtosPagos': item.get('produtosPagos', None),
                                    'dataLancamentoContrato': data_lancamento
                                })
                            break
                        else:
                            error_summary[chave] += 1
                            error_message = await resp.text()
                            print(f"{resp.status} - {chave} - {data_inicio} - {data_fim_mes}")
                            print(f"Resposta da API: {error_message}")
                            if "Dados Não Encontrados" in error_message:
                                break
                            else:
                                attempt += 1
                                await asyncio.sleep(wait_time)
                except Exception as e:
                    error_summary[chave] += 1
                    print(f"Exceção na tentativa {attempt+1} para chave {chave}: {e}")
                    attempt += 1
                    await asyncio.sleep(wait_time)

        return all_rows

async def generate_comissao_consultor_async(chaves: list) -> pd.DataFrame:
    """
    Cria um DataFrame de forma assíncrona com limitação de threads simultâneas e coleta de erros.
    """
    dataframe_columns = [
        'dia', 'chave', 'formaPagamento', 'codigoResponsavelRecebimento', 'qtdParcelas', 'nomePlano',
        'consultorResponsavel', 'valorContrato', 'dataCompensacao', 'situacaoContrato',
        'valorDaComissao', 'tipoContrato', 'nomePessoa', 'codigoContrato',
        'codigoResponsavelLancamento', 'codigoConsultorResponsavel', 'dataPagamento',
        'responsavelRecebimento', 'valor', 'configuracao', 'codigoRecibo',
        'valorProduto', 'responsavelLancamento', 'contratoAgendadoEspontaneo',
        'codigoPessoa', 'matriculaCliente', 'duracaoContrato', 'codigoCliente',
        'codigoProduto', 'nomeProduto', 'produtosPagos', 'dataLancamentoContrato'
    ]

    data_inicio = '01/01/2025'
    data_inicio_ts = pd.to_datetime(data_inicio, format='%d/%m/%Y')
    data_fim = pd.to_datetime('today')

    semaphore = asyncio.Semaphore(5)
    error_summary = defaultdict(int)

    async with aiohttp.ClientSession() as session:
        tasks = [
            get_comissao_consultor_async(
                session,
                semaphore,
                error_summary,
                chave,
                data_inicio_ts,
                data_fim
            )
            for chave in chaves
        ]
        results = await asyncio.gather(*tasks)

    print("\nResumo de erros por chave:")
    for chave, count in error_summary.items():
        print(f"Chave: {chave}, Erros: {count}")

    flat_results = [row for sublist in results if sublist for row in sublist]

    if flat_results:
        dataframe = pd.DataFrame(flat_results, columns=dataframe_columns)
    else:
        dataframe = pd.DataFrame(columns=dataframe_columns)
    
    print(f"\nTotal de registros adicionados ao DataFrame: {len(flat_results)}")

    # Tratar colunas de data para remover 'BRT' e definir fuso horário
    date_columns = ['dataCompensacao', 'dataPagamento', 'dataLancamentoContrato']
    for col in date_columns:
        if col in dataframe.columns:
            dataframe[col] = dataframe[col].str.replace(r'\sBRT$', '', regex=True)
            dataframe[col] = pd.to_datetime(dataframe[col], errors='coerce')
            dataframe[col] = dataframe[col].dt.tz_localize('America/Sao_Paulo').dt.date

    return dataframe

def generate_comissao_consultor(chaves: list) -> pd.DataFrame:
    return asyncio.run(generate_comissao_consultor_async(chaves))

# Exemplo de uso:
if __name__ == "__main__":
    chaves = ['8cac29a56e4ccd7a6a72a7e5bf432d66']
    df_comissao_consultor = generate_comissao_consultor(chaves)
    
    # Exemplo de criação de dataCompetencia (ajuste conforme seu código real)
    df_comissao_consultor['dataCompetencia'] = df_comissao_consultor['dataCompensacao']
    
    # Tratar dataCompetencia diretamente com tz_localize
    if 'dataCompetencia' in df_comissao_consultor.columns:
        print("Tratando dataCompetencia com tz_localize...")
        df_comissao_consultor['dataCompetencia'] = df_comissao_consultor['dataCompetencia'].str.replace(r'\sBRT$', '', regex=True)
        df_comissao_consultor['dataCompetencia'] = pd.to_datetime(df_comissao_consultor['dataCompetencia'], errors='coerce')
        df_comissao_consultor['dataCompetencia'] = df_comissao_consultor['dataCompetencia'].dt.tz_localize('America/Sao_Paulo').dt.date
    
    print(df_comissao_consultor)
    df_comissao_consultor.to_csv('comissao_consultor.csv', index=False)