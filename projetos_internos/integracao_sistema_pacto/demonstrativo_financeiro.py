# -*- coding: utf-8 -*-
"""
  Módulo: demonstrativo_financeiro.py
  Função pública: generate_demonstrativo_financeiro(chaves: list) -> pd.DataFrame
  Observação: Não grava CSV nem carrega no BigQuery; isso é feito pelo caller.
"""
import asyncio
from collections import defaultdict
from typing import List

import aiohttp
import pandas as pd

# ------------------------------------------------------------------
# Helpers
# ------------------------------------------------------------------
async def get_url_async(session: aiohttp.ClientSession, chave: str) -> str:
    api_url = f"https://discovery.ms.pactosolucoes.com.br/find/{chave}"
    async with session.get(api_url) as resp:
        if resp.status == 200:
            data = await resp.json()
            return data.get("content", {}).get("serviceUrls", {}).get("zwUrl", "")
    return ""

def extract_rows(agrupador: dict, chave: str, competencia_esperada: str) -> List[dict]:
    """Retorna linhas somente do mês/ano da chamada (`competencia_esperada`)."""
    nome_agrupador = agrupador.get("nomeAgrupador")
    rows = []

    for item in agrupador.get("listaLancamentos", []):
        competencia = competencia_esperada
        if competencia != competencia_esperada:
            continue

        rateios = item.get("rateios") or [{}]  # garante pelo menos um dicionário
        for rateio in rateios:
            nome_plano = str(rateio.get("nomePlano"))
            nome_plano_final = nome_plano if nome_plano and nome_plano.strip().lower() != "none" else nome_agrupador
            nome_agrupador_completo = f"{nome_agrupador} > {nome_plano_final}" if nome_plano_final != nome_agrupador else nome_agrupador

            rows.append({
                "chave": str(chave),
                "competencia": competencia_esperada,
                "nome_agrupador": str(nome_agrupador),
                "nome_agrupador_completo": nome_agrupador_completo,
                "descricao_lancamento": str(item.get("descricaoLancamento")),
                "contrato": str(item.get("contrato")),
                "nome_pessoa": str(item.get("nomePessoa")),
                "valor_lancamento": str(item.get("valorLancamento")),
                "rateio_nome_plano": nome_plano,
                "rateio_nome_centro_custo": str(rateio.get("nomeCentroCusto")),
                "rateio_percentual": str(rateio.get("percentagem")),
            })
    return rows

async def get_demonstrativo_financeiro_async(
    session: aiohttp.ClientSession,
    semaphore: asyncio.Semaphore,
    error_summary: dict,
    chave: str,
    dt_inicio: pd.Timestamp,
    dt_fim: pd.Timestamp,
    retries: int = 3,
    wait_time: int = 2,
) -> List[dict]:
    async with semaphore:
        base_url = await get_url_async(session, chave)
        if not base_url:
            error_summary[chave] += 1
            print(f"❌ URL não encontrada para a chave {chave}")
            return []

        endpoint = f"{base_url}/prest/relatorios/demonstrativo-financeiro"
        competencias = pd.date_range(start=dt_inicio, end=dt_fim, freq="MS")
        all_rows: List[dict] = []

        for comp in competencias:
            competencia_str = f"{comp.month:02d}/{comp.year}"
            data_inicio = comp.strftime("%d/%m/%Y")
            data_fim_mes = (
                (comp + pd.DateOffset(months=1) - pd.DateOffset(days=1))
                .strftime("%d/%m/%Y")
            )

            params = {
                "key": chave,
                "dataInicio": data_inicio,
                "dataFim": data_fim_mes,
                "codigoEmpresa": "1",
                "tipoRelatorio": "COMPETENCIA",
            }
            headers = {"Authorization": f"Bearer {chave}"}

            attempt = 0
            while attempt <= retries:
                try:
                    async with session.get(
                        endpoint, headers=headers, params=params, timeout=30
                    ) as resp:
                        if resp.status == 200:
                            payload = await resp.json()
                            for agrupador in payload or []:
                                all_rows.extend(
                                    extract_rows(agrupador, chave, competencia_str)
                                )
                            break  # sucesso
                        else:
                            error_summary[chave] += 1
                            msg = await resp.text()
                            print(
                                f"{resp.status} – {chave} – comp {competencia_str}: {msg}"
                            )
                            if "Dados Não Encontrados" in msg:
                                break
                except Exception as exc:
                    error_summary[chave] += 1
                    print(
                        f"⚠️  Tentativa {attempt+1}/{retries} falhou p/ chave {chave}: {exc}"
                    )
                finally:
                    attempt += 1
                    if attempt <= retries:
                        await asyncio.sleep(wait_time)

        return all_rows


async def generate_demonstrativo_financeiro_async(
    chaves: List[str],
) -> pd.DataFrame:
    cols = [
        "chave",
        "competencia",
        "nome_agrupador",
        "nome_agrupador_completo",
        "descricao_lancamento",
        "contrato",
        "nome_pessoa",
        "valor_lancamento",
        "rateio_nome_plano",
        "rateio_nome_centro_custo",
        "rateio_percentual",
    ]

    dt_inicio = pd.Timestamp("2025-01-01")
    dt_fim = pd.Timestamp("today").normalize()

    semaphore = asyncio.Semaphore(5)
    error_summary: defaultdict = defaultdict(int)

    async with aiohttp.ClientSession() as session:
        tasks = [
            get_demonstrativo_financeiro_async(
                session, semaphore, error_summary, chave, dt_inicio, dt_fim
            )
            for chave in chaves
        ]
        results = []
        for task in tasks:
            result = await task
            results.append(result)

    df = pd.DataFrame([row for sub in results for row in sub], columns=cols)
    df = df.sort_values(
        ["competencia", "nome_agrupador_completo", "descricao_lancamento"], ignore_index=True
    )

    if error_summary:
        print("\nResumo de erros por chave:")
        for k, v in error_summary.items():
            print(f"  {k}: {v} ocorrências")
    print(f"\nTotal de linhas retornadas: {len(df):,}")
    return df


# ------------------------------------------------------------------
# Interface pública usada pelo runner do GitLab
# ------------------------------------------------------------------
def generate_demonstrativo_financeiro(chaves: List[str]) -> pd.DataFrame:
    """Wrapper síncrono para o runner (Util.to_gbq)."""
    return asyncio.run(generate_demonstrativo_financeiro_async(chaves))


# ------------------------------------------------------------------
# Execução local de debug (não é usada no runner)
# ------------------------------------------------------------------
if __name__ == "__main__":
    CHAVES_TESTE = ["c464fbabae63716abd8efd8569d0918e"]
    df_debug = generate_demonstrativo_financeiro(CHAVES_TESTE)

    # Caminho para salvar o arquivo
    output_path = r"C:\Users\<USER>\Downloads\demonstrativo_financeiro.csv"
    df_debug.to_csv(output_path, index=False)

    print(f"\n✅ CSV salvo em: {output_path}")