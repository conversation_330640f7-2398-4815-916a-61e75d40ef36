"""Consulta de dados de Chargeback do sistema"""
from collections import defaultdict
import asyncio
import aiohttp
import pandas as pd

async def get_url_async(session: aiohttp.ClientSession, chave: str) -> str:
    """Busca a URL de discovery de forma assíncrona."""
    api_url = f'https://discovery.ms.pactosolucoes.com.br/find/{chave}'
    async with session.get(api_url) as resp:
        if resp.status == 200:
            json_data = await resp.json()
            return (
                json_data
                .get('content', {})
                .get('serviceUrls', {})
                .get('zwUrl', '')
            )
        else:
            return ''

async def get_chargebacks_for_chave(
    session: aiohttp.ClientSession,
    semaphore: asyncio.Semaphore,
    error_summary: dict,
    chave: str,
    data_inicio_ts: pd.Timestamp,
    data_fim: pd.Timestamp
) -> list:
    """
    Busca chargebacks de forma assíncrona para uma única chave,
    mas com requisições mensais sendo feitas de forma sequencial.
    """
    async with semaphore:
        base_url = await get_url_async(session, chave)
        if not base_url:
            print(f"URL vazia para chave {chave}, aguardando 10s antes de tentar novamente...")
            await asyncio.sleep(10)
            base_url = await get_url_async(session, chave)  # Tenta novamente
        url = f"{base_url}/prest/recebiveis/chargeback"

        dias = pd.date_range(start=data_inicio_ts, end=data_fim, freq='MS')

        if data_fim not in dias:
            dias = dias.append(pd.DatetimeIndex([data_fim]))

        all_rows = []

        for dia in dias:
            data_inicio = dia.strftime('%d/%m/%Y')
            data_fim_mes = (
                dia + pd.DateOffset(months=1) - pd.Timedelta(days=1)
                ).strftime('%d/%m/%Y')

            querystring = {
                "key": chave,
                "dataInicio": data_inicio,
                "dataFim": data_fim_mes,
                "tipoConciliacao": "3",
                "codigoEmpresa": "1"
            }

            headers = {"Authorization": f"Bearer {chave}"}
            retries = 3
            wait_time = 5
            attempt = 0

            while attempt <= retries:
                try:
                    async with session.get(
                        url,
                        headers=headers,
                        params=querystring,
                        timeout=10
                        ) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            content = data.get('dados', {})

                            for status, key in [("CA", "dados"), ("CH", "cancelados")]:
                                for item in content.get(key, []):
                                    all_rows.append([
                                        chave, dia, status,
                                        item.get("formaPagamento", None),
                                        item.get("autorizacao", None),
                                        item.get("nsu", None),
                                        item.get("dataLancamentoMP", None),
                                        item.get("tipoConcilicacao", None),
                                        item.get("valorBruto", None),
                                        item.get("dataLancamentoApresentar", None),
                                        item.get("getNrParcelas_apresentar", None),
                                        item.get("nomePagador", None),
                                        item.get("valorMP", None),
                                        item.get("contaMovimento", None),
                                        item.get("valorCC", None)
                                    ])
                            break  # Sai do loop de tentativas se a requisição for bem-sucedida
                        else:
                            error_summary[chave] += 1
                            error_message = await resp.text()
                            print(f"{resp.status} - {chave} - {data_inicio} - {data_fim_mes}")
                            print(f"Resposta da API: {error_message}")

                            attempt += 1
                            await asyncio.sleep(wait_time)
                except Exception as e:
                    error_summary[chave] += 1
                    print(f"Exceção para chave {chave}, tentativa {attempt+1}: {e}")
                    attempt += 1
                    await asyncio.sleep(wait_time)

        return all_rows

async def generate_chargeback_async(chaves: list) -> pd.DataFrame:
    """
    Executa a consulta assíncrona por chave, mantendo as datas de cada chave de forma sequencial.
    """
    dataframe_columns = [
        'chave', 'dia', 'status', 'formaPagamento', 'autorizacao', 'nsu',
        'dataLancamentoMP', 'tipoConcilicacao', 'valorBruto', 'dataLancamentoApresentar',
        'getNrParcelas_apresentar', 'nomePagador', 'valorMP', 'contaMovimento', 'valorCC'
    ]

    data_inicio_ts = pd.to_datetime('01/01/2025', format='%d/%m/%Y')
    data_fim = pd.to_datetime('today')

    semaphore = asyncio.Semaphore(2)  # Define um limite de concorrência apenas para as chaves
    error_summary = defaultdict(int)

    async with aiohttp.ClientSession() as session:
        tasks = [
            get_chargebacks_for_chave(
                session,
                semaphore,
                error_summary,
                chave,
                data_inicio_ts,
                data_fim
                )
            for chave in chaves
        ]
        results = await asyncio.gather(*tasks)

    print("\nResumo de erros por chave:")
    for chave, count in error_summary.items():
        print(f"Chave: {chave}, Erros: {count}")

    flat_results = [row for sublist in results if sublist for row in sublist]  # Filtra listas vazias

    if flat_results:
        dataframe = pd.DataFrame(flat_results, columns=dataframe_columns)
    else:
        dataframe = pd.DataFrame(columns=dataframe_columns)  # Cria um DataFrame vazio com colunas

    print(f"\nTotal de registros adicionados ao DataFrame: {len(flat_results)}")

    return dataframe

def generate_chargeback(list_chaves: list) -> pd.DataFrame:
    """Gera os dados do chargeback de forma assíncrona por chave."""
    return asyncio.run(generate_chargeback_async(list_chaves))

if __name__ == "__main__":
    input_chaves = [
        '8cac29a56e4ccd7a6a72a7e5bf432d66',
        '871763cff107b8a0ef2cf42a9f164c3',
        '1d40882bc4bd32e41d71d18381755f93',
        '5c367450890ebad1a8752bbd7060b820',
        'f0be27fc6698fedfddc8e2afe89acf63'
    ]
    df_chargeback = generate_chargeback(input_chaves)
    print(df_chargeback)
