import os
import requests
import pandas as pd
from tqdm import tqdm
from google.cloud import bigquery
from google.oauth2 import service_account

# Token da API
API_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************.xFavLebDGfNAOKl_dV_g2HitWYQZWFGW6CduHRUxvx0"

# URL da API
URL = f"http://app.nectarcrm.com.br/crm/api/1/contatos/?api_token={API_TOKEN}"

# Função para obter os dados
def obter_dados_api(url):
    contatos_formatados = []
    page = 1
    max_pages = 10  # Limite para pegar até a página 10

    while page <= max_pages:
        print(f"🔄 Enviando requisição para a página {page}...")
        response = requests.get(f"{url}&page={page}", timeout=15)

        if response.status_code == 200:
            try:
                data = response.json()
                # Processa os dados diretamente (não mais esperando por uma chave 'contatos')
                for item in tqdm(data, desc=f"Formatando registros (Página {page})"):
                    data_criacao = pd.to_datetime(item.get("dataCriacao"))
                    data_atualizacao = pd.to_datetime(item.get("dataAtualizacao"))

                    # Endereço principal (se existir)
                    enderecos = item.get("enderecos", [])
                    endereco_principal = next((e for e in enderecos if e.get("principal")), {})
                    uf = endereco_principal.get("uf", "")
                    bairro = endereco_principal.get("bairro", "")
                    cep = endereco_principal.get("cep", "")
                    logradouro = endereco_principal.get("logradouro", "")
                    endereco_formatado = endereco_principal.get("endereco", "")

                    contato = {
                        "idEntidade": item.get("id"),
                        "codigo": item.get("autor", {}).get("id"),
                        "nome": item.get("nome"),
                        "tipo": item.get("empresa"),
                        "origem": item.get("origem"),
                        "razaoSocial": item.get("razaoSocial"),
                        "cnpj": item.get("cnpj"),
                        "email": item.get("emailPrincipal"),
                        "telefones": item.get("telefones"),
                        "site": item.get("site"),
                        "linkedin": item.get("linkedin"),
                        "estado": uf,
                        "Municipio": bairro,
                        "logradouro": logradouro,
                        "endereco_completo": endereco_formatado,
                        "endereco_cep": cep,
                        "dataCriacao": data_criacao,
                        "dataCriacaoAno": data_criacao.year,
                        "dataCriacaoMes": data_criacao.month,
                        "dataCriacaoDia": data_criacao.day,
                        "dataCriacaoSemana": data_criacao.isocalendar().week,
                        "dataAtualizacao": data_atualizacao,
                        "dataAtualizacaoAno": data_atualizacao.year,
                        "dataAtualizacaoMes": data_atualizacao.month,
                        "dataAtualizacaoDia": data_atualizacao.day,
                        "dataAtualizacaoSemana": data_atualizacao.isocalendar().week,
                        "responsavel": item.get("responsavel", {}).get("nome"),
                        "idResponsavel": item.get("responsavel", {}).get("id"),
                        "autorNome": item.get("autor", {}).get("nome"),
                        "autorAtualizacao": item.get("autorAtualizacao", {}).get("nome"),
                    }

                    contatos_formatados.append(contato)

               
                if len(data) > 0: 
                    page += 1
                else:
                    break  # Não há mais páginas

            except Exception as e:
                print(f"❌ Erro ao processar a resposta JSON: {e}")
                break
        else:
            print(f"❌ Erro na requisição: {response.status_code}")
            break

    return contatos_formatados

# Função para enviar dados ao BigQuery
def enviar_para_bq(dataframe, project_id, dataset_id, table_name, credentials):
    from google.cloud import bigquery
    client = bigquery.Client(project=project_id, credentials=credentials)

    # Enviar os dados para o BigQuery
    dataframe.to_gbq(
        destination_table=f"{dataset_id}.{table_name}",
        project_id=project_id,
        if_exists="replace",
        credentials=credentials,
        location="southamerica-east1"
    )

# Definindo as credenciais e informações do projeto
project_id = "oamd-e-financeiro-pacto"
dataset_id = "pacto_nectar_prod"
credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": project_id,
        "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
        "private_key": os.environ.get('KEY_GCP_DATALAKE').replace('\\n', '\n'),
        "client_email": "<EMAIL>",
        "client_id": "117321030275837789997",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
    })

# Obtendo dados da API
print("🔄 Obtendo dados...")
dados = obter_dados_api(URL)

# Convertendo para DataFrame
df = pd.DataFrame(dados)

# Enviar os dados para o BigQuery
enviar_para_bq(df, project_id, dataset_id, "contatos", credentials)

print(f"✅ Dados enviados para o BigQuery com sucesso!")
