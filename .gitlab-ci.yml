stages: # List of stages for jobs, and their order of execution
  - build
  - chat_log

variables:
  TAG: registry.gitlab.com/plataformazw/pacto-pydata-eng:$CI_COMMIT_REF_SLUG
  TAG_MASTER: registry.gitlab.com/plataformazw/pacto-pydata-eng:master

before_script:
  - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
  - export PYTHONDONTWRITEBYTECODE=1

.build-common:
  stage: build
  interruptible: true
  except:
    variables:
      - $IPNET
      - $REDE_FAB
      - $ATIVOS
      - $ATIVOS_LISTA
      - $PACTO_GRUPO_PACTO_QR
      - $ENGENHARIA
      - $ENGENHARIA_SINGLE
      - $PACTO_REDEDEMO
      - $PACTO_SINGLE
      - $PACTO_GRUPOPACTO
      - $PACTO_TODAS_EMPRESAS
      - $PACTO_RECURSO_EMPRESA
      - $PACTO_POC
      - $PACTO_ANALISE_LOCAL_ACESSO
      - $PACTO_PERSO_001
      - $PACTO_VALIDADOR_IMPORTACAO
      - $PACTO_DETALHES_EMPRESA_CS
      - $DADOS_22f2d0587a
      - $DADOS_20b256fe77
      - $DADOS_b597565be2
      - $OAMD_PAGAMENTO
      - $OAMD_FINANCEIRO
      - $OAMD_PACTOPAY
      - $OAMD_PLANODESUCESSO
      - $OMAD_SQL_SERVER
      - $DADOS_341b908afd
      - $DADOS_608e98cfc9_MCL
      - $DADOS_ac649a8c04
      - $DADOS_1a97fec7cf
      - $DADOS_8c7a9f06ab
      - $DADOS_3166708e04
      - $DADOS_fe6966d7e1
      - $DADOS_495afa1552
      - $DADOS_5527da2b9f
      - $DADOS_89048d6669
      - $DADOS_4d3d9bfba7
      - $DADOS_d45e9c2b3f
      - $DADOS_4681725a2d
      - $DADOS_4d6c61e75b
      - $DADOS_7e045904d5
      - $DADOS_ce4c5aaa54
      - $DADOS_de9a667bfc
      - $DADOS_d98c1f4cb7
      - $DADOS_babcb63465
      - $DADOS_94f5ab73ca
      - $DADOS_3a2efdcfe3
      - $DADOS_bb89ff1e21
      - $DADOS_eb7e3fb87e
      - $DADOS_ace70b0ade
      - $GYMBOTPRO
      - $ANALISE_PAGAMENTO_RECORRENTE
      - $INTEGRACAO_JIRA_DADOS_SDD
      - $INTEGRACAO_JIRA_DADOS_DELCIO
      - $INTEGRACAO_DADOS_JIRA_CUSTOMIZACOES
      - $INTEGRACAO_DADOS_SHEETS_SUPORTE
      # - $red_fitness
      - $SQL_SERVER
      - $TESTECLIENTES
      - $SCRAP_TRELLO
      - $GITLOG
      - $CUPOM_DESCONTO
      - $CHURN
      - $NOTA_FISCAL
      - $TIME_PRODUTO
      - $DADOS_MRA
      - $TAXA_CARTAO
      - $CHUNK
      - $NPS
      - $TELA_LOGIN
      - $PIPE
      - $KPIS_PRODUTOS_PACTO
      - $INFO_MIGRACAO_RECURSO_PADRAO
      - $SESSION
      - $CONTRATO_TIPO_SITUACAO
      - $PRODUTO_VENDIDO_TIPO
      - $CAMPANHA
      - $CUPOM_DESCONTO_TIPO
      - $OPERACAO_CONTRATO_TIPO
      - $ASSINATURA_DIGITAL_TIPO
      - $ASSINATURA_DIGITAL_PARQ
      - $EMPRESA
      - $USUARIO_ADM
      - $USUARIO_TREINO
      - $PROGRAMA_TREINO
      - $TREINO_REALIZADO
      - $AGENDAMENTO_AULA
      - $AGENDAMENTO_SERVICO
      - $CLIENTE_ADM
      - $CROSS_WOD
      - $CROSS_SCORE
      - $CONCILIACAO_ITEM
      - $EMPRESA_ADM
      - $CONTA_PAGAR_RECEBER
      - $CAIXA
      - $RECEITA
      - $RECEITA_COMPENSADO
      - $RECEITA_COMPENSADO_DIA
      - $PRT_PERSONALIZADO
      - $ACESSOS_KPI
      - $ACESSOS_DETALHADO
      - $PARCELA
      - $PARCELA_PARTITION_INDEX
      - $CONTROLADORIA
      - $EDC_TICKET_MEDIO
      - $EDC_COMISSAO_CONSULTOR
      - $IRON_DEMONSTRATIVO_FINANCEIRO
      - $IRONBERG_TICKET_MEDIO
      - $IPANEMA_TICKET_MEDIO
      - $LIVE_TICKET_MEDIO
      - $MRA_TICKET_MEDIO
      - $CHARGEBACK
      - $DADOS_CONVERSAS
      - $TREINO_IA

  script:
    - echo "`date` - Compiling the code..."
    - docker build -t $TAG .
    - docker push $TAG
    - echo "`date` - Compile complete."

build-job-branches:
  extends: .build-common

build-job-master:
  extends: .build-common

pct-lista-ativos:
  only:
    variables:
      - $ATIVOS_LISTA
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/pacto-lista-usuarios-data.py

pct-pst-grupopacto-qr:
  only:
    variables:
      - $PACTO_GRUPO_PACTO_QR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-grupopacto.py

pct-pst-grupopacto-qr:
  only:
    variables:
      - $REDE_FAB
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-unidades_fab.py

pct-pipe:
  only:
    variables:
      - $PIPE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/Pipe.py

pct-taxa-cartao:
  only:
    variables:
      - $TAXA_CARTAO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/taxas_cartao.py

pct-tela-login:
  only:
    variables:
      - $TELA_LOGIN
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/tela_login.py

#generate-data-pratique:
#  only:
#    variables:
#      - $GENERATE
#  stage: build
#  script:
#    - docker run --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pratique-data.py

#gen-dtox:
#  only:
#    variables:
#      - $DTOX
#  stage: build
#  script:
#    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_DTOX" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER dtox-data.py

gen-ipnet:
  only:
    variables:
      - $IPNET
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_DEV_IPNET" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/ipnet.py

gen-edc-full:  
  tags:
    - edc
  only:
    variables:
      - $ENGENHARIA
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_EDC" -e LGN_KEY=$LGN_KEY -e QR=$QR -e IS=$IS --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/engenharia.py

gen-edc-ticket-medio:  
  tags:
    - edc
  only:
    variables:
      - $EDC_TICKET_MEDIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_EDC" -e LGN_KEY=$LGN_KEY -e QR=$QR -e EDC_TICKET_MEDIO=$EDC_TICKET_MEDIO --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/engenharia.py

gen-edc-comissao-consultor:  
  tags:
    - edc
  only:
    variables:
      - $EDC_COMISSAO_CONSULTOR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_EDC" -e LGN_KEY=$LGN_KEY -e QR=$QR -e EDC_COMISSAO_CONSULTOR=$EDC_COMISSAO_CONSULTOR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/engenharia.py

gen-iron-demonstrativo-financeiro:  
  only:
    variables:
      - $IRON_DEMONSTRATIVO_FINANCEIRO
  stage: build
  script:
    - docker run --entrypoint sh -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4681725a2d" -e LGN_KEY="$LGN_KEY" -e QR="$QR" -e IRON_DEMONSTRATIVO_FINANCEIRO="$IRON_DEMONSTRATIVO_FINANCEIRO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER -c "until pip install aiohttp==3.7.4.post0 pandas==2.0.3 yarl==1.9.4 multidict==6.0.5 async-timeout==3.0.1 typing_extensions==4.12.2; do echo '⏳ pip install falhou, tentando novamente...'; sleep 5; done && python clientes/dados-4681725a2d.py"

gen-edc-single:  
  tags:
    - edc
  only:
    variables:
      - $ENGENHARIA_SINGLE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_EDC" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/engenharia-single.py

pct-pst-rededemo:  
  only:
    variables:
      - $PACTO_REDEDEMO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-rededemo.py

gen-pst-pacto-single:  
  only:
    variables:
      - $PACTO_SINGLE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-single.py

gen-dados-22f2d0587a-full:  
  only:
    variables:
      - $DADOS_22f2d0587a
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_22f2d0587a" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-22f2d0587a.py

gen-dados-20b256fe77-full:  
  only:
    variables:
      - $DADOS_20b256fe77
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_20b256fe77" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-20b256fe77.py

gen-dados-b597565be2-full:
  only:
    variables:
      - $DADOS_b597565be2
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_b597565be2" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-b597565be2.py

gen-dados-b597565be2-chargeback:
  only:
    variables:
      - $CHARGEBACK
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_b597565be2" -e LGN_KEY=$LGN_KEY -e QR=$QR -e CHARGEBACK=$CHARGEBACK --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-b597565be2.py


z_oamd_empresaformapagamento:
  only:
    variables:
      - $OAMD_PAGAMENTO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/z_oamd_empresaformapagamento.py

oamd_cupom_desconto:
  only:
    variables:
      - $CUPOM_DESCONTO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/cupom_desconto.py

z_oamd_empresafinanceiro_join:
  only:
    variables:
      - $OAMD_FINANCEIRO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/z_oamd_empresafinanceiro_join.py

z_oamd_pactopay_to_bigquery:  
  only:
    variables:
      - $OAMD_PACTOPAY
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/z_oamd_pactopay_to_bigquery.py

oamd_finan_todos_os_dados-sql_server:
  only:
    variables:
      - $OAMD_SQL_SERVER
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/oamd_finan_todos_os_dados_sql_server.py

gen-dados-341b908afd-full:  
  only:
    variables:
      - $DADOS_341b908afd
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_341b908afd" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-341b908afd.py

gen-dados-341b908afd-prt-personalizado-full:  
  only:
    variables:
      - $PRT_PERSONALIZADO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_341b908afd" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/prt_personalizado.py


gen-dados-608e98cfc9-mcl-full:  
  only:
    variables:
      - $DADOS_608e98cfc9_MCL
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_608e98cfc9_mcl" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-608e98cfc9-mcl.py

gen-dados-ac649a8c04-full:  
  only:
    variables:
      - $DADOS_ac649a8c04
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_ac649a8c04" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-ac649a8c04.py

gen-dados-1a97fec7cf-full:  
  only:
    variables:
      - $DADOS_1a97fec7cf
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_1a97fec7cf" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-1a97fec7cf.py

gen-dados-8c7a9f06ab-full:  
  only:
    variables:
      - $DADOS_8c7a9f06ab
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_8c7a9f06ab" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-8c7a9f06ab.py

gen-dados-3166708e04-full:  
  only:
    variables:
      - $DADOS_3166708e04
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_3166708e04" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-3166708e04.py

pct-gymbotpro-wagi:
  only:
    variables:
      - $GYMBOTPRO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/gymbotpro.py

gen-dados-fe6966d7e1-full:  
  only:
    variables:
      - $DADOS_fe6966d7e1
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_fe6966d7e1" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-fe6966d7e1.py

gen-dados-5527da2b9f-full:  
  only:
    variables:
      - $DADOS_5527da2b9f
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_5527da2b9f" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-5527da2b9f.py

gen-new-dados-5527da2b9f-full:  
  only:
    variables:
      - $NEW_DADOS_5527da2b9f
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$NEW_KEY_GCP_DATALAKE_5527da2b9f" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/new-dados-5527da2b9f.py

gen-dados-89048d6669-full:  
  only:
    variables:
      - $DADOS_89048d6669
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_89048d6669" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-89048d6669.py

gen-dados-4681725a2d-full:  
  only:
    variables:
      - $DADOS_4681725a2d
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4681725a2d" -e LGN_KEY=$LGN_KEY -e QR=$QR -e IS=$IS --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-4681725a2d.py

gen-dados-4681725a2d-ticket-medio:  
  only:
    variables:
      - $IRONBERG_TICKET_MEDIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4681725a2d" -e LGN_KEY=$LGN_KEY -e IRONBERG_TICKET_MEDIO=$IRONBERG_TICKET_MEDIO --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-4681725a2d.py

gen-dados-4681725a2d-ticket-medio:  
  only:
    variables:
      - $IPANEMA_TICKET_MEDIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_ce4c5aaa54" -e LGN_KEY=$LGN_KEY -e IPANEMA_TICKET_MEDIO=$IPANEMA_TICKET_MEDIO --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-ce4c5aaa54.py

gen-dados-b597565be2-ticket-medio:  
  only:
    variables:
      - $LIVE_TICKET_MEDIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_b597565be2" -e LGN_KEY=$LGN_KEY -e LIVE_TICKET_MEDIO=$LIVE_TICKET_MEDIO --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-b597565be2.py

gen-DADOS-MRA-ticket-medio:  
  only:
    variables:
      - $MRA_TICKET_MEDIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_ORION" -e LGN_KEY=$LGN_KEY -e MRA_TICKET_MEDIO=$MRA_TICKET_MEDIO --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER academia-de-verdade/clientes.py

gen-dados-4d6c61e75b-full:  
  only:
    variables:
      - $DADOS_4d6c61e75b
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4d6c61e75b" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-4d6c61e75b.py

gen-dados-4d3d9bfba7-full:  
  only:
    variables:
      - $DADOS_4d3d9bfba7
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4d3d9bfba7" -e LGN_KEY=$LGN_KEY -e QR=$QR --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-4d3d9bfba7.py

gen-dados-d45e9c2b3f-full:  
  only:
    variables:
      - $DADOS_d45e9c2b3f
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_d45e9c2b3f" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-d45e9c2b3f.py

gen-dados-7e045904d5-full:  
  only:
    variables:
      - $DADOS_7e045904d5
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_7e045904d5" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-7e045904d5.py

gen-dados-ce4c5aaa54-full:  
  only:
    variables:
      - $DADOS_ce4c5aaa54
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_ce4c5aaa54" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-ce4c5aaa54.py

gen-dados-de9a667bfc-full:  
  only:
    variables:
      - $DADOS_de9a667bfc
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_de9a667bfc" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-de9a667bfc.py

gen-dados-d98c1f4cb7-full:  
  only:
    variables:
      - $DADOS_d98c1f4cb7
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_d98c1f4cb7" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-d98c1f4cb7.py

gen-dados-a7c9057233-full:  
  only:
    variables:
      - $DADOS_a7c9057233
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_a7c9057233" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-a7c9057233.py

gen-dados-babcb63465-full:  
  only:
    variables:
      - $DADOS_babcb63465
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_babcb63465" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-babcb63465.py

gen-dados-3a2efdcfe3-full:  
  only:
    variables:
      - $DADOS_3a2efdcfe3
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_3a2efdcfe3" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-3a2efdcfe3.py

gen-dados-bb89ff1e21-full:  
  only:
    variables:
      - $DADOS_bb89ff1e21
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_bb89ff1e21" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-bb89ff1e21.py

gen-dados-ace70b0ade-full:  
  only:
    variables:
      - $DADOS_ace70b0ade
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_ace70b0ade" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-ace70b0ade.py

gen-dados-eb7e3fb87e-full:  
  only:
    variables:
      - $DADOS_eb7e3fb87e
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_eb7e3fb87e" -e LGN_KEY=$LGN_KEY -e QR=$QR -e IS=$IS --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-eb7e3fb87e.py

# att de forma incremental do recursoempresa de 2 em 2 horas
pct-recurso-empresa:
  only:
    variables:
      - $PACTO_RECURSO_EMPRESA
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/recursoempresa.py

session:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$SESSION'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/session.py

treino-por-ia:
  rules:
    - if: '$TREINO_IA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/treino_ia.py

contrato-tipo-situacao:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CONTRATO_TIPO_SITUACAO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/contrato_tipo_situacao.py

produto-vendido-tipo:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$PRODUTO_VENDIDO_TIPO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/produto_vendido_tipo.py

campanha:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CAMPANHA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/campanha.py

cupom_desconto_tipo:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CUPOM_DESCONTO_TIPO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/cupom_desconto_tipo.py

operacao_contrato_tipo:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$OPERACAO_CONTRATO_TIPO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/operacao_contrato_tipo.py


assinatura_digital_tipo:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$ASSINATURA_DIGITAL_TIPO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/assinatura_digital_tipo.py

assinatura_digital_parq:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$ASSINATURA_DIGITAL_PARQ'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/assinatura_digital_parq.py

empresa:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$EMPRESA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/empresa.py

usuario_adm:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$USUARIO_ADM'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/usuario_adm.py

usuario_adm_empresa:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$USUARIO_ADM'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/usuario_adm_empresa.py

programa_treino:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$PROGRAMA_TREINO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/programa_treino.py

treino_realizado:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$TREINO_REALIZADO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/treino_realizado.py

agendamento_aula:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$AGENDAMENTO_AULA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/agendamento_aula.py

agendamento_servico:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$AGENDAMENTO_SERVICO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/agendamento_servico.py

avaliacao_fisica:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$AVALIACAO_FISICA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/avaliacao_fisica.py

reavaliacao_fisica:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$AVALIACAO_FISICA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/reavaliacao_fisica.py


usuario_treino:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$USUARIO_TREINO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/usuario_treino.py

cliente_adm:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CLIENTE_ADM'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/cliente_adm.py


cross_wod:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CROSS_WOD'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/cross_wod.py

cross_score:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CROSS_SCORE'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/cross_score.py

conciliacao_item:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CONCILIACAO_ITEM'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/conciliacao_item.py

empresa_adm:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$EMPRESA_ADM'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/empresa_adm.py

conta_pagar_receber:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CONTA_PAGAR_RECEBER'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/conta_pagar_receber.py

caixa:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$CAIXA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/caixa.py

receita_compensado:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$RECEITA_COMPENSADO'
    - if: '$RECEITA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/receita_compensado.py

receita_compensado_empresa_dia:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$RECEITA_COMPENSADO_DIA'
    - if: '$RECEITA'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/receita_compensado_empresa_dia.py

empresa_financeiro:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$EMPRESA_FINANCEIRO'
    - if: '$RECEITA'
    - if: '$NICHO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/empresa_financeiro.py

nicho:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$NICHO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/nicho.py


graduacao_ficha_tecnica_aluno:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$GRADUACAO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/graducacao_alunos_ficha_tecnica.py

acesso:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$ACESSOS_KPI'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/acessos.py

acesso_detalhado:
  rules:
    - if: '$KPIS_PRODUTOS_PACTO'
    - if: '$ACESSOS_DETALHADO'
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/acessos_detalhado.py

parcela:
  stage: build
  parallel: 8
  rules:
    - if: $KPIS_PRODUTOS_PACTO 
    - if: $PARCELA
  script:
    - echo "Executando job index = $CI_NODE_INDEX (de $CI_NODE_TOTAL)"
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/parcela.py $CI_NODE_INDEX

parcela_index:
  stage: build
  rules:
    - if: $KPIS_PRODUTOS_PACTO 
    - if: $PARCELA_PARTITION_INDEX
  script:
    - echo "Executando job index = $CI_NODE_INDEX (de $CI_NODE_TOTAL)"
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/kpis_produtos_pacto/parcela.py $PARCELA_PARTITION_INDEX

pacto-info-migracao-recurso-padrao:
  only:
    variables:
      - $INFO_MIGRACAO_RECURSO_PADRAO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/pacto-info-migracao-recurso-padrao.py
pct-analise-local-acesso:
  only:
    variables:
      - $PACTO_ANALISE_LOCAL_ACESSO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/pacto-local-acesso.py

pct-detalhes-empresa:
  only:
    variables:
      - $PACTO_DETALHES_EMPRESA_CS
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/detalhesempresa_cs.py

pct-perso-001:
  only:
    variables:
      - $PACTO_PERSO_001
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-perso.py

pct-pst-poc:
  only:
    variables:
      - $PACTO_POC
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_POC" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-poc.py

pct-pst-grupopacto:
  only:
    variables:
      - $PACTO_GRUPOPACTO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_767c9fb55d" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/pacto-grupopacto.py

pct-validador-de-importacao:
  only:
    variables:
      - $PACTO_VALIDADOR_IMPORTACAO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_VALIDADOR" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/validador-de-importacao.py

sql-server:
  only:
    variables:
      - $SQL_SERVER
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_SQL_SERVER" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER SQLServer.py --table_name $TABLE_NAME --database $DATABASE
  retry: 2
  timeout: 40m

controladoria:
  only:
    variables:
      - $CONTROLADORIA
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_CONTROLADORIA" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/SQLServer.py --table_name $TABLE_NAME --database $DATABASE
  retry: 2
  timeout: 40m

#teste-cliente
gen-teste-clientes:
  only:
    variables:
      - $TESTECLIENTES
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_1a97fec7cf" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER testes/teste.py

scrap-trello:
  only:
    variables:
      - $SCRAP_TRELLO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/scrap-trello.py

GIT-LOG:
  only:
    variables:
      - $GITLOG
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_LOG" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER extras/Pipeline.py

z_oamd_planosdesucesso_join:
  only:
    variables:
      - $OAMD_PLANODESUCESSO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/z_oamd_planosdesucesso_join.py

CHURN:
  only:
    variables:
      - $CHURN
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_ORION" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER academia-de-verdade/dados-clientes.py

CONVERSAS:
  only:
    variables:
      - $DADOS_CONVERSAS
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_conversas" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/conversas.py

DADOS_MRA:
  only:
    variables:
      - $DADOS_MRA
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_ORION" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER academia-de-verdade/clientes.py

notafiscal_to_bigquery:
  only:
    variables:
      - $NOTA_FISCAL
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG pacto/nota_fiscal.py

metricas_time_produto:
  only:
    variables:
      - $TIME_PRODUTO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/metricas_time_produto.py

gen-dados-495afa1552-full:  
  only:
    variables:
      - $DADOS_495afa1552
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_495afa1552" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/dados-495afa1552.py

#teste-cliente
gen-teste-mateus:
  only:
    variables:
      - $TESTE_MATEUS
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_EDC" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/teste_mateus.py

gen-dados-integracao_nectar:  
  only:
    variables:
      - $DADOS_INTEGRACAO_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/integracao_nectar.py

gen-dados-contatos_nectar:  
  only:
    variables:
      - $DADOS_CONTATOS_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_contatos.py

gen-dados-compromissos_nectar:  
  only:
    variables:
      - $DADOS_COMPROMISSOS_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_compromissos.py

gen-dados-formularios_nectar:  
  only:
    variables:
      - $DADOS_FORMULARIOS_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_formularios.py

gen-dados-oportunidades_nectar:  
  only:
    variables:
      - $DADOS_OPORTUNIDADES_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_oportunidades.py

gen-dados-produtos_nectar:  
  only:
    variables:
      - $DADOS_PRODUTOS_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_produtos.py

gen-dados-qualificacoes_nectar:  
  only:
    variables:
      - $DADOS_QUALIFICACOES_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_qualificacoes.py

gen-dados-tarefas_nectar:  
  only:
    variables:
      - $DADOS_TAREFAS_NECTAR
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_nectar/src/runners/run_tarefas.py

gen-analise-pag-rec:  
  only:
    variables:
      - $ANALISE_PAGAMENTO_RECORRENTE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_4d3d9bfba7" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER clientes/analise_pagamento_recorrente.py

gen-dados_jira_customizacoes:
  only:
    variables:
      - $INTEGRACAO_DADOS_JIRA_CUSTOMIZACOES
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/integracao_jira.py dados_jira_customizacoes "project NOT IN (Infra, \"Squad Data Driven\") AND type = Customização AND status in (\"Aguardando Merge\", \"Aguardando Resposta\", \"Análise de Requisitos\", \"Benchmarking/Pesquisa\", \"Criar DAN\", \"Criação da DAN\", \"Fila desenvolvimento\", Implementando, \"Para Testar\", \"Proposta Comercial\", Reaberto, Testando, \"TESTE REPROVADO\", Versão)" 0,1,2,3,5,6,7,8,9,11,12,13,15,16,17,18,19,20,21,22,23,24,25,27

gen-dados_jira_sdd:
  only:
    variables:
      - $INTEGRACAO_JIRA_DADOS_SDD
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/integracao_jira.py dados_jira_sdd "project = \"Squad Data Driven\"" 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27

gen-dados_jira_delcio:
  only:
    variables:
      - $INTEGRACAO_JIRA_DADOS_DELCIO
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/integracao_jira.py dados_jira_delcio "project NOT IN (PEU, PEB, TD, PEV) AND reporter = 70121:cd31e51f-b23f-4c63-a821-dcca2cc0f11d ORDER BY created DESC" 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28

gen-dados_suporte_movidesk:
  only:
    variables:
      - $INTEGRACAO_DADOS_SHEETS_SUPORTE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/integracao_sheets.py

CHUNK:
  only:
    variables:
      - $CHUNk
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_ORION" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER extras/load_chunks.py

pct-nps:  
  only:
    variables:
      - $NPS
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER projetos_internos/wehelp_nps.py

pct-datereference:
  only:
    variables:
      - $DATE
  stage: build
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/datereference.py

# VARIAVEIS RUNNER API GYMBOT #
pct-fetch-sessions:
  extends: .build-common
  only:
    variables:
      - $FETCH_SESSIONS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_sessions.py

pct-fetch-contacts:
  extends: .build-common
  only:
    variables:
      - $FETCH_CONTACTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_contacts.py

pct-fetch-agents:
  extends: .build-common
  only:
    variables:
      - $FETCH_AGENTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_agents.py

pct-fetch-panels:
  extends: .build-common
  only:
    variables:
      - $FETCH_PANELS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_panels.py

pct-fetch-channels:
  extends: .build-common
  only:
    variables:
      - $FETCH_CHANNELS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_channels.py
  
pct-fetch-departments:
  extends: .build-common
  only:
    variables:
      - $FETCH_DEPARTMENTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_departments.py

pct-fetch-tags:
  extends: .build-common
  only:
    variables:
      - $FETCH_TAGS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_tags.py

pct-fetch-chatbots:
  extends: .build-common
  only:
    variables:
      - $FETCH_CHATBOTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_chatbots.py

pct-fetch-panelcards:
  extends: .build-common
  only:
    variables:
      - $FETCH_PANELCARDS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_panels_cards.py

pct-fetch-panelnotes:
  extends: .build-common
  only:
    variables:
      - $FETCH_PANELNOTES
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_panels_notes.py

pct-fetch-jira-issues:
  extends: .build-common
  only:
    variables:
      - $FETCH_JIRA_ISSUES  # Executa apenas se a variável estiver definida
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_PACTO_ATIVOS" -e JIRA_API_TOKEN="$JIRA_API_TOKEN" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_jira.py