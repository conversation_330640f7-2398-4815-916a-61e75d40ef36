appdirs==1.4.4
attrs==21.4.0
audioread==2.1.9
beautifulsoup4==4.11.1
bleach==5.0.0
brotlipy==0.7.0
cachetools==5.2.0
certifi==2021.10.8
cffi @ file:///opt/conda/conda-bld/cffi_1642701102775/work
chardet==3.0.4
charset-normalizer @ file:///tmp/build/80754af9/charset-normalizer_1630003229654/work
colorama @ file:///tmp/build/80754af9/colorama_1607707115595/work
conda==4.12.0
conda-content-trust @ file:///tmp/build/80754af9/conda-content-trust_1617045594566/work
conda-package-handling @ file:///tmp/build/80754af9/conda-package-handling_1649105784853/work
cryptography @ file:///tmp/build/80754af9/cryptography_1639414572950/work
db-dtypes==1.0.2
decorator==5.1.1
defusedxml==0.7.1
entrypoints==0.4
fastjsonschema==2.15.3
google-api-core==2.8.2
google-auth==2.8.0
google-auth-oauthlib==0.5.2
google-cloud==0.34.0
google-cloud-bigquery==3.2.0
google-cloud-bigquery-storage==2.13.2
google-cloud-core==2.3.1
google-cloud-storage==2.4.0
google-crc32c==1.3.0
google-resumable-media==2.3.3
googleapis-common-protos==1.56.3
grpcio==1.46.3
grpcio-status==1.46.3
idna==2.8
ipykernel==4.6.1
ipython==5.5.0
ipython-genutils==0.2.0
Jinja2==3.1.2
joblib==1.1.0
jsonschema==4.6.0
jupyter-client==7.1.2
jupyter-core==4.10.0
jupyterlab-pygments==0.2.2
librosa==0.9.1
llvmlite==0.38.1
MarkupSafe==2.1.1
mistune==0.8.4
nbclient==0.6.4
nbconvert==6.5.0
nbformat==5.4.0
nest-asyncio==1.5.5
numba==0.55.2
numpy==1.22.4
oauthlib==3.2.0
packaging==21.3
pandas==1.4.2
pandas-gbq==0.17.6
pandocfilters==1.5.0
pexpect==4.8.0
pickleshare==0.7.5
pooch==1.6.0
portpicker==1.2.0
prompt-toolkit==1.0.18
proto-plus==1.20.6
protobuf==3.20.1
ptyprocess==0.7.0
pyarrow==8.0.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydata-google-auth==1.4.0
Pygments==2.12.0
pyOpenSSL @ file:///opt/conda/conda-bld/pyopenssl_1643788558760/work
pyparsing==3.0.9
pyrsistent==0.18.1
PySocks @ file:///tmp/build/80754af9/pysocks_1605305812635/work
python-dateutil==2.8.2
pytz==2022.1
pyzmq==23.2.0
requests==2.21.0
requests-oauthlib==1.3.1
resampy==0.2.2
rsa==4.8
ruamel-yaml-conda @ file:///tmp/build/80754af9/ruamel_yaml_1616016711199/work
scikit-learn==1.1.1
scipy==1.8.1
simplegeneric==0.8.1
six==1.12.0
SoundFile==0.10.3.post1
soupsieve==2.3.2.post1
terminado==0.13.3
threadpoolctl==3.1.0
tinycss2==1.1.1
tornado==4.5.3
tqdm @ file:///opt/conda/conda-bld/tqdm_1647339053476/work
traitlets==5.3.0
urllib3==1.24.3
wcwidth==0.2.5
webencodings==0.5.1
py-trello==0.19.0
python-gitlab==4.4.0
