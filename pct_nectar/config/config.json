{"service_account_file": "C:/Users/<USER>/VSCodeProjetos/demetrio/pct_nectar/src/runners/service-account.json", "project_id": "oamd-e-financeiro-pacto", "api": {"base_url": "https://app.nectarcrm.com.br/crm/api/1/", "endpoints": {"contatos": {"path": "contatos", "params": {"details": "basic"}, "max_items": null, "max_duration": 60, "incremental_param": "None", "write_mode": "WRITE_TRUNCATE", "excluded_fields": ["metadata", "campospersonalizados_nome_que_nao_muda"]}, "compromissos": {"path": "compromiss<PERSON>", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": "None", "excluded_fields": []}, "formularios": {"path": "formularios", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": "None", "excluded_fields": []}, "produtos": {"path": "produtos", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": "None", "excluded_fields": []}, "oportunidades": {"path": "oportunidades", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": null, "write_mode": "WRITE_TRUNCATE", "excluded_fields": []}, "qualificacoes": {"path": "qualificacoes", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": "None", "excluded_fields": []}, "tarefas": {"path": "tare<PERSON>s", "params": {}, "max_items": null, "max_duration": 60, "incremental_param": "None", "excluded_fields": []}}, "headers": {"Authorization": "Bearer ${NECTAR_API_TOKEN}", "accept": "application/json", "X-Request-Source": "PactoNectar"}}}