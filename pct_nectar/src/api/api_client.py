import os
import time
import requests
from typing import List, Dict, Optional
from datetime import datetime
from multiprocessing import Pool
from urllib.parse import urljoin

from src.utils.env_loader import load_env_vars
from src.utils.logging_config import setup_logging


class EnhancedAPIClient:
    def __init__(self, base_url: str = None, delay: float = 0.5):
        load_env_vars()
        self.api_token = os.getenv("NECTAR_API_TOKEN")
        if not self.api_token:
            raise ValueError("❌ Variável de ambiente NECTAR_API_TOKEN não encontrada no .env")

        self.base_url = base_url or os.getenv("NECTAR_BASE_URL", "https://app.nectarcrm.com.br/crm/api/1/")
        self.delay = delay
        self.logger = setup_logging("api_client")

        # Sessão com retry automático em conexões
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(max_retries=3)
        self.session.mount('https://', adapter)
        self.session.mount('http://', adapter)

    def fetch_page(self, endpoint: str, page: int, params: Dict, max_attempts: int = 3) -> List[Dict]:
        """Obtém uma página específica da API com retry inteligente."""
        url = urljoin(self.base_url, endpoint)
        full_params = {
            "api_token": self.api_token,
            "page": page,
            "displayLength": 100,
            **params
        }

        for attempt in range(1, max_attempts + 1):
            try:
                response = self.session.get(url, params=full_params, timeout=30)

                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 5))
                    self.logger.warning(f"⚠️ Rate limit na página {page}. Aguardando {retry_after}s (tentativa {attempt})...")
                    time.sleep(retry_after)
                    continue

                response.raise_for_status()
                page_data = response.json()

                if not page_data or not isinstance(page_data, list):
                    self.logger.info(f"✅ Fim dos dados na página {page}")
                    return []

                self.logger.debug(f"📄 Página {page} recebida com {len(page_data)} itens")
                return page_data

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"⚠️ Erro na página {page}, tentativa {attempt}/{max_attempts}: {e}")
                time.sleep(2 ** attempt)  # backoff exponencial

        # Após falhas consecutivas
        self.logger.error(f"❌ Falha permanente na página {page} após {max_attempts} tentativas.")
        return []

    def _fetch_page_wrapper(self, args):
        return self.fetch_page(*args)

    def get_data_parallel(
        self,
        endpoint: str,
        logger,
        params: Optional[Dict] = None,
        processes: int = 5,
        chunk_size: int = 8,
        chunk_delay: float = 3.0
    ) -> List[Dict]:
        """Coleta todas as páginas da API em blocos com multiprocessamento e retry."""
        params = params or {}
        all_data = []
        current_page = 1
        finished = False

        while not finished:
            pages = list(range(current_page, current_page + chunk_size))
            args_list = [(endpoint, page, params) for page in pages]

            with Pool(processes=processes) as pool:
                results = pool.map(self._fetch_page_wrapper, args_list)

            # Flatten dos resultados
            page_data = [item for sublist in results for item in sublist if sublist]
            all_data.extend(page_data)

            # Se retornou menos que o esperado, significa fim dos dados
            if len(page_data) < chunk_size * 100:
                finished = True
            else:
                current_page += chunk_size

            time.sleep(chunk_delay)

        logger.info(f"✅ Total de registros extraídos: {len(all_data)}")
        return all_data
