import os
import json
import re
from datetime import datetime
from typing import List, Dict, Optional

from google.cloud import bigquery
from google.oauth2 import service_account
from src.utils.logging_config import setup_logging


class BigQueryManager:
    def __init__(
        self,
        credentials_file: str = "service-account.json",
        project_id: str = "oamd-e-financeiro-pacto",
        dataset_id: str = "pacto_nectar_prod"
    ):
        self.credentials_file = credentials_file
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.client = None

    def initialize(self, logger) -> bool:
        try:
            private_key = os.environ.get("KEY_GCP_DATALAKE")
            if private_key:
                logger.info("Usando KEY_GCP_DATALAKE como credencial de ambiente")
                credentials_info = {
                    "type": "service_account",
                    "project_id": self.project_id,
                    "private_key_id": "REQUIRED_IF_NECESSARY",
                    "private_key": private_key.replace('\\n', '\n'),
                    "client_email": "<EMAIL>",
                    "client_id": "117321030275837789997",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": "https://accounts.google.com/o/oauth2/auth"
                }
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = bigquery.Client(project=self.project_id, credentials=credentials)
            else:
                logger.info(f"Tentando autenticar via arquivo: {self.credentials_file}")
                if os.path.exists(self.credentials_file):
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.credentials_file
                    self.client = bigquery.Client(project=self.project_id)
                else:
                    logger.error("❌ Arquivo de credenciais não encontrado.")
                    raise FileNotFoundError(f"Credencial não encontrada: {self.credentials_file}")
            logger.info("✅ BigQuery conectado com sucesso")
            return True
        except Exception as e:
            logger.error(f"Erro na conexão com o BigQuery: {str(e)}", exc_info=True)
            return False

    def _sanitize_field_name(self, field_name: str) -> str:
        import unicodedata
        field_name = unicodedata.normalize('NFKD', field_name).encode('ASCII', 'ignore').decode('ASCII')
        sanitized = re.sub(r'[^0-9a-zA-Z_]', '_', field_name.lower())
        return re.sub(r'_+', '_', sanitized).strip('_') or 'unnamed_field'

    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_', excluded_fields: List[str] = None) -> Dict:
        if excluded_fields is None:
            excluded_fields = []

        logger = setup_logging("bigquery_manager")
        flattened = {}
        filtered_d = {k: v for k, v in d.items() if k not in excluded_fields}

        for key, value in filtered_d.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key
            if isinstance(value, dict):
                flattened.update(self._flatten_dict(value, new_key, sep, excluded_fields))
            elif isinstance(value, list):
                flattened[new_key] = json.dumps(value, ensure_ascii=False)
            else:
                flattened[new_key] = value

        return flattened

    def _sanitize_data(self, data: List[Dict], logger, excluded_fields: List[str] = None) -> List[Dict]:
        sanitized = []

        # 1. Coleta todas as chaves possíveis nos dados
        all_keys = set()
        for item in data:
            flat = self._flatten_dict(item, excluded_fields=excluded_fields)
            all_keys.update(flat.keys())

        # 2. Para cada item, preenche todos os campos (com None onde faltar) e converte para string
        for item in data:
            flat = self._flatten_dict(item, excluded_fields=excluded_fields)
            completed = {k: flat.get(k, None) for k in all_keys}

            sanitized_item = {
                self._sanitize_field_name(k): str(v) if v is not None else None
                for k, v in completed.items()
            }
            sanitized.append(sanitized_item)

        logger.info(f"Total de registros sanitizados: {len(sanitized)}")
        return sanitized


    def get_last_processed_timestamp(self, table_id: str, logger) -> Optional[datetime]:
        if not self.client:
            logger.error("Cliente BigQuery não inicializado")
            return None
        query = f"""
        SELECT last_processed_timestamp
        FROM `{self.project_id}.{self.dataset_id}.incremental_state`
        WHERE table_name = '{table_id}'
        LIMIT 1
        """
        try:
            result = self.client.query(query).result()
            for row in result:
                return row.last_processed_timestamp
            return None
        except Exception as e:
            logger.error(f"Erro ao buscar timestamp incremental: {str(e)}", exc_info=True)
            return None

    def update_incremental_state(self, table_id: str, max_timestamp: str, logger) -> bool:
        if not self.client:
            logger.error("Cliente BigQuery não inicializado")
            return False
        query = f"""
        MERGE `{self.project_id}.{self.dataset_id}.incremental_state` AS target
        USING (SELECT '{table_id}' AS table_name, TIMESTAMP('{max_timestamp}') AS last_processed_timestamp, CURRENT_TIMESTAMP() AS last_run_at) AS source
        ON target.table_name = source.table_name
        WHEN MATCHED THEN
          UPDATE SET last_processed_timestamp = source.last_processed_timestamp, last_run_at = source.last_run_at
        WHEN NOT MATCHED THEN
          INSERT (table_name, last_processed_timestamp, last_run_at)
          VALUES (source.table_name, source.last_processed_timestamp, source.last_run_at)
        """
        try:
            self.client.query(query).result()
            logger.info(f"✅ Estado incremental atualizado: {table_id} → {max_timestamp}")
            return True
        except Exception as e:
            logger.error(f"Erro ao atualizar estado incremental: {str(e)}", exc_info=True)
            return False

    def load_data(
        self,
        table_id: str,
        data: List[Dict],
        logger,
        incremental_param: Optional[str] = None,
        excluded_fields: List[str] = None,
        write_mode: str = "DEFAULT",
        job_config: Optional[Dict] = None
    ) -> bool:
        def _split_chunks(lst, chunk_size):
            for i in range(0, len(lst), chunk_size):
                yield lst[i:i + chunk_size]

        if not self.client:
            raise ValueError("BigQuery client não inicializado")
        if not data:
            logger.info(f"Nenhum dado para carregar na tabela {table_id}")
            return True

        logger.info(f"Iniciando carga de {len(data)} registros na tabela {table_id}")

        if incremental_param:
            last_ts = self.get_last_processed_timestamp(table_id, logger)
            if last_ts:
                data = [
                    item for item in data
                    if datetime.fromisoformat(item.get(incremental_param).replace("Z", "+00:00")) > last_ts
                ]
                logger.info(f"{len(data)} registros após filtragem incremental")
            else:
                logger.info("Nenhum timestamp anterior. Todos os dados serão carregados.")

        sanitized_data = self._sanitize_data(data, logger, excluded_fields)

        if not sanitized_data:
            logger.warning("Nenhum dado sanitizado para carregar.")
            return False

        table_ref = self.client.dataset(self.dataset_id).table(table_id)

        # Define schema como STRING para todos os campos
        schema_fields = [
            bigquery.SchemaField(k, "STRING") for k in sanitized_data[0].keys()
        ]

        for i, chunk in enumerate(_split_chunks(sanitized_data, 10000)):
            config = bigquery.LoadJobConfig()
            config.autodetect = False
            config.ignore_unknown_values = True
            config.write_disposition = (
                "WRITE_TRUNCATE" if write_mode == "WRITE_TRUNCATE" and i == 0 else "WRITE_APPEND"
            )

            if job_config:
                for k, v in job_config.items():
                    setattr(config, k, v)

            try:
                logger.info(f"📦 Enviando chunk {i + 1} com {len(chunk)} registros")
                job = self.client.load_table_from_json(chunk, table_ref, job_config=config)
                job.result()
            except Exception as e:
                logger.error(f"Erro ao carregar chunk {i + 1}: {str(e)}", exc_info=True)
                return False


        logger.info(f"✅ Todos os dados carregados na tabela {table_id} ({len(sanitized_data)} registros)")

        if incremental_param:
            try:
                max_ts = max(item.get(incremental_param) for item in data)
                self.update_incremental_state(table_id, max_ts, logger)
            except Exception as e:
                logger.warning(f"Erro ao atualizar timestamp incremental: {e}")

        return True

