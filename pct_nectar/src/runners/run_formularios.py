import json
import sys
from pathlib import Path

current_dir = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(current_dir))

from src.api.api_client import EnhancedAPIClient
from src.api.bigquery_manager import BigQueryManager
from src.utils.logging_config import setup_logging

def run_formularios(config_path: str = str(Path(__file__).parent.parent.parent / "config" / "config.json")):
    logger = setup_logging("formularios")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)

        bq_manager = BigQueryManager(
            credentials_file=CONFIG["service_account_file"],
            project_id=CONFIG["project_id"],
            dataset_id="pacto_nectar_prod"
        )

        if not bq_manager.initialize(logger):
            raise Exception("BigQuery initialization failed")

        config = CONFIG["api"]["endpoints"]["formularios"]
        params = config["params"].copy()
        excluded_fields = config.get("excluded_fields", [])

        # Extrair dados com multiprocessamento
        api_client = EnhancedAPIClient()
        items = api_client.get_data_parallel(
            endpoint=config["path"],
            logger=logger,
            params=params,
            processes=5
        )

        # Carregar no BigQuery
        success = bq_manager.load_data(
            table_id="formularios",
            data=items,
            logger=logger,
            incremental_param=config.get("incremental_param"),  # Pode ser None ou "createdAt", etc.
            excluded_fields=excluded_fields,
            write_mode=config.get("write_mode", "WRITE_APPEND")
        )

        logger.info(
            f"\n📊 formularios Result:\n"
            f"- Registros: {len(items)}\n"
            f"- Status: {'✅ SUCCESS' if success else '❌ FAILURE'}"
        )
    except Exception as e:
        logger.critical(f"❌ Erro crítico no runner de formularios: {str(e)}", exc_info=True)

if __name__ == "__main__":
    run_formularios()
