import pandas as pd
import ssl
import requests
from pandas.io import gbq
from google.oauth2 import service_account
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
from requests import Request,Session
from extras.Pipeline import *
import traceback
from google.cloud.exceptions import NotFound
import urllib3
from http.client import HTTPConnection # py3
import os
import json
import numpy as np
import concurrent.futures
import collections
from termcolor import colored
import datetime
import time
import pytz
import pandas_gbq
import warnings
import random
import time
from extras.Pipeline import *

# Desabilitar a verificação de certificado SSL globalmente
ssl._create_default_https_context = ssl._create_unverified_context

urllib3.disable_warnings()
warnings.filterwarnings("ignore", category=FutureWarning, message="The argument 'date_parser' is deprecated and will be removed in a future version")


API_OAMD="https://app.pactosolucoes.com.br/oamd"
API_OAMD_DEV="http://***********:8085/NewOAMD"
API_ZW="https://app.pactosolucoes.com.br/app/UpdateServlet"
API_ZW_DEV="http://***********:8082/ZillyonWeb/UpdateServlet"
PG_PORT="5432"
ENDPOINT_CHAVES_REDE="{0}/prest/infra/hV0dU9aJ8dY3oE4qL2fD6jI0jF7fD2uQ?chaveRede={1}"
ENDPOINT_ZONAS_REDE="{0}/prest/infra/gM0tE3uY3bU0dL8oN2wJ3vN9fQ8jV8kR?chaveRede={1}"
ENDPOINT_TODAS_ZONAS="{0}/prest/infra/qN0zL3zW9lC8lT0eZ1jK6pB8lL3eN0mF"
LIMIT=50000

class Util:    
    
    def __init__(self, project_id, dataset_id, chave_rede, credentials, extract_context="ZW", base_path=None, dev=False, limit=0, location="US"):
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.chave_rede = chave_rede
        self.credentials = credentials
        pandas_gbq.context.credentials = credentials
        self.API_OAMD = API_OAMD
        self.API_ZW = API_ZW
        self.dev = dev
        self.location = location
        self.client = bigquery.Client(self.project_id, credentials=credentials, location=location)
        self.base_path = base_path
        self.LIMIT = limit if limit > 0 else LIMIT
        self.print_log(pd.__version__, "red")
        
        
        if extract_context == "ZW":
            if self.dev:
                self.API_OAMD = API_OAMD_DEV
                self.API_ZW = API_ZW_DEV
            
            if chave_rede != None:
                self.url_oamd_chaves_rede = ENDPOINT_CHAVES_REDE.format(self.API_OAMD, chave_rede)
                self.url_zonas_rede = ENDPOINT_ZONAS_REDE.format(self.API_OAMD, chave_rede)
            else:
                self.url_todas_zonas = ENDPOINT_TODAS_ZONAS.format(self.API_OAMD)        
                
            self.print_log("================================= DEBUG =================================", "green")
            self.print_log(f'self.API_OAMD {self.API_OAMD}', 'red')
            self.print_log(f'self.API_ZW {self.API_ZW}', 'red')
            self.print_log("================================= DEBUG =================================", "green")
                    
            if chave_rede != None:
                self.initEmpresas() 
            else:
                self.dict_zones = self.initZonas()
    
    def initEmpresas(self):
        self.print_log("Vou chamar INIT EMPRESAS: " + self.url_oamd_chaves_rede, 'green')
        self.dict_chaves_rede = json.loads(self.getdata(self.url_oamd_chaves_rede))
        self.print_log(f"Processar chaves: {self.dict_chaves_rede}", "blue")

        for i,value in self.dict_chaves_rede.items():
            self.chaves = ','.join(v for v in value)
            self.chaves_escaped = ','.join("'" + v + "'" for v in value)

        #GET ZONES BY CHAVE_REDE      

        self.print_log(f"Vou chamar ZONES by rede: {self.url_zonas_rede}", "blue")
        self.dict_zones=json.loads(self.getdata(self.url_zonas_rede))        
                
        #Sorting the list of dictionaries by 'host' in descending order
        sorted_list = sorted(self.dict_zones['infoInfraHostDTOList'], key=lambda x: x['host'], reverse=False)

        # Updating the original dictionary with the sorted list
        self.dict_zones['infoInfraHostDTOList'] = sorted_list        

        return self.dict_zones, self.chaves, self.chaves_escaped
    
    def initZonas(self):      
        self.chaves = ''  
        d = json.loads(self.getdata(self.url_todas_zonas))        
        #print("=================================START RETURN LISTA ZONAS=================================")        
        self.dict_zones = collections.OrderedDict(sorted(d.items()))         
        #print(self.dict_zones)        
        #print("=================================END RETURN LISTA ZONAS=================================")
        return self.dict_zones

    def getsql(self, name, pagination = False, limit = 0, offset = -1, incremental_query=False, field_id='codigo', max_actual=0):   
        filename = f"sql/{name}.sql"
        
        if self.base_path != None:
            filename = f"sql/{self.base_path}/{name}.sql"
            
        with open(filename, "r") as f:     
            content=f.read()
            if pagination and limit > 0 and offset > -1:
                #self.print_log(f"Getting sql query with pagination {offset} limit {limit}", "yellow")
                if incremental_query:                    
                    content = content.replace('{incremental_query}', 'and ({0} > {1})'.format(field_id, max_actual))
                    self.print_log(content, 'red')
                else:
                    content = content.replace('{incremental_query}', '')                
                
                content=content + f"\n offset {offset} limit {limit}"
            
            return content  

    def getdata(self, url):
        http = urllib3.PoolManager()

        response = http.request('POST', url)
        try:
            data = response.data
        except:
            print("Falha ao obter dados remotos (%s)" % traceback.format_exc())
        return data
    
    # Define the converter function for bool columns
    def initialize_bool(self, column):
        return False

    def custom_date_parser(self, x):
        formats = ['%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']  # Adicione outros formatos, se necessário       
        for fmt in formats:
            try:
                return pd.to_datetime(x, format=fmt, errors='coerce')
            except:
                #print("Falha ao obter dados remotos (%s)" % traceback.format_exc())
                continue
        return None
    
    # Função personalizada para converter os valores
    def converter_booleano(self, valor):
        if pd.isna(valor) or valor == "":  # Valores vazios ou NaN
            return False
        return str(valor).strip().lower() == "true"  # Converte True



# ---------------------------------------------------------------------------------------------------------------------

    def getDataInZone(self, name, zona, porta, url, prefix='bdzillyon', delim='\t', 
                    date_cols = [], 
                    date_time_cols = [], 
                    dict_other_types = {}, 
                    numeric_cols = [],
                    columns_drop=[],
                    host='localhost', 
                    user='zillyonweb', 
                    parquet_zw = False, 
                    pagination = False, 
                    upload=True, 
                    replace=False, 
                    executor=None, 
                    insert_as_rows=False):
        
        
        
        
        #print("Executando request em {0}".format(zona))
        #print('################################################################ {0} ############################################################## -> '.format(name))       
                
        inc = self.now()
        result=False            
        erro = None
        identificador = self.chaves
        params = [
            ("op", "selectALL" if parquet_zw == False else "selectParquet"),
            ("prefixoBanco", prefix), 
            ("format", "csv," + delim if parquet_zw == False else "parquet," + name),
            ("mimetype", "text/csv" if parquet_zw == False else "application/json"),
            ("hostPG", host),
            ("portaPG", porta),        
            ("userPG", user),
            ("pwdPG", "pactodb"),
            ("chavesOnly", self.chaves),                
            ("sql", self.getsql(name, pagination=pagination, limit=LIMIT, offset=0)),
            ("lgn", os.environ.get('LGN_KEY'))
        ]        
        
        if "/" in name:            
            name = os.path.basename(name)

        FINAL_CSV="{0}-{1}.csv".format(zona, name)
        
        if parquet_zw:            
            response = requests.post(url, data = params)
            print(colored("{0} ZONE: {1}".format(self.now(), f"Returned from {zona} selectParquet => content {response.text}"), "blue"))
        
        else:
            
            dtype = self.getSchema(name, prefixoBanco=prefix, schema_one=zona == "OAMD")                
        
            converters = None
            parse_dates = None
            
            if dtype != None:
                #self.print_log(f'####################### FOUNDED GetSchema to {FINAL_CSV} #######################', 'green')
                #self.print_log(f'####################### {dtype} #######################', 'green')
                bool_columns = {key: value for key, value in dtype.items() if value == 'bool'}
                #self.print_log(f"####################### bool_columns {bool_columns} #######################", "red")
                converters = {col: self.converter_booleano for col in bool_columns}
                
                # Separate datetime columns from the dtype list
                parse_dates = [col for col, dtype in dtype.items() if 'datetime64[ns]' in dtype]
                #self.print_log(f"####################### parse_dates {parse_dates} #######################", "red")
                
                for col in parse_dates:
                    del dtype[col]
                
            else:
                self.print_log(f'####################### WARNING!!! NOT FOUNDED GetSchema to {FINAL_CSV} IS EMPTY!!!! #######################', 'red')                    
                # It looks like the code you provided is not valid Python code. The first line "erro"
                # is not a valid Python statement, and the rest of the code is commented out using the
                # triple hash symbol "
                erro = f"NOT FOUNDED GetSchema to {FINAL_CSV} IS EMPTY!!!!"
            
            self.print_log(f'####################### GETTING DATA FROM ZW to {FINAL_CSV} #######################', 'green')
                                    
            response = requests.get(url, params=params, stream=True, verify=False)

            if response.status_code == 200:
                                
                with open(FINAL_CSV, "wb") as file:
                    for chunk in response.iter_content(chunk_size=131072):
                        file.write(chunk)

                #self.print_log(f"Downloaded file saved as: {FINAL_CSV}", 'blue')
            else:
                message = f"Failed to download file. Status code: {response.status_code}"
                self.print_log(message, 'red')
                erro = message
            
                
            # self.print_log(f'####################### END GETTING DATA FROM ZW to {FINAL_CSV} #######################', 'green')            
            
            if os.path.exists(FINAL_CSV) and os.path.getsize(FINAL_CSV) > 0:
                
                # self.print_log(f'####################### READING CSV TO DATAFRAME from {FINAL_CSV} #######################', 'green')
                
                df_iter = pd.read_csv(FINAL_CSV, encoding='iso-8859-1', sep=delim,
                                on_bad_lines='skip',
                                quotechar=None, quoting=3, 
                                low_memory=False, 
                                converters=converters,
                                date_parser=self.custom_date_parser,
                                #date_format='%Y-%m-%d %H:%M:%S.%f',
                                #date_format='%Y-%m-%d',
                                parse_dates=parse_dates,
                                dtype=dtype, 
                                chunksize=200000)
                
                self.print_log(f'####################### END READING CSV TO DATAFRAME from {FINAL_CSV} #######################', 'green')
                
                # Iterate over chunks and upload to BigQuery
                for i, df in enumerate(df_iter):
                
                    
                    df = self.apply_types(df, date_cols = date_cols, date_time_cols = date_time_cols,  dict_other_types = dict_other_types, numeric_cols = numeric_cols, columns_drop = columns_drop)                    
                    
                    # if i == 0:
                    #     self.print_log(df.info(max_cols=200), "green")
                    
                    self.print_log(f'####################### {FINAL_CSV} - chunk {i} - {df.shape[0]} rows returned #######################', 'yellow')
                                            
                    if upload:  
                        try:
                            ###self.verify_fields_integrity(df, table_id=table_id)
                            if replace or executor == None:                                
                                self.to_gbq(df, self.dataset_id, name, self.project_id, self.credentials, primeira=replace and i == 0, file_name=FINAL_CSV, insert_as_rows=insert_as_rows)
                            else:                            
                                executor.submit(self.to_gbq, df, self.dataset_id, name, self.project_id, self.credentials, primeira=replace and i == 0, file_name=FINAL_CSV, insert_as_rows=insert_as_rows)
                                
                            self.print_log(f'####################### {FINAL_CSV} - chunk {i} uploaded successfully #######################', 'green')
                            
                            result=True

                        except Exception as erro:
                            erro = erro    
                    
                    else:
                        if executor == None:
                            if FINAL_CSV != None and os.path.exists(FINAL_CSV):
                                os.remove(FINAL_CSV)                        
                
            else:
                self.print_log(f'####################### WARNING!!! CSV FILE {FINAL_CSV} NOT EXISTS OR EMPTY... #######################', 'red')                                            

                erro = f"WARNING!!! CSV FILE {FINAL_CSV} NOT EXISTS OR EMPTY"

                if os.path.exists(FINAL_CSV):

                    with open(FINAL_CSV, 'r', encoding='ISO-8859-1') as f:
                        self.print_log(f.read(), 'red')                
                    os.remove(FINAL_CSV)

        # lista = ["dadosmercado-415119", "dados-1a97fec7cf", "dados-22f2d0587a", "dados-8c7a9f06ab", "dados-20b256fe77", "dados-341b908afd"]
        
        # if self.project_id in lista: 
        # df não esta sendo instanciado aqui           
        #     tamanho_linhas = df.shape[0],
        #     tamanho_colunas = df.shape[1], 
        #     self.print_log(f"Identificador: {self.chaves}", 'blue') 
            
        #     pipeline_log = Log(self.project_id, zona, porta, url, name, erro, inc, tamanho_linhas, tamanho_colunas, self.chaves)  
            
        #     pipeline_log.logs() 
        
        return result  
        
    def getMaxActualId(self, table_id, field_id="codigo"):
        
        try:
            # Create a reference to the table
            table_ref = self.client.dataset(self.dataset_id, project=self.project_id).table(table_id)

            # Define and execute the SQL query to get the max ID
            query = f'SELECT MAX({field_id}) as max_id FROM `{table_ref}`'
            df_max_id = self.client.query(query).to_dataframe()

            # Extract the max ID value
            max_id = df_max_id['max_id'][0]
            self.print_log("MAX ID => " + str(max_id), 'blue')
            return str(max_id)
        except NotFound:
            # Handle case where the table does not exist
            self.print_log(f"Tabela '{table_id}' não encontrada. Retornando 0 como ID padrão.", 'yellow')
            return 0
        except Exception as e:
            # Handle other exceptions
            self.print_log(f"Erro em getMaxActualId: {traceback.format_exc()}", 'red')
            exit(1)
        
        return 0
        
    
    def getDataInZonePag(self, name, zona, porta, url, dtype, offset, page, prefix='bdzillyon', delim='\t', host='localhost', 
                         userPG='zillyonweb', incremental_query=False, field_id='codigo', max_actual=0, pwd='pactodb'):                            
        
        params = [
            ("op", "selectALL" ),
            ("prefixoBanco", prefix), 
            ("format", "csv," + delim),
            ("mimetype", "text/csv"),
            ("hostPG", host),
            ("portaPG", porta),        
            ("userPG", userPG),
            ("pwdPG", pwd),
            ("chavesOnly", self.chaves),                
            ("sql", self.getsql(name, pagination=True, limit=self.LIMIT, offset=offset, incremental_query=incremental_query, field_id=field_id, max_actual=max_actual)),
            ("lgn", os.environ.get('LGN_KEY'))
        ]
        
        if "/" in name:            
            name = os.path.basename(name)

        FINAL_CSV="{0}-{1}-{2}.csv".format(zona, name, page)        
    
        converters = None
        parse_dates = None
        
        if dtype != None:
            # self.print_log(f'####################### FOUNDED GetSchema to {FINAL_CSV} #######################', 'green')
            #self.print_log(f'####################### {dtype} #######################', 'green')
            bool_columns = {key: value for key, value in dtype.items() if value == 'bool'}
            #self.print_log(f"####################### bool_columns {bool_columns} #######################", "red")
            converters = {col: self.converter_booleano for col in bool_columns}
            
            # Separate datetime columns from the dtype list
            parse_dates = [col for col, dtype in dtype.items() if 'datetime64[ns]' in dtype]
            #self.print_log(f"####################### parse_dates {parse_dates} #######################", "red")
            
            for col in parse_dates:
                del dtype[col]
            
        else:
            self.print_log(f'####################### WARNING!!! NOT FOUNDED GetSchema to {FINAL_CSV} IS EMPTY!!!! #######################', 'red')                
            
        self.print_log(f'####################### GETTING DATA FROM ZW to {FINAL_CSV} #######################', 'green')
        
        response = requests.post(url, data = params)        
        
        data = response.content 
        
        with open(FINAL_CSV, 'wb') as s:
            s.write(data)
            
        self.print_log(f'####################### END GETTING DATA FROM ZW to {FINAL_CSV} #######################', 'green')            
        
        if os.path.getsize(FINAL_CSV) > 1024:
            
            self.print_log(f'####################### READING CSV TO DATAFRAME from {FINAL_CSV} #######################', 'green')
            
            df = pd.read_csv(FINAL_CSV, encoding='iso-8859-1', sep=delim,
                            on_bad_lines='skip',
                            quotechar=None, quoting=3, 
                            low_memory=False, 
                            keep_default_na=False,
                            converters=converters,
                            date_parser=self.custom_date_parser,                            
                            parse_dates=parse_dates,
                            dtype=dtype)            
            
            self.print_log(df.info(max_cols=200), "green")
            
            self.print_log(f'####################### END READING CSV TO DATAFRAME from {FINAL_CSV} #######################', 'green')
            print("")
            
            if not df.empty and df.shape[0] > 0:                        
                self.print_log(f'####################### {df.shape[0]} rows returned #######################', 'red')                
                return df
        else:
            self.print_log(f'####################### WARNING!!! CSV {FINAL_CSV} has response {response.text} #######################', 'red')                            

        return pd.DataFrame()
            
                
    
    def getDataInOamd2(self, fileCSV, query, delim = "\t"):
    
        print("Executando request em {0}".format(self.API_ZW))
        
        params = [
            ("op", "selectALL"), 
            ("prefixoBanco", "OAMD"), 
            ("format", "csv," + delim), 
            ("mimetype", "text/csv"),
            ("hostPG", "oamd.pactosolucoes.com.br"),
            ("portaPG", PG_PORT),        
            ("userPG", "postgres"),
            ("pwdPG", "pactodb"),    
            ("sql", query),
            ("lgn", os.environ.get('LGN_KEY'))
        ]
        
        response = requests.post(self.API_ZW, data = params)
        
        data = response.content    
        
        with open(fileCSV, 'wb') as s:
            s.write(data)
        
        if os.path.getsize(fileCSV) > 0:
            df = pd.read_csv(fileCSV, encoding='iso-8859-1', sep=delim, on_bad_lines='skip', skip_blank_lines=False)            
        else:
            print('####################### WARNING!!! CSV ', fileCSV, ' IS EMPTY!!!! #######################')
            return pd.DataFrame()
            
        return df
    
    def getDataInOamd(self, fileCSV, query, delim = "\t"):
    
        print("Executando request em {0}".format(self.API_ZW))        
        
        params = [
            ("op", "selectALL"), 
            ("prefixoBanco", "OAMD"), 
            ("format", "csv," + delim), 
            ("mimetype", "text/csv"),
            ("hostPG", "oamd.pactosolucoes.com.br"),
            ("portaPG", PG_PORT),        
            ("userPG", "postgres"),
            ("pwdPG", "pactodb"),    
            ("sql", query),
            ("lgn", os.environ.get('LGN_KEY'))
        ]
        
        response = requests.post(self.API_ZW, data = params)
        
        data = response.content    
        
        with open(fileCSV, 'wb') as s:
            s.write(data)

        if os.path.getsize(fileCSV) > 0:
            df = pd.read_csv(fileCSV, encoding='iso-8859-1', sep=delim, engine="python", on_bad_lines='skip')
        else:
            print('####################### WARNING!!! CSV ', fileCSV, ' IS EMPTY!!!! #######################')
            return pd.DataFrame()
        
        return df
    
    def extract_column_list(self, params):
        
        response = requests.post(self.API_ZW, data = params)
        
        #self.print_log(f"============ extract column list ============ {params}", 'blue')
        #self.print_log(response.text, 'blue')
                
        if response.text != '' and response.text.startswith("<!DOCTYPE html>"):
            return None
        
        columns_list = response.text.strip().replace(" ", "").replace("\n", "").replace("\r", "").replace("'", "").split(",")            
        
        if len(columns_list) > 1:
            ###self.print_log(f"====================== FOUNDED COLUMN LIST in CHAVE {chave} ======================", "yellow")
            ###self.print_log(response.text, "blue", print_time=False)

            # Create lists to store column names and dtypes
            column_names = []
            column_types = []                    

            for column in columns_list:
                name, dtype = column.split(":")
                column_names.append(name)
                column_types.append(dtype)

            # Create an empty DataFrame with the specified column names
            df = pd.DataFrame(columns=column_names)

            # Set the dtypes for each column
            df = df.astype(dict(zip(column_names, column_types)))
            
            #print(df.info())
            return dict(zip(column_names, column_types))
    
    def getSchema(self, query, prefixoBanco='bdzillyon', schema_one = False, host = "localhost", port=5432, userPG="postgres", pwdPG="pactodb"):
        
        dtype=None
        
        if schema_one:
            
            params = [
                ("op", "extractSchemaParquet"),
                ("format", "json"),
                ("hostPG", host),
                ("userPG", userPG),
                ("pwdPG", pwdPG),
                ("portaPG", port),
                ("bd", prefixoBanco),                
                ("sql", self.getsql(query, pagination=True, limit=1, offset=0)),
                ("lgn", os.environ.get('LGN_KEY'))
            ]
            
            dtype=self.extract_column_list(params)
        
        else:        
            
            if self.chaves == '':
                array_chaves = {"aca438e8c9e947e64db2236bb2f1f7a9"}
            else:    
                array_chaves = self.chaves.split(',')
            
            for chave in array_chaves:           
            
                params = [
                    ("op", "preencherSchemaParquet"),
                    ("prefixoBanco", prefixoBanco),
                    ("chave", chave),            
                    ("sql", self.getsql(query, pagination=True, limit=1, offset=0)),
                    ("lgn", os.environ.get('LGN_KEY'))
                ]
                
                #self.print_log(f"======================SEARCH COLUMN LIST from query {query} for CHAVE {chave}======================", "yellow")            
                
                dtype = self.extract_column_list(params)
                
                if dtype != None and len(dtype) > 0:
                    break
           
        return dtype

    def parseDateTime(self, df, colName):
        if not df.empty:
            df[colName] = pd.to_datetime(df[colName],format='%Y-%m-%d',errors='coerce')
        return df

    def parseTimestamp(self, df, colName):
        if not df.empty:
            df[colName] = pd.to_datetime(df[colName],format='%Y-%m-%d %H:%M:%S.%f', errors='coerce')
        return df
    
    def _do_upload(self, df, dataset_id, table_id, project_id, credentials, primeira=True, file_name=None, insert_as_rows=False):
        if not df.empty:            
            if primeira:                
                pandas_gbq.to_gbq(dataframe=df, destination_table=f'{dataset_id}.{table_id}', project_id=project_id, if_exists='replace', credentials=credentials)                
            else:                
                if insert_as_rows:
                    self.print_log(f"Inserting rows {file_name} as ROWS other times...", "green")                
                    # Convert DataFrame to a list of dictionaries
                    rows_to_insert = df.to_dict(orient="records")
                    
                    table_ref = f"{project_id}.{dataset_id}.{table_id}"

                    # Use BigQuery streaming API to append data
                    errors = self.client.insert_rows_json(table_ref, rows_to_insert)

                    # Check for errors
                    if errors:                        
                        self.print_log(f"Error inserting as rows {file_name}: {errors}", "red")
                    else:
                        print("Data successfully appended to the BigQuery table.")
                        self.print_log(f"Data inserted successfully {file_name}", "green")                        
                else:
                    #self.print_log(f"Inserting rows {file_name} as Pyarrow other time...", "green")                
                    pandas_gbq.to_gbq(dataframe=df, destination_table=f'{dataset_id}.{table_id}', project_id=project_id, if_exists='append', credentials=credentials)                        
        else:
            print('####################### WARNING!!! DATAFRAME ', table_id, ' IS EMPTY!!!! #######################')
            
    def backoff_delay(self):
        # Randomly pick a delay
        delay = random.randint(5, 30)
        print(f"Waiting for {delay} seconds...")
        time.sleep(delay)

    def to_gbq(self, df, dataset_id, table_id, project_id, credentials, primeira=True, file_name=None, insert_as_rows=False):
        
        max_retries = 30    
        
        # Set the desired logging level    
        if self.dev:
            import logging        
            logging.basicConfig(level=logging.DEBUG)  # or logging.DEBUG for more detailed output
            
        self.print_log(f"{self.now()} ####################### Uploading file {file_name} to {dataset_id}.{table_id} {primeira} #######################", "green")
            
        for attempt in range(max_retries):
            try:                
                self._do_upload(df, dataset_id, table_id, project_id, credentials, primeira=primeira, file_name=None, insert_as_rows=insert_as_rows)
                return  # Success, exit the loop
            except Exception as e:
                self.print_log(f"Error uploading {file_name} (attempt {attempt + 1}/{max_retries}): {e}", "yellow")
                # if "rateLimitExceeded" in str(e):
                    # Back off and retry after an exponential delay
                self.backoff_delay()
                # else:                    
                #     break
        else:
            self.print_log(f"Max retries reached for {file_name}. Unable to upload chunk after {max_retries} attempts.", "red")        
        
        if file_name != None and os.path.exists(file_name):
            os.remove(file_name)
            
    def drop_table(self, table_id):        
        try:
            table_ref = self.client.dataset(self.dataset_id).table(table_id)
            table = self.client.get_table(table_ref)
            self.client.delete_table(table)            
            print(colored(f"Deleted table {self.project_id}.{table_id}", "red"))
        except:
            self.print_log(f'Table {table_id} not exists')
        
            
    def apply_types(self, df, date_cols, date_time_cols, numeric_cols, dict_other_types, columns_drop):
        if not df.empty:
                df[date_cols] = df[date_cols].apply(lambda x: pd.to_datetime(x, format='%Y-%m-%d', errors='coerce'))
                df[date_time_cols] = df[date_time_cols].apply(lambda x: pd.to_datetime(x, format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'))
            
        if not df.empty and len(numeric_cols) > 0:
            for n_col in numeric_cols:
                df = pd.to_numeric(df[n_col], errors='ignore')
            
        if not df.empty and len(dict_other_types) > 0:
            df = df.astype(dict_other_types, errors='ignore')
        
        if not df.empty and len(columns_drop) > 0:
            df = df.drop(columns=columns_drop)
        
        return df
    
    def run_one_zone(self, dict_zone, table_id,
                            prefix='bdzillyon',
                            upload=True, 
                            date_cols = [], 
                            date_time_cols = [], 
                            dict_other_types = {}, 
                            numeric_cols = [],
                            columns_drop=[], 
                            replace = True,
                            print_info = True,
                            parquet_zw = False,                             
                            pagination = False,
                            executor=None, 
                            insert_as_rows=False):
        
        self.print_log(dict_zone, 'green')
        
        zona=dict_zone["descricao"]
        host=dict_zone["host"]
        porta=dict_zone["port"]
        url=dict_zone["urlZW"] + '/UpdateServlet'
        
        
        return self.getDataInZone(table_id, zona, porta, url, prefix=prefix, 
                            date_cols = date_cols, 
                            date_time_cols = date_time_cols, 
                            dict_other_types = dict_other_types, 
                            numeric_cols = numeric_cols,
                            columns_drop = columns_drop,
                            upload=upload,
                            host=host, 
                            parquet_zw=parquet_zw, 
                            pagination=pagination, 
                            replace=replace, 
                            executor=executor,
                            insert_as_rows=insert_as_rows)        
        
        
        # if parquet_zw == False and pagination==False:
            
        #     if "/" in table_id:            
        #         table_id = os.path.basename(table_id)
                
        #     #self.print_log(df.dtypes, "blue")            
            
        #     if upload:
                
        #         if replace and drop_before:
        #             self.drop_table(table_id=table_id)
                
        #         #self.verify_fields_integrity(df, table_id=table_id)
                
        #         print(colored(f"{self.now()} ============================= Uploading... {zona} {table_id} {replace} =============================", "green"))
        #         self.to_gbq(df, self.dataset_id, table_id, self.project_id, self.credentials, primeira=replace)
        #     del df          
        
            
    def print_log(self, message, color=None, print_time=True, ident=""):
        if print_time:
            print(colored(f"{self.now()} - {message}", color))
        else:
            print(colored(f"{ident}{message}", color))
            
    def verify_fields_integrity(self, df, table_id):
        
        self.print_log(f"============================= Verify fields integrity from table... {table_id} =============================")
        
        try:
            table_ref = self.client.dataset(self.dataset_id).table(table_id)
            table = self.client.get_table(table_ref)
            type_map = {
                "STRING": "object",
                "BYTES": "object",
                "INTEGER": "int64",
                "INTEGER": "Int64",
                "FLOAT": "float64",
                "BOOLEAN": "bool",
                "TIMESTAMP": "datetime64[ns, UTC]",
                "DATE": "datetime64[ns]",
                "TIME": "timedelta64[ns]",
            }
        
            # retrieve the BigQuery table schema using the client object
            table_ref = self.client.dataset(self.dataset_id).table(table_id)
            rows = self.client.list_rows(table_ref)
            schema_list = list(rows.schema)
            #self.print_log(schema_list, "yellow")
            
            
            if schema_list:
                table = self.client.get_table(table_ref)
                
                for i, column in enumerate(df.columns):                    
                    field = next(filter(lambda x: x.name == column, table.schema))
                    #self.print_log("FieldType: " + field.field_type, print_time=False, ident="\t")
                    field_type = type_map.get(field.field_type, "object")
                    
                    if field != None and str(df.dtypes[i]) != field_type and not field_type.__contains__("datetime"):                
                        self.print_log(f"Attention: column type from DataFrame {table_id} {column}: {df.dtypes[i]} to GBQ: {field_type} ...", "yellow")
                        # if replace:                        
                        #self.print_log(f"Forcing set {column} to type {field_type}", "red")
                        #df[column] = df[column].astype(field_type, errors="coerce")
        except Exception as e:
            #traceback.print_exc()
            self.print_log(f"Table {table_id} não existe! Não foi possível obter informações do schema", "red")
            
            
    def register_execution(self, table_id):
        client = bigquery.Client(credentials=self.credentials, project=self.project_id)
        table_ref = client.dataset(self.dataset_id).table('datas_execucoes')

        try:
            client.get_table(table_ref)

        except NotFound:
            print(f"\nA tabela {table_ref.path} não existe. Criando tabela...")

            schema = [
                bigquery.SchemaField("tabela", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("data_ultima_execucao", "TIMESTAMP", mode="REQUIRED"),
                bigquery.SchemaField("data_ultima_execucao_up", "STRING", mode="REQUIRED"),
            ]

            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Tabela {table_ref.path} criada com sucesso!\n")
            
        df = pandas_gbq.read_gbq(f"select * from {self.dataset_id}.datas_execucoes", project_id=self.project_id)
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao_up'] = self.now_as_string()        
        
        if not df['tabela'].isin([table_id]).any():
            new_row = {'tabela': table_id, 'data_ultima_execucao': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'data_ultima_execucao_up': self.now_as_string()}            
            
            if df.empty:
                df = pd.DataFrame(new_row, index=[0])                
            else:                               
                df = pd.concat([df, pd.DataFrame(new_row, index=[0])], ignore_index=True)

        df['data_ultima_execucao'] = pd.to_datetime(df['data_ultima_execucao'], utc=False)
        
        self.to_gbq(df, self.dataset_id, 'datas_execucoes', self.project_id, self.credentials, primeira=True)                
        self.print_log(f"\nData e hora '{table_id}' atualizado com sucesso em datas_execucoes", "green")
    
    def generate_data_all_zones(self, 
                                table_id,
                                prefix='bdzillyon',
                                upload=True, 
                                date_cols = [], 
                                date_time_cols = [], 
                                dict_other_types = {}, 
                                numeric_cols = [],
                                columns_drop = [],
                                parquet_zw = False,
                                parallel = False, 
                                pagination = False, 
                                insert_as_rows = False):
        
        
        
        if upload:
            self.drop_table(table_id)
            
        first_ok = False
        if parallel:            
            max_workers = self.max_threads()
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for ind, (key, infra) in enumerate(self.dict_zones.items()):                    
                    for idx, z in enumerate(infra):                        
                        #print(colored("{0} ZONE: {1}".format(self.now(), z["urlZW"]), "blue"))
                        if (idx == 0) or (first_ok == False):
                            ok = self.run_one_zone(z, table_id, prefix, upload=upload, date_cols=date_cols, 
                                                date_time_cols=date_time_cols, dict_other_types=dict_other_types, 
                                                numeric_cols=numeric_cols, columns_drop=columns_drop, replace=True, 
                                                parquet_zw=parquet_zw, pagination=pagination, executor=None, insert_as_rows=insert_as_rows)
                            if ok:
                                first_ok = True
                        else:
                            futures.append(executor.submit(self.run_one_zone, z, table_id, prefix, upload=upload, date_cols=date_cols, 
                                                            date_time_cols=date_time_cols, dict_other_types=dict_other_types, 
                                                            numeric_cols=numeric_cols, columns_drop=columns_drop, print_info=False, replace=False, 
                                                            parquet_zw=parquet_zw, pagination=pagination, executor=executor, insert_as_rows=insert_as_rows))
                            
                for future in futures:
                    try:
                        self.print_log(f"Return Future: {future.result()}", "green")
                        # if self.erro:
                        #     self.print_log(f"Return Error: {self.erro}", "red")
                    except Exception as e:
                        self.print_log(f"Error uploading chunk: {e}", "red")
                
        else:
            for ind, (key,infra) in enumerate(self.dict_zones.items()):                                                
                for idx, z in enumerate(infra):
                    #print(colored("{0} ZONE: {1}".format(self.now(), z["urlZW"]), "blue"))                    
                    self.run_one_zone(z, table_id, prefix, upload=upload, date_cols=date_cols, date_time_cols=date_time_cols, 
                                      dict_other_types=dict_other_types, numeric_cols=numeric_cols, columns_drop=columns_drop, 
                                      replace=idx == 0, parquet_zw=parquet_zw, pagination=pagination, insert_as_rows=insert_as_rows)
    
        if not parquet_zw and upload:
            self.register_execution(table_id)


    def generate_data_oamd(self, table_id, query, lst_date_times = (), lst_timestamps = (), dict_other_types = {}, columns_drop=(), upload=True):

        df = self.getDataInOamd2(table_id, query)
        
        for dt in lst_date_times:
            df = self.parseDateTime(df, dt)
            
        for ts in lst_timestamps:
            df = self.parseTimestamp(df, ts)
            
        if not df.empty and len(dict_other_types) > 0:
            df = df.astype(dict_other_types, errors='ignore')
        
        if not df.empty and len(columns_drop) > 0:
            for col in columns_drop:
                df.drop(col, axis=1)
                
        if upload:                        
            print(colored(f"{self.now()} ============================= Uploading... OAMD {table_id} True =============================", "green"))            
            self.drop_table(table_id)
            self.to_gbq(df, self.dataset_id, table_id, self.project_id, self.credentials, primeira=True)
            self.register_execution(table_id)

        del df
        
        
    def read_csv_and_upload(self, table_id, lst_date_times = (), upload=False, names=None, delim=','):

        self._read_csv_upload(table_id, f'/usr/src/app/{table_id}.csv', names=names, delim=delim, date_cols=lst_date_times, upload=upload)       
        
        
    def generate_data_oamd_pagination(self, table_id, prefix="OAMD", host="localhost", incremental_query=False, 
                                      field_id='codigo', drop_table=False, upload=True, port=5432, userPG='postgres', pwd='pactodb'):
        hasData=True
        sent=False
        offset = 0
        page = 1
        zona = "OAMD"

        print(colored(f"{self.now()} ============================= Getting... OAMD =============================", "green"))        
        
        dtype = self.getSchema(table_id, prefixoBanco=prefix, schema_one=True, host=host, port=port, userPG=userPG, pwdPG=pwd)
        
        if dtype != None:
            
            max_actual=0
            if incremental_query:
                max_actual = self.getMaxActualId(table_id=table_id, field_id=field_id)       
                
            while hasData:
                
                hasData=False
                
                page_str="{0}-{1}-{2}.csv".format(zona, table_id, page)

                df = self.getDataInZonePag(table_id, "OAMD", port, self.API_ZW, dtype.copy(), offset, page, userPG=userPG, prefix=prefix, host=host, 
                                        incremental_query=incremental_query, field_id=field_id, max_actual=max_actual, pwd=pwd)
                        
                if upload:
                    print(colored(f"{self.now()} ============================= Uploading... OAMD {page_str} True =============================", "green"))            
                    if drop_table and page==1:
                        self.drop_table(table_id)
                    self.to_gbq(df, self.dataset_id, table_id, self.project_id, self.credentials, primeira=drop_table and page==1)
                    sent=True
                    os.remove(page_str)
                
                if df.shape[0] >= self.LIMIT:
                    hasData=True
                    offset+=self.LIMIT
                    page+=1
                
                del df
            
            if sent:
                self.register_execution(table_id)
        
    def _read_csv_upload(self, table_id, fileName, date_cols = [], 
                        date_time_cols = [], 
                        dict_other_types = {}, 
                        columns_drop=[], delim='\t', names=None, upload=False):
        
        primeira=True
        
        for df in pd.read_csv(fileName, encoding='iso-8859-1', sep=delim, on_bad_lines='skip', 
                            quotechar=None, quoting=3, low_memory=False, names=names, chunksize=200000):
        
            if not df.empty and len(date_cols) > 0:
                df[date_cols] = df[date_cols].apply(lambda x: pd.to_datetime(x, format='%Y-%m-%d', errors='coerce'))
            
            if not df.empty and len(date_time_cols) > 0:
                df[date_time_cols] = df[date_time_cols].apply(lambda x: pd.to_datetime(x, format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'))        
                
            if not df.empty and len(dict_other_types) > 0:
                df = df.astype(dict_other_types, errors='ignore')
            
            if not df.empty and len(columns_drop) > 0:
                df = df.drop(columns=columns_drop)
                
            df.info()
            
            if not df.empty and upload:                        
                print(colored(f"{self.now()} ============================= Uploading... {self.project_id}/{table_id} True =============================", "green"))            
                if primeira:
                    self.drop_table(table_id)
                self.to_gbq(df, self.dataset_id, table_id, self.project_id, self.credentials, primeira=primeira)            
                primeira=False            
            
            
    def now(self):
        return datetime.datetime.now(pytz.timezone('America/Sao_Paulo'))
    
    def now_as_string(self):
        return self.now().strftime("%Y-%m-%d %H:%M:%S.%f%z")
    

    def verificar_dataset(self, dataset_name):
        dataset_id = f"{self.project_id}.{dataset_name}"
        
        create_dataset_query = f"""
            CREATE SCHEMA IF NOT EXISTS `{dataset_id}`
            OPTIONS(
                location="{self.location}"
            )
        """


        # Run the query to create the dataset
        pandas_gbq.read_gbq(create_dataset_query, project_id=self.project_id, progress_bar_type=None)
        
    def max_threads(self):
        # now = self.now()
        # return now.hour >= 19 or now.hour < 5 or now.weekday() == 6
        # Evict error :
        # Error uploading chunk: Reason: 403 Exceeded rate limits: too many table update operations for this table. For more information, see https://cloud.google.com/bigquery/docs/troubleshoot-quotas; reason: rateLimitExceeded, location: table.write, message: Exceeded rate limits: too many table update operations for this table. For more information, see https://cloud.google.com/bigquery/docs/troubleshoot-quotas
        max_threads = 8 if os.cpu_count() > 8 else os.cpu_count()
        self.print_log(f"====== MAX_THREADS: {max_threads} ====== ")
        return max_threads

    
    def create_view(self, view_table='views', create_unidades: bool = True):
        self.verificar_dataset(view_table)

        queries = {
            "view_Finan_Lancamentos": f"""
                CREATE OR REPLACE VIEW `{view_table}.view_Finan_Lancamentos` AS
                SELECT 
                finan.*,
                unidade.*
                FROM `{self.project_id}.{self.dataset_id}.finan_lancamentos` finan
                join `{self.project_id}.{view_table}.view_Unidades` unidade 
                            on finan.cod_empresafinanceiro = unidade.Unidade_IdPacto
                """,
            
            "view_Marketing": f"""
                CREATE OR REPLACE VIEW `{view_table}.view_Marketing` AS
                SELECT 
                CONCAT(mk.cod_empresafinanceiro,'-',mk.codigopessoa) as id_Pessoa,
                CONCAT(mk.cod_empresafinanceiro,'-',per.matricula) as id_Matricula,
                CONCAT(mk.cod_empresafinanceiro,'-',mk.contrato) as id_Contrato,
                CASE
                WHEN mk.tiporegistro = 'CONTRATO'      THEN CONCAT(mk.cod_empresafinanceiro,'-C-',mk.contrato)
                WHEN mk.tiporegistro = 'VENDA_AVULSA'  THEN CONCAT(mk.cod_empresafinanceiro,'-A-',mk.vendaavulsa)
                WHEN mk.tiporegistro = 'OUTRAS_VENDAS' THEN CONCAT(mk.cod_empresafinanceiro,'-O-',mk.data)
                ELSE null
                END as id_Venda,
                mk.*,
                unidade.*,
                per.matricula
                FROM `{self.project_id}.{self.dataset_id}.marketing` mk
                JOIN `{self.project_id}.{view_table}.view_Unidades` unidade
                ON mk.cod_empresafinanceiro = unidade.Unidade_idPacto
                LEFT JOIN `{self.project_id}.{self.dataset_id}.person` per 
                ON CONCAT(mk.cod_empresafinanceiro, '-', mk.codigopessoa) = per.id_pessoa
                """,

            "view_Tpv_Previsao": f"""
                CREATE OR REPLACE VIEW `{view_table}.view_Tpv_Previsao` AS
                SELECT
                tpv.*,
                CONCAT(tpv.empresa_financeiro,'-',tpv.contrato)      as Id_Contrato, 
                CONCAT(tpv.empresa_financeiro,'-',pes.matricula )    as Id_Matricula, 
                CONCAT(tpv.empresa_financeiro,'-',tpv.venda_avulsa)  as Id_VendaAvulsa,  
                CONCAT(tpv.empresa_financeiro,'-',tpv.pessoa)        as Id_Pessoa,
                -- CONCAT(tpv.empresa_financeiro,'-',COALESCE(tpv.contrato,0),'-',COALESCE(tpv.venda_avulsa,0)) as Id_Venda,
                unidade.*,
                pes.matricula        as Cliente_Matricula,
                pes.situacao         as Cliente_Situacao,
                pes.situacaocontrato as Cliente_SituacaoContrato, 
                FROM `{self.project_id}.{self.dataset_id}.tpv-previsao` tpv 
                join `{self.project_id}.{view_table}.view_Unidades` unidade on tpv.empresa_financeiro = unidade.Unidade_idPacto
                left join `{self.project_id}.{self.dataset_id}.person` pes on CONCAT(tpv.empresa_financeiro,'-',tpv.pessoa)  = pes.id_pessoa
                """,

            "view_person": f"""
                CREATE OR REPLACE VIEW `{view_table}.view_person` AS
                -- VERSÃO NOVA 
                SELECT 
                pes.*,
                comp.dt_competencia as competencia_mes,
                comp.soma_valor as competencia_valor,
                inad.inadimplencia_Parcelas,
                inad.inadimplencia_4_90_Valor,
                unidade.*
                FROM `{self.project_id}.{self.dataset_id}.person` pes
                join `{self.project_id}.{view_table}.view_Unidades` unidade on pes.cod_empresafinanceiro = unidade.Unidade_IdPacto
                left join 
                        (SELECT -- TRAS O VALOR DE COMPENTENCIA PARA O ALUNO NO MES CORRENTE
                        CONCAT(vd.empresa_financeiro,'-',vd.pessoa) as id_Pessoa,
                        vd.dt_competencia,
                        sum(vd.valor) as soma_valor
                        FROM `{self.project_id}.{self.dataset_id}.tpv-previsao` vd
                        where 1=1
                                    and ( date(vd.dt_competencia) = date(DATETIME_TRUNC(CURRENT_DATE(), MONTH) ))
                                    and vd.parcela_status not in ('CA')
                                    and vd.tipo_registro = 'FATURAMENTO'
                                --    and CONCAT(vd.empresa_financeiro,'-',vd.pessoa) in ('8452-9','8242-103685','8242-86206','8452-289','8242-72940','8242-100945')
                        group by 1,2) comp on pes.id_pessoa = comp.id_pessoa
                    left join 
                        (SELECT -- TRAS PARCELA EM ABERTO E VENCIDAS DE 4 A 90 DIAS
                            CONCAT(mp.empresa_financeiro,'-',mp.pessoa) as id_Pessoa,
                            count(*) as inadimplencia_Parcelas,
                            sum(mp.valor) as inadimplencia_4_90_Valor
                        FROM `{self.project_id}.{self.dataset_id}.tpv-previsao` mp
                        where 1=1
                            and mp.tipo_registro = 'PREVISAO'
                            and mp.parcela_status in ('EA')
                            and date_diff( CURRENT_DATE(),DATE(mp.dt_vencimento) , DAY ) between 4 and 90 
                        --  and CONCAT(mp.empresa_financeiro,'-',mp.pessoa) in ('8245-10188','8242-103685','8242-73617')
                        group by 1) inad on pes.id_pessoa = inad.id_pessoa 
                --  where pes.id_pessoa = '8242-78590'
                --     or pes.id_pessoa in ('8245-10188','8242-103685','8242-73617')
                """,
 


            "view_DadosGerenciaisPmg": f"""
                CREATE OR REPLACE VIEW `{view_table}.view_DadosGerenciaisPmg` AS
                SELECT 
                pmg.*,
                rede.*,
                ud.UltimaDataFim,
                CASE
                    WHEN ud.UltimaDataFim = pmg.datapesquisafim THEN Valor ELSE Null
                END as ValorUltimaDataFim 
                FROM `{self.project_id}.{self.dataset_id}.dadosgerenciais` pmg 
                join `{self.project_id}.{view_table}.view_Unidades` rede on pmg.cod_empresafinanceiro = rede.Unidade_IdPacto
                left join ( -- Para pegar a Ultima data Fim --------------------------------------
                        SELECT 
                            cod_empresafinanceiro as ud_ep,
                            identificador as Id,
                            periodicidade as udPeri,
                            FORMAT_DATE('%Y%m',x1.datapesquisafim) as AnoMes,
                            max(x1.datapesquisafim) as UltimaDataFim
                        FROM `{self.project_id}.{self.dataset_id}.dadosgerenciais` x1 
                        -- where x1.identificador = 'REP'
                        group by 1,2,3,4      -- ORDER BY 1,2,3,4
                            ) ud on ( (ud.ud_ep = pmg.cod_empresafinanceiro) and  (ud.udPeri = pmg.periodicidade) and
                                    (ud.Id = pmg.identificador) and (ud.AnoMes = FORMAT_DATE('%Y%m',pmg.datapesquisafim) ))
                --WHERE rede.Unidade_IdPacto in ( 8429 )
            """,
            
            "view_Acessos": f"""
                    CREATE OR REPLACE VIEW `{view_table}.view_Acessos` AS

                    WITH AcessosNumerados AS (
                    SELECT
                        ace.*,
                        unidade.*,
                        pes.nomecliente AS Cliente_NomeCliente,
                        pes.matricula AS Cliente_Matricula,
                        pes.situacao AS Cliente_Situacao,
                        pes.situacaocontrato AS Cliente_SituacaoContrato,
                        pes.descricoesmodalidades AS Cliente_DescricaoModalidades,
                        pes.nomeplano AS Cliente_NomePlano,
                        pes.colaboradores AS Cliente_ColaboradoresCarteira,
                        pes.idade AS idade,
                        pes.situacaomatriculacontrato as Cliente_SituacaoMatricula,
                        pes.frequenciasemanal as frequenciasemanal,
                        pes.duracaocontratomeses as duracaocontratomeses,
                        TIME(TIMESTAMP_TRUNC(ace.ac_dthrentrada, SECOND)) AS horario_entrada,
                        DATE(ace.ac_dthrentrada) AS data_entrada,
                        CASE
                        WHEN EXTRACT(HOUR FROM TIME(TIMESTAMP_TRUNC(ace.ac_dthrentrada, SECOND))) BETWEEN 3 AND 10 THEN "Período Manhã"
                        WHEN EXTRACT(HOUR FROM TIME(TIMESTAMP_TRUNC(ace.ac_dthrentrada, SECOND))) BETWEEN 11 AND 17 THEN "Período Tarde"
                        ELSE "Período Noite"
                        END AS periodo_do_dia,
                        CASE
                        WHEN pes.idade BETWEEN 0 AND 10 THEN '0-10 anos'
                        WHEN pes.idade BETWEEN 11 AND 16 THEN '11-16 anos'
                        WHEN pes.idade BETWEEN 17 AND 22 THEN '17-22 anos'
                        WHEN pes.idade BETWEEN 23 AND 30 THEN '23-30 anos'
                        WHEN pes.idade > 30 THEN CONCAT((FLOOR((pes.idade - 30) / 5) * 5) + 30, '-', (FLOOR((pes.idade - 30) / 5) * 5) + 35, ' anos')
                        END AS faixa_idade,
                        CASE EXTRACT(DAYOFWEEK FROM TIMESTAMP(ace.ac_dthrentrada))
                        WHEN 1 THEN '6 - Domingo'
                        WHEN 2 THEN '0 - Segunda'
                        WHEN 3 THEN '1 - Terça'
                        WHEN 4 THEN '2 - Quarta'
                        WHEN 5 THEN '3 - Quinta'
                        WHEN 6 THEN '4 - Sexta'
                        WHEN 7 THEN '5 - Sábado'
                        ELSE NULL
                        END AS dia_da_semana,
                        ROW_NUMBER() OVER (PARTITION BY pes.codigocliente, DATE(ace.ac_dthrentrada) ORDER BY ace.ac_dthrentrada) AS num_linha
                    FROM `{self.project_id}.{self.dataset_id}.acessos` ace
                        JOIN `{self.project_id}.{view_table}.view_Unidades` unidade ON ace.e_empresafinanceiro = unidade.Unidade_idPacto
                        LEFT JOIN `{self.project_id}.{self.dataset_id}.person` pes ON CONCAT(ace.e_empresafinanceiro,'-',ace.s_codigocliente) = CONCAT(pes.cod_empresafinanceiro,'-',pes.codigocliente)
                    )
                    SELECT *
                    FROM AcessosNumerados
                    WHERE num_linha = 1
                    -- AND pes.vinculo IS NOT NULL;
            """,
            
                
            
            # "view_CRM": f"""
            #     CREATE OR REPLACE VIEW `views.view_CRM` AS
            #     SELECT 
            #     crm.*,
            #     rede.nomeempresazw as Unidade,
            #     rede.cidade as Unidade_Cidade,
            #     rede.estado as Unidade_Estado,
            #     rede.codigorede as Unidade_CodigoRede,
            #     rede.cnpj as Unidade_cnpj,
            #     rede.grupofavorecido,
            #     case when crm.repescagem then 1 else 0 end as repescagem_numero,
            #     case when crm.obtevesucesso then 1 else 0 end as sucesso_numero,
            #     case when crm.obtevesucesso then 1.0 else 0 end as sucesso_porcentagem,
            #     1 as Meta_numero,
            #     FROM `{self.project_id}.{self.dataset_id}.crm` crm
            #     left join `{self.project_id}.{self.dataset_id}.rede_empresa_financeiro` rede on crm.cod_empresafinanceiro = rede.codigofinanceiro
            #     """,


            # "view_Treino_Predefinido": f"""
            #     CREATE OR REPLACE VIEW `views.view_Treino_Predefinido` AS
            #     SELECT 
            #     tp.*,
            #     rede.nomeempresazw   as Unidade_Nome,
            #     rede.cidade          as Unidade_Cidade,
            #     rede.estado          as Unidade_Estado,
            #     rede.grupofavorecido as Unidade_GrupoFavorecido
            #     FROM `{self.project_id}.{self.dataset_id}.treinos_predefinidos` tp
            #     left join `{self.project_id}.{self.dataset_id}.rede_empresa_financeiro` rede on tp.cod_empresafinanceiro = rede.codigofinanceiro
            #     """

        }
        if create_unidades:
            queries["view_Unidades"] = f"""
                CREATE OR REPLACE VIEW `{view_table}.view_Unidades` AS
                SELECT  
                CASE rede.codigofinanceiro 
                    WHEN 8690 THEN 'EC - VILLAGIO, RS'
                    WHEN 8684 THEN 'EC - LOURDES, RS'
                    ELSE rede.nomeempresazw end as Unidade,
                -- rede.nomeempresazw   as Unidede,
                rede.cnpj as Unidade_cnpj,
                rede.cidade as Unidade_Cidade,
                rede.estado as Unidade_Estado,
                CASE rede.codigofinanceiro 
                    WHEN 8690 THEN 4
                    WHEN 8684 THEN 96
                    ELSE rede.codigorede end as Unidade_IdRede,
                -- rede.codigorede as Unidade_IdRede,
                rede.codigofinanceiro as Unidade_IdPacto,
                rede.metragem as Unidade_Metragem,
                case 
                    when rede.tipoempresa = 'USO_INTERNO' then 'Usu Interno'
                    when (rede.inicioproducao is not null and DATE(rede.inicioproducao) < CURRENT_DATE()) then 'BI Ativo'
                    else 'Em Validação' end as Unidade_Status
                FROM `{self.project_id}.{self.dataset_id}.rede_empresa_financeiro` rede
            """
        
        for view_name, query in queries.items():
            try:
                # Executa as queries definidas no dicionário
                self.client.query(query).result()
                print(colored(f"Query bem sucedida: {view_name}", "green"))
            except Exception as e:
                print(colored(f"Query falhada: {view_name}", "red"))
                print(colored(str(e), "red"))

        
    # run specific queries from array comma separated
    def run_queries(self, parallel=True, upload=True):
        if os.environ.get('QR'):
          qr = os.environ.get('QR').split(sep=',')
        for q in qr:
            self.print_log(f'Running specific query from QR variable => {q}', "blue")
            self.generate_data_all_zones(q, parallel=parallel, upload=upload)


