import pandas as pd
from json import dumps
from httplib2 import Http
from gitlab import Gitlab
from gitlab.exceptions import GitlabAuthenticationError, GitlabGetError
import pandas as pd
from pandas.io import gbq
from google.oauth2 import service_account
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
# from extras.Pipeline import *
from http.client import HTTPConnection # py3
import os
import numpy as np
import datetime
import pytz
import pandas_gbq


# ------------------
# KEYS TO GITLAB...

gitlab_url = "https://gitlab.com"
access_token = "**************************"
project_path = "Plataformazw/pacto-pydata-eng"

### credential to pipeline logs dataset into dadosmercado-415119 project 
credentials_log = service_account.Credentials.from_service_account_info(
        {
        "type": "service_account",
        "project_id": "dadosmercado-415119",
        "private_key_id": "1ad17cf6a3324b511b5e936be7b587f2b3471595",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "104771537170357748391",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/dados-mercado%40dadosmercado-415119.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
        }

)

def last_job(gitlab_url, access_token, project_path):
    """
    Retrieve information about the last job executed in a GitLab pipeline.

    Parameters:
        gitlab_url (str): URL of the GitLab instance.
        access_token (str): Personal access token for GitLab API.
        project_path (str): Path of the project in the format 'owner/project'.

    Returns:
        pd.DataFrame: DataFrame containing information about the last job,
                      or None if no pipelines are found.
    """
    try:
        # Connecting to GitLab
        gl = Gitlab(gitlab_url, private_token=access_token)

        # Obtaining the latest pipeline
        project = gl.projects.get(project_path)
        latest_pipeline = project.pipelines.list(order_by='id', sort='desc', per_page=1, get_all=False)[0]

        if not latest_pipeline:
            print("No pipelines found.")
            return None

        # Retrieving job details for the latest pipeline
        latest_job = latest_pipeline.jobs.list()[0]
        full_job = gl.projects.get(project_path).jobs.get(latest_job.id)

        # Getting user information
        user = gl.users.get(full_job.user['id'])
        user_email = user.username
        print(full_job.status)
        print(type(full_job.status))

        job_info = {
            'Pipeline_ID': latest_pipeline.id,
            'Pipeline_Date': latest_pipeline.created_at,
            'Job_ID': full_job.id,
            'Job_Date': full_job.created_at,
            'Job_Name': full_job.name,
            'Job_Logs': full_job.trace(),
            'Job_Status': full_job.status,
            'User_Email': user_email
        }
        print(job_info)
        return pd.DataFrame([job_info])

    except GitlabAuthenticationError:
        print("Authentication failed. Please check your access token.")
        return None
    except GitlabGetError as e:
        print(f"Error occurred while fetching data from GitLab: {e}")
        return None

class Log:

    def __init__(self, PROJECT_ID, zona, porta, url, name, erro, timer, tamanho_linhas, tamanho_colunas, chaves):
        self.staging_file = 'staging_pipelines.csv'
        self.project_id = PROJECT_ID
        self.chaves = chaves
        self.zona = zona
        self.porta = porta
        self.url = url
        self.name = name
        self.erro = erro
        self.inc = timer
        self.tamanho_linhas = tamanho_linhas
        self.tamanho_colunas = tamanho_colunas
        self.data = {}
        self.gitlab_url = "https://gitlab.com"
        self.access_token = "**************************"
        self.project_path = "Plataformazw/pacto-pydata-eng"
        self.credentials_log = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': 'academia-de-verdade',
                        'private_key_id': '5bb5e37635dba35c47f7d7a6bd09d2496d56a210',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110607617536668275794",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/academia-de-verdade%40academia-de-verdade.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )
    def now(self):
        return datetime.datetime.now(pytz.timezone('America/Sao_Paulo'))

    def logs(self):

        print("#############################-log-################################")
        

        pipeline_id = os.getenv('CI_PIPELINE_ID')
        print(f"Pipeline ID: {pipeline_id}")
        fim = self.now()
        inc = self.inc

        time_diff = fim - inc
        time_diff = time_diff.total_seconds() / 60

        data = {
            "projeto": self.project_id,
            "data_inicio": inc,
            "data_termino": fim,
            "tempo_execucao": time_diff,
            "tamanho_linhas": self.tamanho_linhas,
            "tamanho_colunas": self.tamanho_colunas,
            "zona": self.zona,
            "url": self.url,
            "name": self.name, 
            "porta": self.porta,
            "erro": self.erro
        }
        # ----------------------------------------------------------------------
        dados = pd.DataFrame([data])
        temp = last_job(self.gitlab_url, self.access_token, self.project_path)
        dados = pd.concat([dados, temp], axis=1)
        dados = dados.astype("str")
        
        # ----------------------------------------------------------------------
        
        data_temp = pd.DataFrame(columns=['_chave', "data"])
        chaves = self.chaves.split(',')
        for i in chaves:
            new_row = pd.DataFrame([[i, inc]], columns=data_temp.columns)
            data_temp = pd.concat([data_temp, new_row], ignore_index=True)

        # ----------------------------------------------------------------------
        
        destination_table = "log.Log_History"
        destination_table_debug = "log.Log_dataexecucao"
        
        pandas_gbq.to_gbq(dados, destination_table, project_id="dadosmercado-415119", credentials=credentials_log, if_exists="append")
        pandas_gbq.to_gbq(data_temp, destination_table_debug, project_id="dadosmercado-415119", credentials=credentials_log, if_exists="append")

    def def_to_gbq(self, dados, destination_table):
        
        # Lê o CSV de staging existente
        try:
            staging_data = pd.read_csv(self.staging_file)
        except pd.errors.EmptyDataError:
            staging_data = pd.DataFrame()
        
        # Adiciona os novos dados ao DataFrame de staging
        staging_data = pd.concat([staging_data, dados], ignore_index=True)
        
        if len(staging_data) >= 100:
            # Envia os dados para o Google BigQuery
            pandas_gbq.to_gbq(
                staging_data, 
                destination_table, 
                project_id=self.project_id, 
                credentials=self.credentials, 
                if_exists="append"
            )
            # Limpa o CSV de staging
            pd.DataFrame().to_csv(self.staging_file, index=False)
        else:
            # Salva os dados no CSV de staging
            staging_data.to_csv(self.staging_file, index=False)