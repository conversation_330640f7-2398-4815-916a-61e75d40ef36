import pandas as pd
import requests
from pandas.io import gbq
from google.oauth2 import service_account
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
from requests import Request,Session
import urllib3
from http.client import HTTPConnection # py3
import os
import numpy as np
from termcolor import colored
import datetime
import pytz
import pandas_gbq


urllib3.disable_warnings()

class Colab:
    data_atual = datetime.datetime.now()
    if data_atual.hour < 3:
        data_atual -= datetime.timedelta(days=1)
    data_execucao = data_atual.replace(hour=(data_atual.hour - 3) % 24)

    API_ZW = "https://app.pactosolucoes.com.br/app/UpdateServlet"

    PROJECT_ID = "oamd-e-financeiro-pacto"
    DATASET_ID = "oamd_financeiro"

    CREDENTIALS = service_account.Credentials.from_service_account_info(
        {
            "type": "service_account",
            "project_id": "oamd-e-financeiro-pacto",
            "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
            "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
            "client_email": "<EMAIL>",
            "client_id": "117321030275837789997",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com",
        }
    )
    

    def __init__(self):
        self.api_zw = Colab.API_ZW
        self.project_id = Colab.PROJECT_ID
        self.dataset_id = Colab.DATASET_ID
        self.credentials = Colab.CREDENTIALS
        

    def conectar_postgresql(self, QUERY_COPY, FINAL_CSV):
        # Extrair dados pelo ZW usando exportador para CSV do PostgreSQL
        payload = {
            "op": "selectONE",
            "bd": "OAMD",
            "hostPG": "oamd.pactosolucoes.com.br",
            "portaPG": "5432",
            "userPG": "postgres",
            "pwdPG": "pactodb",
            "sql": f"COPY ({QUERY_COPY}) TO '/opt/NFS/ZW_ARQ/{FINAL_CSV}' WITH (FORMAT csv, DELIMITER ';', ENCODING UTF8, HEADER);",
            "lgn": os.environ.get('LGN_KEY')
        }
        response = requests.post(self.api_zw, data=payload)
        response.raise_for_status()

        # Baixar o arquivo gerado pelo ZW
        payload = {
            "op": "downloadfilediretorioarquivos",
            "file": FINAL_CSV,
            "mimetype": "text/csv"
        }
        response = requests.post(self.api_zw, data=payload)
        response.raise_for_status()
        with open(FINAL_CSV, "wb") as f:
            f.write(response.content)


    def convert_to_datetime(self, df, date_columns):
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors="coerce")
        return df
    

    def to_gbq(self, df, dataset_id, table_id, project_id, credentials, primeira=True, generated_schema=None):
        if not df.empty:
            if primeira:
                df.to_gbq(destination_table=f'{dataset_id}.{table_id}', project_id=project_id, if_exists='replace', credentials=credentials, table_schema = generated_schema)
                primeira=False
            else:
                df.to_gbq(destination_table=f'{dataset_id}.{table_id}', project_id=project_id, if_exists='append', credentials=credentials, table_schema = generated_schema)
        else:
            print('####################### WARNING!!! DATAFRAME ', table_id, ' IS EMPTY!!!! #######################')
    

    def register_execution(self, table_id):
        client = bigquery.Client(credentials=self.credentials, project=self.project_id)
        table_ref = client.dataset(self.dataset_id).table('datas_execucoes')

        try:
            client.get_table(table_ref)

        except NotFound:
            print(f"\nA tabela {table_ref.path} não existe. Criando tabela...")

            schema = [
                bigquery.SchemaField("tabela", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("data_ultima_execucao", "TIMESTAMP", mode="REQUIRED"),
                bigquery.SchemaField("data_ultima_execucao_up", "STRING", mode="REQUIRED"),
            ]

            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Tabela {table_ref.path} criada com sucesso!\n")
            
        df = pandas_gbq.read_gbq(f"select * from {self.dataset_id}.datas_execucoes", project_id=self.project_id)
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao_up'] = datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")       
        
        if not df['tabela'].isin([table_id]).any():
            new_row = {'tabela': table_id, 'data_ultima_execucao': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'data_ultima_execucao_up': datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")}      
            
            if df.empty:
                df = pd.DataFrame(new_row, index=[0])                
            else:                               
                df = pd.concat([df, pd.DataFrame(new_row, index=[0])], ignore_index=True)

        df['data_ultima_execucao'] = pd.to_datetime(df['data_ultima_execucao'], utc=False)
        
        self.to_gbq(df, self.dataset_id, 'datas_execucoes', self.project_id, self.credentials, primeira=True)                
        print(f"\nData e hora '{table_id}' atualizado com sucesso em datas_execucoes", "green")


    def upload_to_gbq(self, df, table_id):
        df.to_gbq(
            destination_table=self.dataset_id + "." + table_id,
            project_id=self.project_id,
            if_exists="replace",
            credentials=self.credentials,)
        
        Colab.register_execution(self, table_id)