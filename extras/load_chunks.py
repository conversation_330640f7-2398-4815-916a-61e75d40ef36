"""
Cria um script que gera todos os campos de frequencia baseados na tabela acessos e mescla com dados unicos da tabela person
"""
import time

# libs...
import pandas_gbq
import numpy as np
import pandas as pd
from google.cloud import bigquery
from google.oauth2 import service_account
from datetime import datetime, timedelta, timezone


#-----------------------------------------------------------------------------------------------------------------------

class load_data:

    def __init__(self, project, dataset_id, credentials, train = False):
        self.credentials = credentials
        self.dataset_id = dataset_id
        self.project = project
        self.train = train

    def print_log(self, message, color):
        colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'reset': '\033[0m',
            'black': '\033[90m',
            'light_red': '\033[1;31m',
            'light_green': '\033[1;32m',
            'light_yellow': '\033[1;33m',
            'light_blue': '\033[1;34m',
            'light_magenta': '\033[1;35m',
            'light_cyan': '\033[1;36m',
            'light_white': '\033[1;37m',
            'bg_red': '\033[41m',
            'bg_green': '\033[42m',
            'bg_yellow': '\033[43m',
            'bg_blue': '\033[44m',
            'bg_magenta': '\033[45m',
            'bg_cyan': '\033[46m',
            'bg_white': '\033[47m',
            'bg_reset': '\033[49m',
        }

        if color not in colors:
            color = 'white'  # Cor padrão se a cor especificada não existir na lista

        print(f"{colors[color]}{message}{colors['reset']}")

    def select(self, sql, projeto, credenciais):

        """CRIA A REQUISIÇÃO PARA USAR UMA QUERY DENTRO DO BIGQUERY"""

        try:
            bq_client = bigquery.Client(credentials=credenciais, project=projeto)
            job_config = bigquery.QueryJobConfig()
            query_job = bq_client.query(sql, job_config=job_config)
            df = query_job.result().to_dataframe()

            self.print_log("==============================query loaded==============================", "green")
            self.print_log(f"ID job config{query_job}", "light_green")
            self.print_log(f"tamanho da consulta{df.shape}", "light_green")
            self.print_log(df.head(), "light_green")
            return df
        except Exception as exc:
            self.print_log(f"Erro!!!!!\n{exc}", "bg_red")

    # ----------------------------------------------------------------------------------------------------------------------
    # request data...

    def load_dataset(self):
        query_acessos = f"""SELECT * 
                             FROM `{self.project}.{self.dataset_id}.acessos`"""

        query_person = f"""SELECT *
                            FROM `{self.project}.{self.dataset_id}.person`"""


        ACESSOS = self.select(query_acessos, self.project, self.credentials)
        ACESSOS = pd.DataFrame(ACESSOS)

        PERSON = self.select(query_person, self.project, self.credentials)
        PERSON = pd.DataFrame(PERSON)

        self.print_log("==============================date loaded==============================", "green")

        ACESSOS, PERSON = self.cleaning(ACESSOS, PERSON)
        return ACESSOS, PERSON

    # ------------------------------------------------------

    def cleaning(self, ACESSOS, PERSON):
        # filtragem...
        ACESSOS = ACESSOS[ACESSOS['ac_de'] == 'aluno']
        ACESSOS = ACESSOS[["s_codigocliente", "ac_dthrentrada", "e_empresafinanceiro", "_chave"]]
        ACESSOS = ACESSOS.sort_values(by=['s_codigocliente', 'ac_dthrentrada'])

        print("checkpoint 0.1")
        print(ACESSOS.head())

        # remoção de ocorrencias no mesmo dia por clientes...
        ACESSOS['ac_dthrentrada'] = pd.to_datetime(ACESSOS['ac_dthrentrada'])

        ACESSOS['ac_dthrentrada_date'] = ACESSOS['ac_dthrentrada'].dt.date
        ACESSOS = ACESSOS.drop_duplicates(subset=['s_codigocliente', 'e_empresafinanceiro', 'ac_dthrentrada_date'], keep='first')


        PERSON = PERSON[PERSON['situacao'] != 'VI']

        # predição...
        PERSON = PERSON[PERSON['situacao'] != 'IN']
        PERSON = PERSON[PERSON['situacao'] != 'TR']

        self.print_log("==============================Clean data==============================", "green")

        return ACESSOS, PERSON
    # ----------------------------------------------------------------------------------------------------------------------
    # life-time...

    def life_time(self):

        self.ACESSOS, self.PERSON = self.load_dataset()
        self.ACESSOS['ac_dthrentrada'] = pd.to_datetime(self.ACESSOS['ac_dthrentrada'])
        self.ACESSOS['Ano_Mes'] = self.ACESSOS['ac_dthrentrada'].dt.to_period('M')

        # frequencia mes...
        freq_mes = self.ACESSOS.groupby(['s_codigocliente', 'Ano_Mes', 'e_empresafinanceiro', "_chave", "ac_dthrentrada"]).size().reset_index(name="freq_mensal")
        freq_mes.sort_values(by='Ano_Mes', ascending=False, inplace=True)

        # frequencia de todos os meses...
        # freq_mes  = freq_mes.groupby("s_codigocliente").mean().reset_index()

        freq_mes["faltas_mes"] = abs(freq_mes["freq_mensal"] - 30) # faltas mensal

        # Pegar apenas o registro mais recente de cada cliente
        freq_mes = freq_mes.groupby('s_codigocliente').first().reset_index()

        #---------------------------------------------------------------------------------------------------------------

        # frequencia quartil...
        self.ACESSOS['Trimestre'] = self.ACESSOS['ac_dthrentrada'].dt.year.astype(str) + 'Q' + self.ACESSOS['ac_dthrentrada'].dt.quarter.astype(str)

        freq_quartils = self.ACESSOS.groupby(['s_codigocliente', 'Trimestre']).size().reset_index(name='freq_quartil')
        freq_quartils.sort_values(by='Trimestre', ascending=False, inplace=True)

        # freq_quartils = freq_quartils.groupby("s_codigocliente").mean().reset_index() # media de todos os quartils
        freq_quartils = freq_quartils.groupby('s_codigocliente').first().reset_index()

        #---------------------------------------------------------------------------------------------------------------

        # frequencia semestre...
        self.ACESSOS['Semestre'] = self.ACESSOS['ac_dthrentrada'].dt.year.astype(str) + '-' + ((self.ACESSOS['ac_dthrentrada'].dt.month - 1) // 6 + 1).astype(str)

        freq_semestral = self.ACESSOS.groupby(['s_codigocliente', 'Semestre']).size().reset_index(name='freq_semestre')
        freq_semestral.sort_values(by='Semestre', ascending=False, inplace=True)

        # freq_semestral  = freq_semestral.groupby("s_codigocliente").mean().reset_index() # media de todos os semestres

        freq_semestral = freq_semestral.groupby('s_codigocliente').first().reset_index()

        #---------------------------------------------------------------------------------------------------------------

        # agrupamento...
        temp = pd.merge(freq_mes, freq_quartils, on="s_codigocliente")
        temp = pd.merge(temp, freq_semestral, on="s_codigocliente")

        #---------------------------------------------------------------------------------------------------------------

        # faltas quinzena...
        self.ACESSOS['Ano_Quinzena'] = self.ACESSOS['ac_dthrentrada'].dt.to_period('15D')

        freq_quinzenal = self.ACESSOS.groupby(['s_codigocliente', 'Ano_Quinzena']).size().reset_index(name="freq_quinzenal")
        freq_quinzenal.sort_values(by='Ano_Quinzena', ascending=False, inplace=True)

        freq_quinzenal["falta_quinzena"] = abs(freq_quinzenal["freq_quinzenal"] - 15)
        freq_quinzenal = freq_quinzenal.groupby('s_codigocliente').first().reset_index()

        # freq_quinzenal = freq_quinzenal.groupby("s_codigocliente").mean().reset_index()
        temp["falta_quinzena"] = freq_quinzenal["falta_quinzena"]

        #---------------------------------------------------------------------------------------------------------------

        # faltas semana...
        self.ACESSOS['Ano_Semana'] = self.ACESSOS['ac_dthrentrada'].dt.to_period('7D')

        freq_semanal = self.ACESSOS.groupby(['s_codigocliente', 'Ano_Semana']).size().reset_index(name="freq_semanal")
        freq_semanal.sort_values(by='Ano_Semana', ascending=False, inplace=True)

        freq_semanal["falta_semanal"] = abs(freq_semanal["freq_semanal"] - 7)
        freq_semanal = freq_semanal.groupby("s_codigocliente").mean().reset_index()
        freq_semanal = freq_semanal.groupby('s_codigocliente').first().reset_index()

        temp["freq_semanal"] = freq_semanal["freq_semanal"]

        self.ACESSOS = temp
    # ----------------------------------------------------------------------------------------------------------------------
    # persson

    def person_labels(self):

        # select coluns...
        self.PERSON = self.PERSON[["codigocliente", "cod_empresafinanceiro", "sexocliente", "situacao", "situacaocontrato",
                         "idade", "datamatricula", "datavigenciaate","datarenovacaocontrato", "datarematriculacontrato",
                         "duracaocontratomeses", "frequenciasemanal", "valorparcabertocontrato", "valorpagocontrato","vezesporsemana", "nomecliente"]]

        # campo proximo fim -> até 2 meses para fim do contrato...
        data_atual = datetime.now(timezone.utc)
        periodo_proximo_termino = timedelta(days=60)

        self.PERSON['prox_termino'] = np.nan
        self.PERSON.loc[~self.PERSON['datavigenciaate'].isnull(), 'prox_termino'] = (self.PERSON['datavigenciaate'] - data_atual) <= periodo_proximo_termino

        # a vencer
        self.PERSON["vencimento"] = self.PERSON["situacaocontrato"].apply(lambda x: x == "AV")

        # churn...
        self.PERSON['churn'] = np.where((self.PERSON['situacao'] == 'IN') & ((self.PERSON['situacaocontrato'] == 'CA') | (self.PERSON['situacaocontrato'] == 'DE')), True, False)

        # cálculo do lifetime matricula rematricula e renovacao...

        # conversão de datas para pandas datetime para fazer os cálculos...
        self.PERSON['datamatricula'] = pd.to_datetime(self.PERSON['datamatricula'], utc=True)
        self.PERSON['datarenovacaocontrato'] = pd.to_datetime(self.PERSON['datarenovacaocontrato'], utc=True)
        self.PERSON['datarematriculacontrato'] = pd.to_datetime(self.PERSON['datarematriculacontrato'], utc=True)

        # data de hoje com utc=True para ser tz aware igual às colunas de data do acessos...
        self.PERSON['datahoje'] = pd.to_datetime('today', utc=True)

        # cálculo de LT: hoje - data...
        self.PERSON['tempo_renovacao'] = self.PERSON['datahoje'].sub(self.PERSON['datarenovacaocontrato'], axis=0)
        self.PERSON['tempo_rematricula'] = self.PERSON['datahoje'].sub(self.PERSON['datarematriculacontrato'], axis=0)
        self.PERSON['tempo_matricula'] = self.PERSON['datahoje'].sub(self.PERSON['datamatricula'], axis=0)

        # conversão do valor para DIAS...
        self.PERSON['tempo_renovacao'] = self.PERSON['tempo_renovacao'].dt.days
        self.PERSON['tempo_rematricula'] = self.PERSON['tempo_rematricula'].dt.days
        self.PERSON['tempo_matricula'] = self.PERSON['tempo_matricula'].dt.days

        # tirando coluna temporária...
        self.PERSON = self.PERSON.drop(columns=['datahoje'])

        # junção...

        self.DATA = pd.merge(self.ACESSOS, self.PERSON, left_on=['s_codigocliente', 'e_empresafinanceiro'], right_on=['codigocliente', 'cod_empresafinanceiro'], how='inner')
        self.DATA.drop(['codigocliente', 'cod_empresafinanceiro', 'Semestre', 'Ano_Mes', 'Trimestre'], axis=1, inplace=True)

        self.print_log(self.ACESSOS.shape, "light_yellow")
        self.print_log(self.PERSON.shape, "light_yellow")

        # self.DATA = self.scheme(self.DATA)
        self.to_gbq(self.DATA)
        self.DATA.to_csv("personas.csv", index=False)
    # ----------------------------------------------------------------------------------------------------------------
    # type data...

    def scheme(self, DATA):

        DATA["churn"] = DATA["churn"].astype("str")
        DATA["idade"] = DATA["idade"].astype("Int64")
        DATA["situacao"] = DATA["situacao"].astype("str")
        DATA["faltas_mes"] = DATA["faltas_mes"].astype("float64")
        DATA["sexocliente"] = DATA["sexocliente"].astype("str")
        DATA["modalidades"] = DATA["modalidades"].astype("str")
        DATA["prox_termino"] = DATA["prox_termino"].astype("str")
        DATA["freq_quartil"] = DATA["freq_quartil"].astype("float64")
        DATA["freq_mensal"] = DATA["freq_mensal"].astype("float64")
        DATA["freq_semanal"] = DATA["freq_semanal"].astype("float64")
        DATA["freq_semestre"] = DATA["freq_semestre"].astype("float64")
        DATA["vezesporsemana"] = DATA["vezesporsemana"].astype("Int64")
        DATA["situacaocontrato"] = DATA["situacaocontrato"].astype("str")
        DATA["s_codigocliente"] = DATA["s_codigocliente"].astype("Int64")
        DATA["falta_quinzena"] = DATA["falta_quinzena"].astype("float64")
        DATA["tempo_matricula"] = DATA["tempo_matricula"].astype("float64")
        DATA["tempo_renovacao"] = DATA["tempo_renovacao"].astype("float64")
        DATA["frequenciasemanal"] = DATA["frequenciasemanal"].astype("float64")
        DATA["valorpagocontrato"] = DATA["valorpagocontrato"].astype("float64")
        DATA["tempo_rematricula"] = DATA["tempo_rematricula"].astype("float64")
        DATA["e_empresafinanceiro"] = DATA["e_empresafinanceiro"].astype("Int64")
        DATA["duracaocontratomeses"] = DATA["duracaocontratomeses"].astype("Int64")
        DATA["valorparcabertocontrato"] = DATA["valorparcabertocontrato"].astype("float64")

        self.print_log("===========================================GENERETE SCHEME===========================================", "green")

        return DATA
    # ----------------------------------------------------------------------------------------------------------------
    # to_gbq...

    def to_gbq(self, DATA):
        cred = service_account.Credentials.from_service_account_info(
            **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        )
        pandas_gbq.to_gbq(DATA, "churn_model.churn_data_bronze", project_id="academia-de-verdade", credentials=cred, if_exists='replace')
        self.print_log("===========================================UPLOADING DATA TO GOOGLE===========================================", "green")
        self.print_log("Os dados foram carregados na tabela no BigQuery.", "bg_cyan")

        # DATA.to_csv("data/CHURN.csv")



# ----------------------------------------------------------------------------------------------------------------------

def main(credentials, project, dataset_id):

    DATA = load_data(project=project,
                     dataset_id=dataset_id,
                     credentials=credentials)
    DATA.life_time()
    DATA.person_labels()