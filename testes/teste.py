# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-fd10e19d77'
SQL_PATH = 'engenharia_operacao_de_excecao'

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '243693591c4b9a507d0351f36c5d8d7b55868c53',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        'client_email': '<EMAIL>',
                        'client_id': '117924762975739159681',
                        'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
                        'token_uri': 'https://oauth2.googleapis.com/token',
                        'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',
                        'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-fd10e19d77%40dados-fd10e19d77.iam.gserviceaccount.com'
                      }
                      )

util = Util(PROJECT_ID, 'dataset_fd10e19d77', 'fd10e19d777ffea95ae185d53fb6c10c', credentials=credentials, base_path = SQL_PATH)

      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)

if os.environ.get('QR'):
  util.run_queries()
else:
  ################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
  TABLE_ID='rede_empresa_financeiro'
  QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
  '''util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

  #######################################DADOS ZONAS#################################################
  util.generate_data_all_zones('person',  columns_drop=['faseatualcrm', 'codigocontrato', 'endereco'], parallel=True)
  util.generate_data_all_zones('dadosgerenciais', parallel=True)
  util.generate_data_all_zones('colab', parallel=True)
  util.generate_data_all_zones('plano', parallel=True)
  ##### ------Por enquanto no temos BI em atividade deste hoje é 01/06/2023..
  ##### util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc', parallel=True)
  ##### util.generate_data_all_zones('treinos_executados', prefix='bdmusc', parallel=True)
  ##### util.generate_data_all_zones('treinos_alunos', prefix='bdmusc', parallel=True)
  util.generate_data_all_zones('crm', parallel=True)
  util.generate_data_all_zones('marketing', parallel=True)
  util.generate_data_all_zones('tpv-previsao', parallel=True)
  util.generate_data_all_zones('movparcela', parallel=True)
  util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
  util.generate_data_all_zones('estorno_cancelamento', parallel=True)
  util.generate_data_all_zones('finan_lancamentos', parallel=True)
  util.generate_data_all_zones('valor_desconto', parallel=True)'''
  
  util.generate_data_all_zones('clientes_contrato_inativo_com_acesso', parallel=True)
