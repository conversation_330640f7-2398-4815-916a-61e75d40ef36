-- Active: 1741625085864@@localhost@5432@bdmuscliveunidtorresam@public
SELECT c.nome,
       c.matricula,
       tr.cliente_codigo,
       tr.codigo,
       tr.datainicio AS datainicio,
       tr.datafim AS datafim,
       pt.datainicio as datainicio_programa,
       tr.executado<PERSON>hadia,
       tr.nota,
       tr.chaveexecucao,
       pt.isgeradoporia,
       tr.unidadeexecucao,
       pt.codigo as codigo_programa,
       src.origem,
       src.descricao_origem
FROM treinorealizado tr
         INNER JOIN clientesintetico c ON tr.codigo = c.codigo
         INNER JOIN programatreinoficha ptf ON ptf.codigo = tr.programatreinoficha_codigo
         INNER JOIN programatreino pt ON pt.codigo = ptf.programa_codigo
         LEFT JOIN (SELECT 0 as origem, 'Ficha Impressa' as descricao_origem
                    UNION ALL
                    SELECT 1 as origem, 'A<PERSON>p. Professor' as descricao_origem
                    UNION ALL
                    SELECT 2 as origem, 'Smartphone' as descricao_origem
                    UNION ALL
                    SELECT 3 as origem, '<PERSON>cha concluída pelo RetiraFicha' as descricao_origem) src
                   ON tr.origem = src.origem