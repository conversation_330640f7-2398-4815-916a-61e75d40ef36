CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.receita_compensado_nicho` AS
WITH
    receita_dia as (
        select
            DATE_TRUNC (datalancamento, DAY) AS datalancamento,
            SUM(valor) AS receita_total,
            nome_empresa,
            codigo_empresa,
            _chave
        from
            `dadosmercado-415119.sistema_pacto_geral.receita_compensado`
        group by
            1,
            3,
            4,
            5
        order by datalancamento desc
    )

SELECT distinct_receitas.datalancamento, distinct_receitas.nicho, distinct_receitas.nicho_descricao, SUM(
        distinct_receitas.receita_total
    ) AS receita_total
FROM (
        SELECT DISTINCT
            rc.receita_total, ef.nicho, ef.nicho_descricao, rc.datalancamento
        FROM
            receita_dia rc
            INNER JOIN `dadosmercado-415119.sistema_pacto_geral.empresa_financeiro` ef ON rc.codigo_empresa = ef.empresazw
            AND rc._chave = ef.chavezw
    ) AS distinct_receitas
GROUP BY
    1,
    2,
    3
ORDER BY receita_total DESC