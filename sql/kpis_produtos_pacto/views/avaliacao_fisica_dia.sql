CREATE OR REPLACE VIEW `dadosmercado-415119.sistema_pacto_geral.avaliacao_fisica_por_dia` AS
SELECT
    'avaliacao' as tipo,
    CAST(dataavaliacao AS DATE) AS dia,
    COUNT(*) AS quantidade_avaliacoes
FROM `dadosmercado-415119.sistema_pacto_geral.avaliacao_fisica`
GROUP BY
    dia
UNION ALL
SELECT
    'reavaliacao' as tipo,
    CAST(dataavaliacao AS DATE) AS dia,
    COUNT(*) AS quantidade_avaliacoes
FROM `dadosmercado-415119.sistema_pacto_geral.reavaliacao_fisica`
GROUP BY
    dia
ORDER BY
    dia;