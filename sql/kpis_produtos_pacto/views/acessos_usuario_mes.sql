CREATE
OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.acesso_usuario_por_dia` AS
SELECT
    DATE(data) as data,
    SUM(quantidade_acessos) as total_acessos
FROM
    (
        SELECT
            usuarioid,
            TIMESTAMP_TRUNC (createdat, MINUTE) as data,
            key,
            COUNT(
                DISTINCT CONCAT (
                    usuarioid,
                    '_',
                    CAST(TIMESTAMP_TRUNC (createdat, MINUTE) AS STRING),
                    '_',
                    key
                )
            ) as quantidade_acessos
        FROM
            `dadosmercado-415119.sistema_pacto_geral.session`
        WHERE
            createdat IS NOT NULL
            AND createdat >= '2020-08-01'
        GROUP BY
            usuarioid,
            data,
            key
    ) as subquery
GROUP BY
    DATE(data)
ORDER BY
    data;
