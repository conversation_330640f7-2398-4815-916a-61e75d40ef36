CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.empresa_campanha_ativa_mes` AS
WITH meses AS (
    SELECT DATE_TRUNC(date, MONTH) as mes
    FROM UNNEST(
        GENERATE_DATE_ARRAY('2023-01-01', CURRENT_DATE(), INTERVAL 1 MONTH)
    ) as date
),
campanhas AS (
    SELECT concat(_chave, '-', codigo_empresa) as identificacao_empresa,
           DATE_TRUNC(datainicial, MONTH) as mes_inicial,
           DATE_TRUNC(datafinal, MONTH) as mes_final
    FROM `dadosmercado-415119.sistema_pacto_geral.campanha`
)
SELECT meses.mes,
       COUNT(DISTINCT campanhas.identificacao_empresa) as numero_campanhas
FROM meses
LEFT JOIN campanhas ON meses.mes BETWEEN campanhas.mes_inicial AND campanhas.mes_final
GROUP BY meses.mes
ORDER BY meses.mes;