CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.empresa_campanha_ativa` AS
WITH dias AS (SELECT DATE_TRUNC(date, DAY) as dia
              FROM UNNEST(
                           GENERATE_DATE_ARRAY('2023-01-01', CURRENT_DATE(), INTERVAL 1 DAY)
                   ) as date),
     campanhas AS (SELECT concat(_chave, '-', codigo_empresa) identificacao_empresa,
                          DATE_TRUNC(datainicial, DAY) as     dia_inicial,
                          DATE_TRUNC(datafinal, DAY)   as     dia_final,
                          nome nome_campanha,
                          nome_empresa,
                          _chave,
                          codigo_empresa
                   FROM
                       `dadosmercado-415119.sistema_pacto_geral.campanha`)
SELECT dias.dia,
       identificacao_empresa,
       dia_inicial,
       dia_final,
       nome_campanha,
       nome_empresa,
       _chave,
       codigo_empresa
FROM dias
         LEFT JOIN campanhas ON dias.dia BETWEEN campanhas.dia_inicial AND campanhas.dia_final
ORDER BY dias.dia;