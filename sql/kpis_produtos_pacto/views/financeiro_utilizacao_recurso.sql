CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.financeiro_utilizacao_recurso` AS
SELECT
    DATE_TRUNC (date, DAY) AS data,
    recurso,
    chave,
    usuario,
    nomeempresa,
    COUNT(*) AS quantidade_utilizacoes
FROM
    `dadosmercado-415119.sistema_pacto_geral.recursoempresa`
WHERE
    recurso LIKE "%ABRIR_CAIXA%"
    OR recurso LIKE "%CONCILIACAO%"
    OR recurso LIKE "%CONTAS_A_PAGAR%"
    OR recurso LIKE "%CONTAS_PAGAR%"
    OR recurso LIKE "%CONTAS_A_RECEBER%"
    OR recurso LIKE "%CONTAS_RECEBER%"
    OR recurso LIKE "%DRE%"
    OR recurso LIKE "%FECHAR_CAIXA%"
    OR recurso LIKE "%GESTAO_RECEBIVEIS_FATURAMENTO_RECEBIDO%"
    AND TIMESTAMP_DIFF (
        CURRENT_TIMESTAMP(),
        date,
        DAY
    ) <= 31
GROUP BY
    1,
    2,
    3,
    4,
    5
ORDER BY data;