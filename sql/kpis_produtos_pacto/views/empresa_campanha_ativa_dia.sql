CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.empresa_campanha_ativa_dia` AS
WITH dias AS (SELECT DATE_TRUNC(date, DAY) as dia
              FROM UNNEST(
                           GENERATE_DATE_ARRAY('2023-01-01', CURRENT_DATE(), INTERVAL 1 DAY)
                   ) as date),
     campanhas AS (SELECT concat(_chave, '-', codigo_empresa) identificacao_empresa,
                          DATE_TRUNC(datainicial, DAY) as     dia_inicial,
                          DATE_TRUNC(datafinal, DAY)   as     dia_final
                   FROM
                       ` dadosmercado-415119.sistema_pacto_geral.campanha `)
SELECT dias.dia,
       COUNT(DISTINCT campanhas.identificacao_empresa) as numero_empresas
FROM dias
         LEFT JOIN campanhas ON dias.dia BETWEEN campanhas.dia_inicial AND campanhas.dia_final
GROUP BY dias.dia
ORDER BY dias.dia;