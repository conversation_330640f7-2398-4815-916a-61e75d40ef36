CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.acesso_relatorio_treino` AS
SELECT
    DATE_TRUNC (date, DAY) data,
    recurso,
    chave,
    usuario,
    nomeempresa,
    COUNT(*) as quantidade_utilizacoes
FROM
    `dadosmercado-415119.sistema_pacto_geral.recursoempresa`
WHERE
    recurso LIKE "%TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO%"
    OR recurso LIKE "%TREINO_ANDAMENTO%"
    OR recurso LIKE "%TREINO_ATIVIDADES_PROFESSORES%"
    OR recurso LIKE "%TREINO_CARTEIRA%"
    OR recurso LIKE "%TREINO_GESTAO_CREDITOS%"
    OR recurso LIKE "%TREINO_PROFESSORES_SUBSTITUIDOS%"
GROUP BY
    1,
    2,
    3,
    4,
    5
ORDER BY data;