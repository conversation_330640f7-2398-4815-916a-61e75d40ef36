CREATE
OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.planos_vendidos_tipo` AS
SELECT
    TIMESTAMP_TRUNC(datalancamento, DAY) AS dia,
    COUNT(
        CASE
            WHEN planopersonal = true THEN 1
        END
    ) AS personal,
    COUNT(
        CASE
            WHEN planoavancado = true THEN 1
        END
    ) AS avancado,
    COUNT(
        CASE
            WHEN regimerecorrencia = true THEN 1
        END
    ) AS recorrencia,
    COUNT(
        CASE
            WHEN regimerecorrencia = false THEN 1
        END
    ) AS normal,
    COUNT(
        CASE
            WHEN vendacreditotreino = true THEN 1
        END
    ) AS credito    
FROM
    `dadosmercado-415119.sistema_pacto_geral.contrato_tipo_situacao` c
WHERE
    datalancamento IS NOT NULL
GROUP BY
    dia
ORDER BY
    dia DESC;