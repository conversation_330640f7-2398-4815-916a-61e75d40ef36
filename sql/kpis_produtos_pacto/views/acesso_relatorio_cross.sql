CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.acesso_relatorio_cross` AS
SELECT
    DATE_TRUNC (date, DAY) AS data,
    recurso,
    chave,
    usuario,
    nomeempresa,
    CASE
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*CROSS(-|_)APARELHOS.*'
        ) THEN 'Aparelhos'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*CROSS(-|_)ATIVIDADE.*'
        ) THEN 'Atividade'
        WHEN REGEXP_CONTAINS (recurso, '.*CROSS(-|_)WOD.*') THEN 'WOD'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*CROSS(-|_)BENCHMARKS.*'
        ) THEN 'Benchmarks'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*CROSS(-|_)TIPOS(-|_)BENCHMARK.*'
        ) THEN 'Tipos Benchmark'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*CROSS(-|_)TIPO(-|_)WOD.*'
        ) THEN 'Tipo WOD'
        ELSE 'Outro'
    END AS nome_normalizado,
    COUNT(*) AS quantidade_utilizacoes
FROM
    `dadosmercado-415119.sistema_pacto_geral.recursoempresa`
WHERE
    recurso LIKE "%CROSS_APARELHOS%"
    OR recurso LIKE "%CROSS_ATIVIDADE%"
    OR recurso LIKE "%CROSS_WOD%"
    OR recurso LIKE "%CROSS_BENCHMARKS%"
    OR recurso LIKE "%CROSS_TIPOS_BENCHMARK%"
    OR recurso LIKE "%CROSS_TIPO_WOD%"
GROUP BY
    data,
    recurso,
    nome_normalizado,
    chave,
    usuario,
    nomeempresa
ORDER BY data;