CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.acesso_relatorio_agenda` AS
SELECT
    DATE_TRUNC (date, DAY) AS data,
    recurso,
    chave,
    usuario,
    nomeempresa,
    CASE
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)AMBIENTE.*'
        ) THEN 'Ambiente'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)MODALIDADE.*'
        ) THEN 'Modalidade'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)INDICADORES.*'
        ) THEN 'Indicadores'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)AULA(-|_)EXCLUIDA.*'
        ) THEN 'Aula Excluída'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)TV(-|_)AULA.*'
        ) THEN 'TV Aula'
        WHEN REGEXP_CONTAINS (
            recurso,
            '.*AGENDA(-|_)TV(-|_)GESTOR.*'
        ) THEN 'TV Gestor'
        ELSE 'Outro'
    END AS nome_normalizado,
    COUNT(*) AS quantidade_utilizacoes
FROM
    `dadosmercado-415119.sistema_pacto_geral.recursoempresa`
WHERE
    recurso LIKE "%AGENDA_AMBIENTE%"
    OR recurso LIKE "%AGENDA_MODALIDADE%"
    OR recurso LIKE "%AGENDA_INDICADORES%"
    OR recurso LIKE "%AGENDA_AULA_EXCLUIDA%"
    OR recurso LIKE "%AGENDA_TV_AULA%"
    OR recurso LIKE "%AGENDA_TV_GESTOR%"
GROUP BY
    data,
    recurso,
    chave,
    usuario,
    nomeempresa,
    nome_normalizado
ORDER BY data;