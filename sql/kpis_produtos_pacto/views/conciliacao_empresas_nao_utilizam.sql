CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.conciliacao_empresas_nao_utilizam` AS
SELECT
    chave_empresa,
    datalancamento,
    quantidade_registros,
    nomeempresa
FROM (
        SELECT
            CONCAT(
                e._chave, '-', CAST(e.codigo AS STRING)
            ) AS chave_empresa, ci.datalancamento, COUNT(ci._chave) AS quantidade_registros, nome as nomeempresa
        FROM
            `dadosmercado-415119.sistema_pacto_geral.empresa_adm` e
            LEFT JOIN `dadosmercado-415119.sistema_pacto_geral.conciliacao_item` ci ON e._chave = ci._chave
        WHERE
            e.ativa
        GROUP BY
            1, 2, 4
    ) subquery
WHERE
    quantidade_registros = 0
ORDER BY datalancamento, chave_empresa