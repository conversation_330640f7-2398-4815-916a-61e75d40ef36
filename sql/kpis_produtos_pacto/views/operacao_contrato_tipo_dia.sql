CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.operacao_contrato_tipo_dia` AS
SELECT DATE(c.dataoperacao) AS dia,
       CASE
           WHEN c.tipo = 'RM' THEN 'Rematrícula'
           WHEN c.tipo = 'TS' THEN 'Transferência Saída'
           WHEN c.tipo = 'MA' THEN 'Matrícula'
           WHEN c.tipo = 'AD' THEN 'Alteração Duração'
           WHEN c.tipo = 'RE' THEN 'Renovação'
           WHEN c.tipo = 'BA' THEN 'Bônus - Acréscimo de dias'
           WHEN c.tipo = 'BR' THEN 'Bônus - Redução de dias'
           WHEN c.tipo = 'CR' THEN 'Férias'
           WHEN c.tipo = 'TE' THEN 'Transferência Entrada'
           WHEN c.tipo = 'CA' THEN 'Cancelamento'
           WHEN c.tipo = 'AH' THEN 'Alteração Horário'
           WHEN c.tipo = 'TR' THEN 'Trancamento'
           WHEN c.tipo = 'TV' THEN 'Trancamento Vencido'
           WHEN c.tipo = 'RT' THEN 'Retorno Trancamento'
           WHEN c.tipo = 'IM' THEN 'Incluir Modalidade'
           WHEN c.tipo = 'EM' THEN 'Excluir Modalidade'
           WHEN c.tipo = 'AM' THEN 'Alterar Modalidade'
           WHEN c.tipo = 'AC' THEN 'Alteração Contrato'
           WHEN c.tipo = 'AT' THEN 'Atestado'
           WHEN c.tipo = 'RA' THEN 'Retorno - Atestado'
           WHEN c.tipo = 'LV' THEN 'Liberação de Vaga'
           WHEN c.tipo = 'BC' THEN 'Afastamento Coletivo'
           WHEN c.tipo = 'TD' THEN 'Transferência dos Direitos de uso'
           WHEN c.tipo = 'RD' THEN 'Retorno dos Direitos de uso'
           ELSE c.tipo
       END AS tipo,
       COUNT(*) AS quantidade
FROM `dadosmercado-415119.sistema_pacto_geral.operacao_contrato_tipo` c
GROUP BY DATE(c.dataoperacao), tipo;