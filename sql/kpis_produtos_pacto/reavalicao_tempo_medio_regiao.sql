CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.tempo_medio_reavaliacao_por_regiao` AS
WITH cliente_info AS (
    SELECT
        cli.codigo_cliente,
        cli.estado_empresa AS regiao
    FROM `dadosmercado-415119.sistema_pacto_geral.cliente_adm` cli
    WHERE cli.estado_empresa IS NOT NULL
),
avaliacao_duracao AS (
    SELECT
        av.codigo_cliente,
        TIMESTAMP_DIFF(TIMESTAMP(av.dataproxima), TIMESTAMP(av.dataavaliacao), DAY) AS dias_para_reavaliacao
    FROM `dadosmercado-415119.sistema_pacto_geral.avaliacao_fisica` av
    WHERE av.dataproxima IS NOT NULL
       AND TIMESTAMP_DIFF(CURRENT_DATE(), DATE(av.dataavaliacao), MONTH) <= 12
      AND TIMESTAMP_DIFF(TIMESTAMP(av.dataproxima), TIMESTAMP(av.dataavaliacao), DAY) >= 0
)
SELECT
    ci.regiao,
     CAST(ROUND(AVG(ad.dias_para_reavaliacao)) AS INT64) AS tempo_medio_reavaliacao_em_dias
FROM avaliacao_duracao ad
INNER JOIN cliente_info ci ON ad.codigo_cliente = ci.codigo_cliente
GROUP BY
    ci.regiao
ORDER BY
    ci.regiao;