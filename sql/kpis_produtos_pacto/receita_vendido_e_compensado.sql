select distinct movpagamento.*,
                empresa.nome                                                                 as empresaNome,
                cli.matricula,
                cli.cpf                                                                      as cpfPagador,
                conta.descricao                                                              as descricaoconta,
                movc.dataquitacao                                                            as datamovconta,
                array_to_string(array(select mp.descricao
                                      from pagamentomovparcenomepagadorla p
                                               inner join movparcela mp on mp.codigo = p.movparcela
                                      where p.movpagamento = movpagamento.codigo), ', ', '') as parcelasPagas,
                p.nomepai,
                p.nomemae,
                p.datanasc
from movpagamento
         left join empresa ON empresa.codigo = movpagamento.empresa
         left outer join situacaoclientesinteticodw as cli on cli.codigopessoa = movpagamento.pessoa
         left outer join movconta as movc on movc.codigo = movpagamento.movconta
         left outer join conta on conta.codigo = movc.conta
         left join pessoa p on p.codigo = movpagamento.pessoa
         left outer join cheque on movpagamento.codigo = cheque.movpagamento and cheque.situacao not like 'CA' and
                                   cheque.situacao not like 'DV'
         left outer join cartaocredito
                         on movpagamento.codigo = cartaocredito.movpagamento AND cartaocredito.situacao not like 'CA'
         inner join formapagamento fp on movpagamento.formapagamento = fp.codigo and fp.tipoformapagamento IN
                                                                                     ('CA', 'CD', 'CH', 'AV', 'CC',
                                                                                      'BB', 'CO', 'PD', 'TB', 'PX')
         inner join usuario u on u.codigo = movpagamento.responsavelpagamento
where 1 = 1
  and movpagamento.empresa = 1
  and (movpagamento.recibopagamento is not null or movpagamento.credito = 't')
  and (movpagamento.valor > 0 or fp.tipoformapagamento = 'CC')
  and ((movpagamento.datalancamento >= '2024-12-17 00:00' and movpagamento.datalancamento <= '2024-12-17 23:59'))
  and ((movpagamento.datapagamento >= '2024-12-17 00:00' and movpagamento.datapagamento <= '2024-12-17 23:59' and
        fp.tipoformapagamento IN ('AV', 'CD', 'CC', 'BB', 'CO', 'TB', 'PX')) or
       (cheque.datacompesancao >= '2024-12-17 00:00' and cheque.datacompesancao <= '2024-12-17 23:59') or
       (cartaocredito.datacompesancao >= '2024-12-17 00:00' and cartaocredito.datacompesancao <= '2024-12-17 23:59'))
