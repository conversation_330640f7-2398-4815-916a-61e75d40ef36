CREATE OR REPLACE VIEW `dadosmercado-415119.kpis_produtos_pacto.tempo_medio_reavaliacao_por_protocolo` AS
WITH avaliacao_duracao AS (
    SELECT
        av.protocolo,
        descricao_protocolo,
        TIMESTAMP_DIFF(TIMESTAMP(av.dataproxima), TIMESTAMP(av.dataavaliacao), DAY) AS dias_para_reavaliacao
    FROM `dadosmercado-415119.sistema_pacto_geral.avaliacao_fisica` av
    WHERE av.dataproxima IS NOT NULL
      AND TIMESTAMP_DIFF(CURRENT_DATE(), DATE(av.dataavaliacao), MONTH) <= 12
      AND TIMESTAMP_DIFF(TIMESTAMP(av.dataproxima), TIMESTAMP(av.dataavaliacao), DAY) >= 0
)
SELECT
    ad.protocolo,
    ad.descricao_protocolo,
    CAST(ROUND(AVG(ad.dias_para_reavaliacao)) AS INT64) AS tempo_medio_reavaliacao_em_dias
FROM avaliacao_duracao ad
GROUP BY
    ad.protocolo,
    descricao_protocolo
ORDER BY
    ad.descricao_protocolo;