SELECT e.codigo,
       e.chavezw,
       e.empresazw,
       e.nicho,
       e.grupofavorecido,
       CASE
           WHEN e.nicho = 'AFSH' THEN 'Academia Full Service High Cost'
           WHEN e.nicho = 'AFSLC' THEN 'Academia Full Service Low Cost'
           WHEN e.nicho = 'AN' THEN 'Academia de Natação'
           WHEN e.nicho = 'AL' THEN 'Academia de Lutas'
           WHEN e.nicho = 'BCF' THEN 'Box Cross / Funcional'
           WHEN e.nicho = 'ST' THEN 'Studios'
           WHEN e.nicho = 'EQA' THEN 'Esportes de Quadra e Areia'
           WHEN e.nicho = 'AU' THEN 'Autarquia'
           WHEN e.nicho = 'ACP' THEN 'Associação / Clube Particular'
           ELSE 'Nicho Desconhecido'
           END AS nicho_descricao,
       e.ativazw,
       e.tipoempresa,
       e.ultimaatualizacao,
       e.cidade,
       e.estado,
       e.nomefantasia,
       e.razao<PERSON><PERSON>,
       e.nomere<PERSON><PERSON>,
       e.dataca<PERSON>tro,
       e.nomeempresazw,
       e.dataexpiracaozw::timestamp,
       e.datasuspensaoempresazw,
       e.pais,
       e.metragem,
       CASE
           WHEN e.metragem <= 200 THEN 'Pequena'
           WHEN e.metragem > 200 AND e.metragem <= 800 THEN 'Média'
           WHEN e.metragem > 800 AND e.metragem <= 2000 THEN 'Grande'
           WHEN e.metragem is null or e.metragem = 0 THEN 'Sem tamanho'
       END AS tamanho_academia,
       r.chaverede,
       r.nome
from empresafinanceiro e
         left join redeempresa r on r.id = e.redeempresa_id