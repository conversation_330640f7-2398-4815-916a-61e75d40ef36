SELECT
    c.codigo AS codigo_cliente,
    c.matricula AS matricula_cliente,
    c.situacao AS situacao_cliente,
    p.nome AS nome_cliente,
    p.datanasc,
    em.nome AS nome_empresa,
    es.descricao AS estado_empresa,
    cd.nome AS cidade_empresa,
    CASE
        WHEN DATE_PART('year', AGE(p.datanasc)) < 20 THEN 'Menor que 20'
        WHEN DATE_PART('year', AGE(p.datanasc)) BETWEEN 20 AND 29 THEN '20-29'
        WHEN DATE_PART('year', AGE(p.datanasc)) BETWEEN 30 AND 39 THEN '30-39'
        WHEN DATE_PART('year', AGE(p.datanasc)) BETWEEN 40 AND 49 THEN '40-49'
        WHEN DATE_PART('year', AGE(p.datanasc)) BETWEEN 50 AND 59 THEN '50-59'
        ELSE '60+'
    END AS faixa_etaria
FROM cliente c
INNER JOIN pessoa p ON c.pessoa = p.codigo
INNER JOIN empresa em ON c.empresa = em.codigo
LEFT JOIN estado es ON em.estado = es.codigo
LEFT JOIN cidade cd ON em.cidade = cd.codigo