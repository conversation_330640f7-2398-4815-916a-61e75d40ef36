SELECT DISTINCT ON (movparcela.codigo)
       movparcela.codigo AS movparcela_codigo,
       oo.dataoperacao::timestamp AS datacancelamento, -- Converte para TIMESTAMP
       movparcela.pessoa AS codigopessoa,
       cliente.codigo AS codigoCliente,
       CASE
           WHEN fp.descricao LIKE '%PIX%' OR fp.descricao LIKE '%DINHEIRO%' 
           THEN movparcela.datavencimento::timestamp -- Converte para DATE
           WHEN fp.descricao LIKE '%BOLETO%' 
           THEN movparcela.datavencimento::timestamp + INTERVAL '2 days' -- Converte para DATE
           WHEN fp.descricao LIKE '%CARTÃO DE CRÉDITO%' 
           THEN movparcela.datavencimento::timestamp + INTERVAL '30 days' -- Converte para DATE
           ELSE NULL
       END AS data_previsao_receita,
       CASE
           WHEN necp.contrato IS NOT NULL 
           THEN cliev.matricula
           WHEN ((vendaavulsa.colaborador IS NOT NULL OR controletaxapersonal.codigo IS NOT NULL) AND colaborador.codigo IS NOT NULL)
           THEN 'cl-' || colaborador.codigo
           WHEN (cliente.matricula IS NULL OR cliente.matricula = '')
           THEN vendaavulsa.nomecomprador || ' sem matrícula'
           ELSE cliente.matricula
       END AS cliente_matricula,
       CASE
           WHEN (movparcela.vendaavulsa > 0 AND (pessoa.nome IS NULL OR pessoa.nome = ''))
           THEN vendaavulsa.nomecomprador
           WHEN necp.contrato IS NOT NULL
           THEN pesev.nome
           ELSE pessoa.nome
       END AS pessoa_nome,
       movparcela.descricao AS parcela_descricao,
       movparcela.dataregistro::timestamp AS parcela_dataregistro, -- Converte para DATE
       movparcela.datavencimento::timestamp AS parcela_datavencimento, -- Converte para DATE
       movparcela.situacao AS parcela_situacao,
       movparcela.regimerecorrencia AS regime_recorrencia,
       movparcela.contrato AS parcela_contrato,
       movparcela.valorparcela AS parcela_valorparcela,
       1 AS total,
       rp.data::timestamp AS datapagamento, -- Converte para TIMESTAMP
       array_to_string(array(SELECT email FROM email WHERE email.pessoa = movparcela.pessoa), ',') AS email,
       array_to_string(array_agg(DISTINCT fp.descricao), ',') AS formaspagamento,
       array_to_string(array_agg(DISTINCT mod.nome), ',') AS modalidades,
       array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa = movparcela.pessoa), ',') AS telefone,
       emp.nome AS nomeEmpresa,
       (
         SELECT mr.nrtentativas 
         FROM movparcelaresultadocobranca mr 
         WHERE mr.movparcela = movparcela.codigo
         ORDER BY mr.codigo DESC 
         LIMIT 1
       ) AS nrtentativas,
       (
         SELECT mr.motivoretorno 
         FROM movparcelaresultadocobranca mr 
         WHERE mr.movparcela = movparcela.codigo
         ORDER BY mr.codigo DESC 
         LIMIT 1
       ) AS motivoretorno,
       array_to_string((
             SELECT array_agg(cc.descricao)
             FROM conveniocobranca cc
             INNER JOIN autorizacaocobrancacliente acc ON acc.conveniocobranca = cc.codigo AND acc.ativa
             WHERE acc.cliente = cliente.codigo
           ), ',') AS convenios,
       plano.descricao AS nomeplano,
       array_to_string((
             SELECT array_agg(tcc.descricao)
             FROM conveniocobranca cc
             INNER JOIN autorizacaocobrancacliente acc ON acc.conveniocobranca = cc.codigo AND acc.ativa
             INNER JOIN public.tipoconveniocobranca tcc ON tcc.codigo = cc.tipoconvenio
             WHERE acc.cliente = cliente.codigo
           ), ',') AS tipo_formas_pagamento_convenio,
       emp.codigo AS codigo_empresa,
       emp.cod_empresafinanceiro AS codigo_empresafinanceiro
FROM movparcela
    LEFT JOIN observacaooperacao oo ON oo.movparcela = movparcela.codigo AND oo.tipooperacao = 'PC'
    LEFT JOIN empresa emp ON emp.codigo = movparcela.empresa
    LEFT JOIN pessoa ON pessoa.codigo = movparcela.pessoa
    LEFT JOIN cliente ON cliente.pessoa = pessoa.codigo
    LEFT JOIN colaborador ON colaborador.pessoa = pessoa.codigo
    LEFT JOIN contrato ON movparcela.contrato = contrato.codigo
    LEFT JOIN contratocondicaopagamento ccp ON ccp.contrato = contrato.codigo
    LEFT JOIN condicaopagamento cp ON cp.codigo = ccp.condicaopagamento
    LEFT JOIN contratomodalidade cmod ON cmod.contrato = contrato.codigo
    LEFT JOIN modalidade mod ON cmod.modalidade = mod.codigo
    LEFT JOIN aulaavulsadiaria ON aulaavulsadiaria.codigo = movparcela.aulaavulsadiaria
    LEFT JOIN vendaavulsa ON vendaavulsa.codigo = movparcela.vendaavulsa
    LEFT JOIN controletaxapersonal ON controletaxapersonal.codigo = movparcela.personal
    LEFT JOIN usuario ON usuario.codigo = movparcela.responsavel
    LEFT JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = movparcela.codigo
    LEFT JOIN pessoa pesev ON pesev.codigo = movparcela.pessoa
    LEFT JOIN cliente cliev ON cliev.pessoa = pesev.codigo
    LEFT JOIN movprodutoparcela mpp ON mpp.movparcela = movparcela.codigo
    LEFT JOIN recibopagamento rp ON mpp.recibopagamento = rp.codigo
    LEFT JOIN pagamentomovparcela pmp ON movparcela.codigo = pmp.movparcela
    LEFT JOIN movpagamento mpag ON pmp.movpagamento = mpag.codigo
    LEFT JOIN formapagamento fp ON mpag.formapagamento = fp.codigo
    LEFT JOIN contratohorario chor ON chor.contrato = movparcela.contrato
    LEFT JOIN plano ON plano.codigo = contrato.plano
WHERE movparcela.datavencimento >= '2020-12-29 00:00:00'
  AND movparcela.datavencimento <= '2022-12-28 23:59:59'
  AND movparcela.situacao != 'RG'
GROUP BY 
         rp.data,
         nrtentativas,
         motivoretorno,
         oo.dataoperacao,
         movparcela.codigo, 
         cliente.codigo,
         movparcela.pessoa,
         data_previsao_receita,
         cliente_matricula,
         pessoa_nome,
         parcela_descricao,
         parcela_dataregistro,
         parcela_datavencimento,
         parcela_situacao,
         parcela_contrato,
         parcela_valorparcela,
         regime_recorrencia,
         datapagamento,
         emp.nome,
         emp.codigo,
         plano.descricao
ORDER BY 
         movparcela.codigo, 
         pessoa_nome
