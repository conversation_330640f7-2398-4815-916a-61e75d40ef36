WITH protocolo_descricao AS (
    SELECT 0 AS protocolo, 'Pollock 3 Dobras' AS descricao
    UNION ALL
    SELECT 1 AS protocolo, 'Pollock 7 Dobras' AS descricao
    UNION ALL
    SELECT 2 AS protocolo, '<PERSON><PERSON><PERSON>' AS descricao
    UNION ALL
    SELECT 3 AS protocolo, '<PERSON><PERSON><PERSON> Dobras' AS descricao
    UNION ALL
    SELECT 4 AS protocolo, 'Bioimpedancia' AS descricao
    UNION ALL
    SELECT 5 AS protocolo, '<PERSON><PERSON><PERSON> Obeso' AS descricao
    UNION ALL
    SELECT 6 AS protocolo, 'Pollock Adolescente: coxa, triceps; triceps, coxa' AS descricao
    UNION ALL
    SELECT 7 AS protocolo, 'Slaughter: panturrilha, triceps; triceps, panturrilha' AS descricao
    UNION ALL
    SELECT 8 AS protocolo, 'Yuhasz: abdominal, coxa, torax, triceps, suprailiaca, subescapular' AS descricao
    UNION ALL
    SELECT 9 AS protocolo, 'TG Lohman: coxa, triceps; coxa, triceps' AS descricao
)
SELECT av.dataavaliacao,
       cli.nome              AS nome_cliente,
       cli.codigocliente     AS codigo_cliente,
       cli.matricula         AS matricula_cliente,
       emp.nome              AS nome_empresa,
       av.protocolo,
       pd.descricao AS descricao_protocolo
FROM avaliacaofisica av
         INNER JOIN (SELECT cliente_codigo, MIN(codigo) AS primeira_avaliacao
                     FROM avaliacaofisica
                     GROUP BY cliente_codigo) AS primeiras_avaliacoes
                    ON av.cliente_codigo = primeiras_avaliacoes.cliente_codigo
                    AND av.codigo <> primeiras_avaliacoes.primeira_avaliacao
         INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo
         LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo
         LEFT JOIN empresa emp ON cli.empresa = emp.codzw
         LEFT JOIN protocolo_descricao pd ON av.protocolo = pd.protocolo;