select
    codigo,
    nomepagador,
    formapagamento,
    valor,
    valortotal,
    datalancamento,
    datapagamento,
    dataquitacao,
    recibopagamento,
    codigo_empresa,
    nome_empresa,
    cpf_pagador,
    produtospagos,
    datapagamentooriginal,
    descricao_tipo_forma_pagamento,
    tipo_forma_pagamento,
    descricao_forma_pagamento,
    codigo_forma_pagamento,
    matricula,
    parcelasPagas
from (
        select distinct
            movpagamento.*, CASE
                WHEN fp.tipoformapagamento = 'CA' THEN 'Cartão de Crédito'
                WHEN fp.tipoformapagamento = 'CD' THEN 'Cartão de Débito'
                WHEN fp.tipoformapagamento = 'CH' THEN 'Cheque'
                WHEN fp.tipoformapagamento = 'AV' THEN 'A vista'
                WHEN fp.tipoformapagamento = 'CC' THEN 'Conta Corrente'
                WHEN fp.tipoformapagamento = 'BB' THEN 'Boleto Bancário'
                WHEN fp.tipoformapagamento = 'CO' THEN 'Convênio'
                WHEN fp.tipoformapagamento = 'PD' THEN 'Pagamento Digital'
                WHEN fp.tipoformapagamento = 'LO' THEN 'Lote'
                WHEN fp.tipoformapagamento = 'PX' THEN 'PIX'
                ELSE 'Outro'
            END AS descricao_tipo_forma_pagamento, fp.tipoformapagamento as tipo_forma_pagamento, fp.descricao as descricao_forma_pagamento, fp.codigo as codigo_forma_pagamento, empresa.nome as nome_empresa, empresa.codigo as codigo_empresa, cli.matricula, cli.cpf as cpf_pagador, conta.descricao as descricao_conta, movc.dataquitacao as data_quitacao, array_to_string (
                array (
                    select mp.descricao
                    from
                        pagamentomovparcela p
                        inner join movparcela mp on mp.codigo = p.movparcela
                    where
                        p.movpagamento = movpagamento.codigo
                ), ', ', ''
            ) as parcelasPagas
        from
            movpagamento
            left join empresa ON empresa.codigo = movpagamento.empresa
            left outer join situacaoclientesinteticodw as cli on cli.codigopessoa = movpagamento.pessoa
            left outer join movconta as movc on movc.codigo = movpagamento.movconta
            left outer join conta on conta.codigo = movc.conta
            left join pessoa p on p.codigo = movpagamento.pessoa
            left outer join cheque on movpagamento.codigo = cheque.movpagamento
            and cheque.situacao not like 'CA'
            and cheque.situacao not like 'DV'
            left outer join cartaocredito on movpagamento.codigo = cartaocredito.movpagamento
            AND cartaocredito.situacao not like 'CA'
            inner join formapagamento fp on movpagamento.formapagamento = fp.codigo
            and fp.tipoformapagamento IN (
                'CA', 'CD', 'CH', 'AV', 'CC', 'BB', 'CO', 'PD', 'TB', 'PX'
            )
            inner join usuario u on u.codigo = movpagamento.responsavelpagamento
        where (
                movpagamento.recibopagamento is not null
                or movpagamento.credito = 't'
            )
            and (
                movpagamento.valor > 0
                or fp.tipoformapagamento = 'CC'
            )
            and (
                (
                    movpagamento.datapagamento >= '2017-01-01 00:00:00'
                    and movpagamento.datapagamento <= '2018-12-31 23:59:59'
                    and fp.tipoformapagamento IN (
                        'AV', 'CD', 'CC', 'BB', 'CO', 'TB', 'PX'
                    )
                )
                or (
                    cheque.datacompesancao >= '2017-01-01 00:00:00'
                    and cheque.datacompesancao <= '2018-12-31 23:59:59'
                    and fp.tipoformapagamento = 'CH'
                )
                or (
                    cartaocredito.datacompesancao >= '2017-01-01 00:00:00'
                    and cartaocredito.datacompesancao <= '2018-12-31 23:59:59'
                    and fp.tipoformapagamento = 'CA'
                )
            )
    ) as receita
order by datalancamento desc