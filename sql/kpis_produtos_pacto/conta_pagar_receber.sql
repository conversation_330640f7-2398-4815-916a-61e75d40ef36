SELECT
    mc.codigo,
    mc.tipoopera<PERSON><PERSON>,
    mc.valor,
    mc.valorpago,
    mc.dataquita<PERSON>o,
    mc.datalancamento,
    mc.datavencimento,
    mc.datacompetencia,
    mc.nfceemitida,
    CASE mc.tipooperacao
        WHEN 1 THEN 'Pagamento'
        WHEN 2 THEN 'Recebimento'
        WHEN 3 THEN 'Depósito'
        WHEN 4 THEN 'Transferência'
        WHEN 5 THEN 'Estorno'
        WHEN 6 THEN 'Troca de Forma de Pagamento'
        WHEN 7 THEN 'Ajuste de Saldo'
        WHEN 8 THEN 'Custódia'
        WHEN 9 THEN 'Recebível Avulso'
        WHEN 10 THEN 'Retirada de recebível de lote'
        WHEN 11 THEN 'Fluxo de Caixa'
        WHEN 12 THEN 'Devolução de cheques'
        ELSE 'Desconhecido'
    END AS descricao_tipooperacao,
    p.nome AS nome_pessoa,
    mc.empresa AS empresazw,
    e.nome AS nome_empresa
FROM
    movconta AS mc
JOIN
    pessoa AS p ON mc.pessoa = p.codigo
JOIN
    empresa AS e ON mc.empresa = e.codigo