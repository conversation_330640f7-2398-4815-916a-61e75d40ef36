SELECT
    ei.dataprocessamentoextrato,
    ei.datareprocessamento,
    ei.datalancamento,
    ei.valorbruto,
    ei.valorliquido,
    ei.valorcomissao,
    ei.credito,
    ei.antecipacao,
    CASE
        WHEN ei.tipoconciliacao = 4 THEN 'Por pagamentos'
        WHEN ei.tipoconciliacao = 3 THEN 'Por vendas'
        WHEN ei.tipoconciliacao = 5 THEN 'Cancelamento de pagamento'
        WHEN ei.tipoconciliacao = 6 THEN 'Chargeback'
        WHEN ei.tipoconciliacao = 7 THEN 'Taxas de Cancelamento'
        WHEN ei.tipoconciliacao = 8 THEN 'Estorno de Chargeback'
        ELSE 'Unknown'
    END AS descricao_tipo_conciliacao,
    CASE
        WHEN ei.situacao = 0 THEN 'OK'
        WHEN ei.situacao = 1 THEN 'PENDENCIAS'
        WHEN ei.situacao = 2 THEN 'AUTORIZACAO_NAO_EXISTE'
        WHEN ei.situacao = 3 THEN 'ESTORNADO_SISTEMA'
        WHEN ei.situacao = 4 THEN 'ESTORNADO_OPERADORA'
        ELSE 'Unknown'
    END AS situacao_descricao,
    e.nome AS nome_empresa,
    e.codigo AS codigo_empresa
FROM
    extratodiarioitem ei
JOIN
    empresa e ON ei.empresa = e.codigo
WHERE
    ei.tipoconciliacao IS NOT NULL