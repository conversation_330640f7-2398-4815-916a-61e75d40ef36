select
    e.nome as nomeempresa,
    e.cod_empresafinanceiro as cod_empresafinanceiro,
    e.codigo as cod_empresa,
    pes.nome as nomepessoa,
    ac.dataregistro,
    ac.dth<PERSON>rada as data_entrada,
    ac.dth<PERSON>ida as data_saida,
    ac.codigo as codigo_acesso,
    ac.situacao as situacao_acesso,
    case
        when ac.sentido = 'E' then 'Entrada'
        when ac.sentido = 'S' then 'Saí<PERSON>'
    end as sentido_acesso,
    case
        when ac.situacao = 'RV_BLOQRESTRICAOACESSO' then 'Acesso restrito a alunos dessa academia.'
        when ac.situacao = 'RV_BLOQALUNONAOCADASTRADO' then 'Cartão de aluno não cadastrado.'
        when ac.situacao = 'RV_BLOQALUNOMATNAOCADASTRADO' then 'Matrícula de aluno não cadastrada.'
        when ac.situacao = 'RV_BLOQCOLABORADORNAOCADASTRADO' then 'Cartão de colaborador não cadastrado.'
        when ac.situacao = 'RV_BLOQCOLABORADORCODNAOCADASTRADO' then 'Código de colaborador não cadastrado.'
        when ac.situacao = 'RV_BLOQEMPRESANAOCONFERE' then 'Cartão inválido para esta empresa.'
        when ac.situacao = 'RV_BLOQTAMCARTAOINVALIDO' then 'Transação inválida pelo tamanho.'
        when ac.situacao = 'RV_BLOQFORAHORARIO' then 'Fora do horário do Plano.'
        when ac.situacao = 'RV_BLOQFORAHORARIOTURMA' then 'Fora do horário da turma.'
        when ac.situacao = 'RV_BLOQCONTRATOTRANCADO' then 'Contrato trancado.'
        when ac.situacao = 'RV_BLOQCONTRATOFERIAS' then 'Contrato em férias.'
        when ac.situacao = 'RV_BLOQCONTATOVENCIDO' then 'Contrato vencido.'
        when ac.situacao = 'RV_BLOQCONTRATONAOINICIOU' then 'Contrato ainda não iniciado.'
        when ac.situacao = 'RV_BLOQEXAMEVENCIDO' then 'Avaliação ou exame médico vencido.'
        when ac.situacao = 'RV_BLOQMSGPERSONALIZADA' then 'Bloqueio por mensagem personalizada.'
        when ac.situacao = 'RV_BLOQACESSOSSEGUIDOS' then 'Bloqueio por acessos seguidos.'
        when ac.situacao = 'RV_BLOQCONTRATOATESTADOM' then 'Contrato em atestado médico.'
        when ac.situacao = 'RV_BLOQSTATUSALUNO' then 'Aluno não encontra-se ativo.'
        when ac.situacao = 'RV_BLOQSEMAUTORIZACAO' then 'Aluno não possui autorização de acesso.'
        when ac.situacao = 'RV_BLOQPLANOEMPRESA' then 'O plano do aluno não permite acesso a essa unidade.'
        when ac.situacao = 'RV_BLOQDVNAOCONFERE' then 'Dígito verificador não confere.'
        when ac.situacao = 'RV_BLOQCAPACIDADEMAXIMA' then 'Capacidade máxima atingida. Acesso não permitido.'
        when ac.situacao = 'RV_LIBACESSOAUTORIZADO' then 'Acesso autorizado.'
        when ac.situacao = 'RV_BLOQREGRA_LIBERACAO' then 'Bloqueio por regra de validação do terminal.'
        when ac.situacao = 'RV_BLOQPERSONAL' then 'Verificar Controle do Personal.'
        when ac.situacao = 'RV_BLOQCOLABORADORINATIVO' then 'Colaborador inativo.'
        when ac.situacao = 'RV_BLOQPESSOASENHAINVALIDA' then 'Senha inválida.'
        when ac.situacao = 'RV_BLOQALUNOPARCELAABERTA' then 'Por favor, compareça à Recepção.'
        when ac.situacao = 'RV_BLOQALUNOFREQUENCIAPLANO' then 'Quantidade máxima de frequência atingida.'
        when ac.situacao = 'RV_BLOQCARTEIRINHAVENCIDA' then 'Carteirinha vencida.'
        when ac.situacao = 'RV_LIBACESSOAUTORIZADOTOTALPASS' then 'Acesso autorizado por TotalPass.'
        when ac.situacao = 'RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO' then 'Limite de acessos diários TotalPass atingido.'
        when ac.situacao = 'RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS' then 'Realize o Check-in no aplicativo TotalPass.'
        when ac.situacao = 'RV_LIBACESSOAUTORIZADOGYMPASS' then 'Acesso autorizado por Gympass.'
        when ac.situacao = 'RV_BLOQGYMPASS5' then 'O usuário já visitou esta academia hoje.'
        when ac.situacao = 'RV_BLOQGYMPASS11' then 'Token Diário inválido.'
        when ac.situacao = 'RV_BLOQGYMPASS12' then 'Validador não autorizado. Um passe só é válido na academia onde foi comprado.'
        when ac.situacao = 'RV_BLOQGYMPASS13' then 'Token já foi usado hoje.'
        when ac.situacao = 'RV_BLOQGYMPASS14' then 'Item não está disponível devido a problemas com o pagamento.'
        when ac.situacao = 'RV_BLOQGYMPASS15' then 'Passe já foi completamente usado.'
        when ac.situacao = 'RV_BLOQGYMPASS16' then 'Passe já expirou e não pode mais ser usado.'
        when ac.situacao = 'RV_BLOQGYMPASS21' then 'Número de Token Diário inválido.'
        when ac.situacao = 'RV_BLOQGYMPASS22' then 'Cartão não habilitado.'
        when ac.situacao = 'RV_BLOQGYMPASS23' then 'O aluno não fez check-in.'
        when ac.situacao = 'RV_BLOQGYMPASS24' then 'Sem permissão para validar passes diários.'
        when ac.situacao = 'RV_BLOQGYMPASS26' then 'Não há créditos.'
        when ac.situacao = 'RV_BLOQGYMPASS27' then 'Pessoa bloqueada.'
        when ac.situacao = 'RV_BLOQGYMPASS28' then 'Erro ao aprovar o cartão bancário.'
        when ac.situacao = 'RV_BLOQGYMPASS29' then 'Cartão desabilitado.'
        when ac.situacao = 'RV_BLOQGYMPASS30' then 'Cartão expirado.'
        when ac.situacao = 'RV_BLOQGYMPASS32' then 'Esta pessoa não tem passes disponíveis para essa academia.'
        when ac.situacao = 'RV_BLOQGYMPASS33' then 'Academia bloqueada.'
        when ac.situacao = 'RV_BLOQGYMPASS34' then 'Token diário desativado.'
        when ac.situacao = 'RV_BLOQGYMPASS35' then 'Token Diário expirou.'
        when ac.situacao = 'RV_BLOQGYMPASS38' then 'Pessoa não está na lista de permitidos para essa academia.'
        when ac.situacao = 'RV_BLOQGYMPASS39' then 'Número máximo permitido de vezes na semana foi excedido.'
        when ac.situacao = 'RV_BLOQGYMPASS40' then 'Número máximo permitido de vezes este mês foi excedido.'
        when ac.situacao = 'RV_BLOQGYMPASS41' then 'Nenhuma reserva foi encontrada para esta pessoa.'
        when ac.situacao = 'RV_BLOQGYMPASS42' then 'É muito cedo para validar esta reserva.'
        when ac.situacao = 'RV_BLOQGYMPASS43' then 'É tarde demais para validar esta reserva.'
        when ac.situacao = 'RV_BLOQGYMPASS45' then 'O usuário ainda não fez check-in.'
        when ac.situacao = 'RV_BLOQGYMPASS46' then 'Usuário fez check-in em outra academia.'
        when ac.situacao = 'RV_BLOQGYMPASS47' then 'User Check In para essa academia expirou.'
        when ac.situacao = 'RV_BLOQGYMPASSGENERICO' then 'Token Gympass não foi validado.'
        when ac.situacao = 'RV_GYMPASS_AGUARDANDO_RESPOSTA' then 'Aguardando resposta da Gympass.'
        when ac.situacao = 'RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO' then 'Limite de acessos diários Gympass atingido.'
        when ac.situacao = 'RV_BLOQALUNOSEMASSINATURA' then 'Verificar Assinatura Digital.'
        when ac.situacao = 'RV_BLOQALUNOCAPACIDADESIMULTANEA' then 'Academia está lotada.'
        when ac.situacao = 'RV_BLOQSEMCARTAOVACINA' then 'Sem comprovante de vacinação apresentado.'
        when ac.situacao = 'RV_BLOQALUNOSEMPARQASSINADO' then 'Aluno sem Par-Q assinado.'
        when ac.situacao = 'RV_BLOQCREFVENCIDO' then 'CREF do colaborador está vencido.'
        when ac.situacao = 'RV_BLOQPLANOEMPRESA_PERSONAL' then 'O plano do personal não permite acesso a essa unidade.'
        when ac.situacao = 'RV_BLOQALUNOSEMTERMORESPONSABILIDADE' then 'Aluno sem Termo de Responsabilidade assinado.'
    end as situacao_descricao,
    case
        when ac.situacao in (
            'RV_LIBACESSOAUTORIZADO',
            'RV_LIBACESSOAUTORIZADOTOTALPASS',
            'RV_LIBACESSOAUTORIZADOGYMPASS'
        ) then 'Liberado'
        else 'Bloqueado'
    end as liberacao_acesso,
    loc.descricao as descricao_local,
	case
		when extract(hour from dthrentrada) between 0 and 11 then 'Manhã'
		when extract(hour from dthrentrada) between 12 and 17 then 'Tarde'
		when extract(hour from dthrentrada) between 18 and 24 then 'Noite'
		else null
	end as periododia
from
    acessocliente ac
    inner join cliente cli on cli.codigo = ac.cliente
    inner join pessoa pes on pes.codigo = cli.pessoa
    inner join empresa e on e.codigo = cli.empresa
    left join localacesso loc on loc.codigo = ac.localacesso

WHERE ac.dthrentrada >= now() - interval '7 day'

