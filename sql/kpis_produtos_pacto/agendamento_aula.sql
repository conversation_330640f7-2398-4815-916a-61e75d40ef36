SELECT 'marcada_aula_coletiva' AS tipo_agendamento,
       cliente.matricula       AS matricula_aluno,
       pessoa.nome             AS nome_aluno,
       datalancamento          AS data_lancamento,
       empresa.nome            AS nome_empresa,
       CASE
           WHEN origemsistema = 1 THEN 'ZillyonWeb'
           WHEN origemsistema = 2 THEN 'Agenda Web'
           WHEN origemsistema = 3 THEN 'Pacto Treino'
           WHEN origemsistema = 4 THEN 'App Treino'
           WHEN origemsistema = 5 THEN 'App Professor'
           WHEN origemsistema = 6 THEN 'Autoatendimento'
           WHEN origemsistema = 7 THEN 'Site Vendas'
           WHEN origemsistema = 8 THEN 'Buzz Lead'
           WHEN origemsistema = 9 THEN 'Vendas 2.0'
           WHEN origemsistema = 10 THEN 'App do consultor'
           WHEN origemsistema = 11 THEN 'Booking Gympass'
           WHEN origemsistema = 12 THEN 'Fila de espera'
           WHEN origemsistema = 13 THEN 'Importação API'
           WHEN origemsistema = 14 THEN 'Hubspot Lead'
           WHEN origemsistema = 15 THEN 'CRM Meta Diaria'
           WHEN origemsistema = 16 THEN 'Pacto Flow'
           WHEN origemsistema = 17 THEN 'Nova Tela de Negociação'
           ELSE 'Outro'
           END                 AS origemsistema
FROM alunohorarioturma
    INNER JOIN horarioturma ON horarioturma.codigo = alunohorarioturma.horarioturma
    INNER JOIN turma ON turma.codigo = horarioturma.turma
    LEFT JOIN empresa ON empresa.codigo = turma.empresa
    INNER JOIN cliente ON cliente.codigo = alunohorarioturma.cliente
    INNER JOIN pessoa ON cliente.pessoa = pessoa.codigo
UNION ALL
SELECT 'feito_resposicao' AS tipo_agendamento,
       cliente.matricula  AS matricula_aluno,
       pessoa.nome        AS nome_aluno,
       datalancamento     AS data_lancamento,
       empresa.nome       AS nome_empresa,
       CASE
           WHEN origemsistema = 1 THEN 'ZillyonWeb'
           WHEN origemsistema = 2 THEN 'Agenda Web'
           WHEN origemsistema = 3 THEN 'Pacto Treino'
           WHEN origemsistema = 4 THEN 'App Treino'
           WHEN origemsistema = 5 THEN 'App Professor'
           WHEN origemsistema = 6 THEN 'Autoatendimento'
           WHEN origemsistema = 7 THEN 'Site Vendas'
           WHEN origemsistema = 8 THEN 'Buzz Lead'
           WHEN origemsistema = 9 THEN 'Vendas 2.0'
           WHEN origemsistema = 10 THEN 'App do consultor'
           WHEN origemsistema = 11 THEN 'Booking Gympass'
           WHEN origemsistema = 12 THEN 'Fila de espera'
           WHEN origemsistema = 13 THEN 'Importação API'
           WHEN origemsistema = 14 THEN 'Hubspot Lead'
           WHEN origemsistema = 15 THEN 'CRM Meta Diaria'
           WHEN origemsistema = 16 THEN 'Pacto Flow'
           WHEN origemsistema = 17 THEN 'Nova Tela de Negociação'
           ELSE 'Outro'
           END            AS origemsistema
FROM reposicao
    INNER JOIN horarioturma ON horarioturma.codigo = reposicao.horarioturma
    INNER JOIN turma ON turma.codigo = horarioturma.turma
    LEFT JOIN empresa ON empresa.codigo = turma.empresa
    INNER JOIN cliente ON cliente.codigo = reposicao.cliente
    INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa
UNION ALL
SELECT 'aula_desmarcada' AS tipo_agendamento,
       cliente.matricula AS matricula_aluno,
       pessoa.nome       AS nome_aluno,
       datalancamento    AS data_lancamento,
       empresa.nome      AS nome_empresa,
       CASE
           WHEN origemsistema = 1 THEN 'ZillyonWeb'
           WHEN origemsistema = 2 THEN 'Agenda Web'
           WHEN origemsistema = 3 THEN 'Pacto Treino'
           WHEN origemsistema = 4 THEN 'App Treino'
           WHEN origemsistema = 5 THEN 'App Professor'
           WHEN origemsistema = 6 THEN 'Autoatendimento'
           WHEN origemsistema = 7 THEN 'Site Vendas'
           WHEN origemsistema = 8 THEN 'Buzz Lead'
           WHEN origemsistema = 9 THEN 'Vendas 2.0'
           WHEN origemsistema = 10 THEN 'App do consultor'
           WHEN origemsistema = 11 THEN 'Booking Gympass'
           WHEN origemsistema = 12 THEN 'Fila de espera'
           WHEN origemsistema = 13 THEN 'Importação API'
           WHEN origemsistema = 14 THEN 'Hubspot Lead'
           WHEN origemsistema = 15 THEN 'CRM Meta Diaria'
           WHEN origemsistema = 16 THEN 'Pacto Flow'
           WHEN origemsistema = 17 THEN 'Nova Tela de Negociação'
           ELSE 'Outro'
           END           AS origemsistema
FROM auladesmarcada
    INNER JOIN horarioturma ON horarioturma.codigo = auladesmarcada.horarioturma
    INNER JOIN turma ON turma.codigo = horarioturma.turma
    LEFT JOIN empresa ON empresa.codigo = turma.empresa
    INNER JOIN cliente ON cliente.codigo = auladesmarcada.cliente
    INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa