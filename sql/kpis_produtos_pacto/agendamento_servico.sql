select datalancamento,
       status,
       CASE
           WHEN status = 1 THEN 'Agendado'
           WHEN status = 2 THEN 'Confirmado'
           WHEN status = 3 THEN 'Cancelado'
           WHEN status = 4 THEN 'Concluído'
           ELSE 'Desconhecido'
       END as descricao_status,
       inicio,
       fim,
       c.matricula as matricula_aluno,
       c.nome as nome_aluno,
       e.nome as nome_empresa
from agendamento a
inner join clientesintetico c on a.cliente_codigo = c.codigo
inner join empresa e on e.codigo = c.empresa
where disponibilidade is false
  and cliente_codigo is not null