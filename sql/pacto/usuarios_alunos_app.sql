select
    'ALUNOS APP' as usuario_tipo,
    emp.nome as empresa_nome,
    pes.datacadastro as pessoa_data_cadastro,
    pes.codigo as pessoa_codigo,
    pes.nome as pessoa_nome,
    pes.datanasc,
    pes.sexo,
    uf.sigla as uf,
    cid.nome as cidade,
    array_to_string(
        array_agg(
            distinct regexp_replace(tel.numero, '\D', '', 'g')
        ),
        ','
    ) as telefones,
    array_to_string(array_agg(distinct lower(em.email)), ',') as emails,
    cli.codigo as codigo,
    cli.matricula as matricula,
    cli.situacao as situacao,
    cli.codacesso as codacesso,
    um.nome as usuario_username
from
    usuariomovel um
    inner join cliente cli on cli.codigo = um.cliente
    inner join pessoa pes on pes.codigo = cli.pessoa
    inner join empresa emp on emp.codigo = cli.empresa and emp.ativa
    left join telefone tel on tel.pessoa = pes.codigo
    left join email em on em.pessoa = pes.codigo
    left join estado uf on uf.codigo = pes.estado
    left join cidade cid on cid.codigo = pes.cidade
where
    um.ativo
group by
    emp.nome,
    pes.datacadastro,
    pes.codigo,
    pes.nome,
    pes.datanasc,
    pes.sexo,
    uf.sigla,
    cid.nome,
    cli.codigo,
    cli.matricula,
    cli.situacao,
    cli.codacesso,
    um.nome