-- PEGA HITORICO DE USUARIOS POR RECURSO -----
  select
     i.codigo,
 	   e.cod_empresaf<PERSON>nce<PERSON> ,     
     i.dataregistro,
     i.usuario,
     u.nome , 
     u.username ,
     u.ultimoacesso ,
     i.tipoinfo,
     te.descricao as tela_descricao,
     -- i.info,
     case when i.info = 'true' then 'NOVA' else 'ANTIGA' end as configurada
     --   
     ,(   select case when coalesce(ix.padrao,false) then 'FIXA' else 'OPCIONAL' end as tela_nova
          from infomigracaohistoricoempresa ix 
          where ix.tipoinfo = i.tipoinfo and ix.dataregistro::date = i.dataregistro::date ) as rec_ep_padrao
     --
 from infomigracaohistorico i
    inner join usuario u on u.codigo = i.usuario
    left join tabelagenericaenum te on upper(te.nomeenum) = 'TIPOINFOMIGRACAOENUM' and te.codigo = i.tipoinfo::text
  	left join colaborador c on c.codigo = u.colaborador
  	left join empresa e on e.codigo = c.empresa  
 where i.tipoinfo not in (2,3)
   and upper (u.username) not in ('ADMIN','PACTOBR','RECOR')
   and i.usuario_ativo
--   and i.dataregistro > '2025-05-01 00:00:00'
   AND i.dataregistro >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '2 months'   
 --order by u.ultimoacesso desc  --i.tipoinfo;
--limit 10
;