SELECT
    c.codigo AS codigocontrato,
    c.data<PERSON><PERSON><PERSON>,
    cli.codigocliente AS cliente,
    cli.situacao,
    cli.colaboradores,
    cli.matricula,
    cli.nomecliente AS nome,
    c.situacaorenovaca<PERSON>,
    c.<PERSON><PERSON><PERSON><PERSON>,
    c.datarenovarrealizada::date,
    c.dataprevistarenovar::date,
    c.empresa,
    c.renovavelautomaticamente,
    e.nome AS Unidade,
    e.cod_empresafinanceiro AS Unidade_IdPacto,
    e.codigorede AS Unidade_IdRede
FROM
    contrato c
    INNER JOIN SituacaoClienteSinteticoDW cli ON cli.codigopessoa = c.pessoa
    INNER JOIN empresa e ON e.codigo = c.empresa
WHERE
    c.dataprevistarenovar >= '2023-06-01' AND
    c.datarenovarrealizada >= '2023-06-01';
