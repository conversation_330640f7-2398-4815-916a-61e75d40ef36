select
    sql.empresa_financeiro,
    sql.empresa_nome,
    sql.forma_pagamento,
    sql.adquirente,
    sql.bandeira,
    to_char(sql.data_venda, 'YYYY/MM') as data_venda,
    sql.extrato_antecipacao,
    sql.temExtratoStone,
    case when sql.extrato_antecipacao then 'SIM' else 'NAO' end as extrato_antecipacao_str,
    case when sql.temExtratoStone then 'SIM' else 'NAO' end as temExtratoStone_str,
    round(sql.extrato_taxa_30_dias_antecipacao::numeric, 3) as extrato_taxa_30_dias_antecipacao,
    sum(sql.valor) as valor
from (
         select
             mp.codigo as movpagamento,
             e.nome as empresa_nome,
             e.codigo                as empresa_codigo,
             e.cod_empresafinanceiro as empresa_financeiro,
             case
                 when fp.tipoformapagamento = 'AV' then 'DINHEIRO'
                 when fp.tipoformapagamento = 'CH' then 'CHEQUE'
                 when fp.tipoformapagamento = 'CA' then 'CARTAO_CREDITO'
                 when fp.tipoformapagamento = 'CD' then 'CARTAO_DEBITO'
                 when fp.tipoformapagamento = 'CO' then 'CONVENIO'
                 when fp.tipoformapagamento = 'CC' then 'CONTA_CORRENTE'
                 when fp.tipoformapagamento = 'BB' then 'BOLETO'
                 when fp.tipoformapagamento = 'PD' then 'PAGAMENTO_DIGITAL'
                 when fp.tipoformapagamento = 'LO' then 'LOTE'
                 when fp.tipoformapagamento = 'PF' then 'PARCEIRO_FIDELIDADE'
                 when fp.tipoformapagamento = 'TB' then 'TRANSFERENCIA_BANCARIA'
                 when fp.tipoformapagamento = 'PX' then 'PIX'
                 else 'OUTRA' end    as forma_pagamento,
             case
                 when (tr.tipo = 11) then 'STONE'
                 when (tr.tipo = 3 or re.tipo = 2) then 'CIELO'
                 when (re.tipo = 12) then 'BIN'
                 when (tr.tipo = 10 or re.tipo = 8) then 'GETNET'
                 when (tr.tipo = 2) then 'VINDI'
                 when (tr.tipo = 13) then 'PAGAR_ME'
                 when (tr.tipo = 15) then 'STRIPE'
                 when (tr.tipo = 16) then 'PAGO_LIVRE'
                 when (tr.tipo = 17) then 'VALORI_BANK'
                 when (tr.tipo = 7) then 'REDE'
                 when (ad.nome ilike '%STONE%') then 'STONE'
                 when (ad.nome ilike '%CIELO%') then 'CIELO'
                 when (ad.nome ilike '%BIN%') then 'BIN'
                 when (ad.nome ilike '%GETNET%' or ad.nome ilike '%GET NET%') then 'GETNET'
                 when (ad.nome ilike '%VINDI%') then 'VINDI'
                 when (ad.nome ilike '%PAGARME%') then 'PAGAR_ME'
                 when (ad.nome ilike '%STRIPE%') then 'STRIPE'
                 when (ad.nome ilike '%PAGOLIVRE%') then 'PAGO_LIVRE'
                 when (ad.nome ilike '%VALORIBANK%' or ad.nome ilike '%VALORI BANK%') then 'VALORI_BANK'
                 when ad.nome ilike '%SAFRA%' then 'SAFRA'
                 when ad.nome ilike '%FROGPAY%' then 'FROGPAY'
                 when ad.nome ilike '%SIPAG%' then 'SIPAG'
                 when ad.nome ilike '%SISPAG%' then 'SISPAG'
                 when ad.nome ilike '%GLOBAL%' then 'GLOBAL_PAYMENTS'
                 when ad.nome ilike '%VIPPAY%' then 'VIPPAY'
                 when ad.nome ilike '%KREDIT%' then 'KREDIT'
                 when ad.nome ilike '%FLEXPAG%' then 'FLEXPAG'
                 when ad.nome ilike '%BCPAG%' then 'BCPAG'
                 when (ad.nome ilike '%MERCADOPAGO%' or ad.nome ilike '%MERCADO PAGO%')then 'MERCADO_PAGO'
                 when (ad.nome ilike '%PAGSEGURO%' or ad.nome ilike '%PAG SEGURO%')then 'PAG_SEGURO'
                 when (ad.nome ilike '%REDE%') then 'REDE'
                 when ((fp.tipoformapagamento = 'CA' or fp.tipoformapagamento = 'CD') and ad.codigo is null) then 'NAO_INFORMADO'
                 when ad.codigo is null then ''
                 else 'OUTRA' end    as adquirente,
             case
                 when length(coalesce(split_part(split_part(mp.respostarequisicaopinpad, 'cardBrandName":"', 2), '"', 1),'')) > 0 then replace(split_part(split_part(mp.respostarequisicaopinpad, 'cardBrandName":"', 2), '"', 1),' ' ,'')
                 when length(coalesce(split_part(split_part(tr.outrasinformacoes, 'cartaoBandeira":"', 2), '"', 1), '')) > 0 then replace(split_part(split_part(tr.outrasinformacoes, 'cartaoBandeira":"', 2), '"', 1),' ' ,'')
                 when length(coalesce(split_part(split_part(split_part(ri.props, 'Bandeira=', 2), ',', 1), '}', 1), '')) > 0 then replace(split_part(split_part(split_part(ri.props, 'Bandeira=', 2), ',', 1), '}', 1),' ' ,'')
                 when o.descricao ilike '%MAESTRO%' then 'MAESTRO'
                 when o.descricao ilike '%ELECTRON%' then 'ELECTRON'
                 when o.descricao ilike '%VISA%' then 'VISA'
                 when o.descricao ilike '%MASTER%' then 'MASTERCARD'
                 when o.descricao ilike '%HIPERCARD%' then 'HIPERCARD'
                 when o.descricao ilike '%HIPER CARD%' then 'HIPERCARD'
                 when o.descricao ilike '%HIPER%' then 'HIPER'
                 when (o.descricao ilike '%AMERICAN%' or o.descricao ilike '%AMEX%') then 'AMEX'
                 when o.descricao ilike '%ALELO%' then 'ALELO'
                 when o.descricao ilike '%CABAL%' then 'CABAL'
                 when o.descricao ilike '%DISCOVER%' then 'DISCOVER'
                 when o.descricao ilike '%JCB%' then 'JCB'
                 when (o.descricao ilike '%DINERS%' or o.descricao ilike '%DINNERS%') then 'DINERS'
                 when o.descricao ilike '%ELO%' then 'ELO'
                 when ((fp.tipoformapagamento = 'CA' or fp.tipoformapagamento = 'CD') and o.codigo is null) then 'NAO_INFORMADO'
                 when o.codigo is null then ''
                 else 'OUTRA' end    as bandeira,
             mp.datalancamento::date as data_venda,
                 case
                     when cc.codigo is not null then cc.datacompesancao::date
                    else mp.datapagamento::date end as data_compensacao,
                    case
                    when fp.tipoformapagamento = 'CA' then coalesce(mp.nrparcelacartaocredito, 0)
                    else null end       as nr_vezes_zw,
                    cc.nrparcela as cartao_nrparcela,
		    case
                    when cc.codigo is not null then cc.valor::numeric
                    else mp.valortotal::numeric end as valor,
		    edi.nrparcela   as extrato_nrparcela,
                    edi.nrtotalparcelas as extrato_nrtotalparcelas,
                    edi.taxa as extrato_taxa,
                    coalesce(edi.antecipacao,false) as extrato_antecipacao,
                    (edi.taxacalculadaantecipacao / extract(day from datapgtooriginalantesdaantecipacao - dataprevistapagamento)) * 30 as extrato_taxa_30_dias_antecipacao,
					exists (select codigo from extratodiarioitem where empresa = e.codigo and tipoconveniocobranca in (21,36) ) as temExtratoStone
         from movpagamento mp
             inner join empresa e on e.codigo = mp.empresa
             inner join formapagamento fp on fp.codigo = mp.formapagamento
             left join cartaocredito cc on cc.movpagamento = mp.codigo and cc.situacao = 'EA'
             left join adquirente ad on ad.codigo = mp.adquirente
             left join usuario usm on usm.codigo = mp.responsavelpagamento
             left join operadoracartao o on o.codigo = mp.operadoracartao
             left join pix px on px.recibopagamento = mp.recibopagamento
             left join conveniocobranca ccp on ccp.codigo = px.conveniocobranca
             left join usuario usp on usp.codigo = px.usuarioresponsavel
             left join boleto bl on bl.movpagamento = mp.codigo
             left join conveniocobranca ccb on ccb.codigo = bl.conveniocobranca
             left join usuario usb on usb.codigo = bl.usuario
             left join transacao tr on tr.movpagamento = mp.codigo
             left join conveniocobranca cct on cct.codigo = tr.conveniocobranca
             left join usuario ust on ust.codigo = tr.usuarioresponsavel
             left join remessaitem ri on ri.movpagamento = mp.codigo
             left join remessa re on re.codigo = ri.remessa
             left join conveniocobranca ccr on ccr.codigo = re.conveniocobranca
             left join banco bar on bar.codigo = ccr.banco
             left join usuario usr on usr.codigo = re.usuario
             left join extratodiarioitem edi on edi.codigo = (select max(codigo) from extratodiarioitem  where (codigocartaocredito = cc.codigo or (cc.codigo is null and codigomovpagamento = mp.codigo)))
         where mp.valor > 0
           and mp.datalancamento::date >= '01/06/2023'
         order by mp.codigo,cc.nrparcela
     ) as sql
where adquirente ilike '%stone%'
group by 1,2,3,4,5,6,7,8,9,10,11
order by 1,6,3