-- Consul<PERSON> todas as transaçoes de cobranças do cartão de credito  || a diferença dessa com a de erro, é que essa puxa as transações que deram certo também
select
    e.cod_empresafinanceiro as empresa_financeiro,
    tcc.descricao as adquirente,
    t.dataprocessamento as data_transacao,
    t.valor,
    t.situacao,
    upper(split_part(split_part(t.outrasinformacoes, 'cartaoBandeira":"',2),'"',1)) as bandeira,
    t.codigoretorno as codigoretorno_sistema,
    case 
        when t.codigoretorno ilike 'PAC%' then 
              split_part(split_part(split_part(replace(replace(t.codigoretornodescricao,' ', ' '),'<br/>', ' '), 'Motivo:', 2),'-',1),'</br>',1) 
        else t.codigoretorno 
    end as codigoretorno_adquirente,
    t.codigoretornodescrica<PERSON> as codigoretorno_descricao
from transacao t 
    left join empresa e on e.codigo = t.empresa
    left join conveniocobranca cc on cc.codigo = t.conveniocobranca
    left join tipoconveniocobranca tcc on tcc.codigo = cc.tipoconvenio
where t.dataprocessamento::date >= '01/07/2022'
order by t.dataprocessamento;
