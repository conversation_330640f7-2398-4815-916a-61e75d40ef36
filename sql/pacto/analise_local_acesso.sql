select
	distinct la.codigo as codLocal,
	la.descricao as localAcesso,
	la.nomecomputador,
	la.servidorimpressoes,
	col.codigo as codColetor,
	col.descricao as coletor,
	col.modelo,
	la.databaseoffline,
	la.datadownloadbase,
	emp.nome as empresa,
	es.sigla as estado,
	ci.nomesemacento as cidade,
	(
	select
		count(*)
	from
		cliente
	where
		situacao = 'AT'
		and empresa = emp.codigo) as ativos,
	la.versaoacesso,
	(
	select
		dthrentrada
	from
		acessocliente
	where
		localacesso = la.codigo
	order by
		codigo desc
	limit 1) as ultimoacesso,
	date_part('day',
	(
	select
		dthrentrada
	from
		acessocliente
	where
		localacesso = la.codigo
	order by
		codigo desc
	limit 1)) as diaUltAcesso,
	date_part('month',
	(
	select
		dthrentrada
	from
		acessocliente
	where
		localacesso = la.codigo
	order by
		codigo desc
	limit 1)) as mesUltAcesso,
	date_part('year',
	(
	select
		dthrentrada
	from
		acessocliente
	where
		localacesso = la.codigo
	order by
		codigo desc
	limit 1)) as anoUltAcesso,
	s2.nomecomputador as servidorfacial
from
	coletor col
inner join localacesso la on
	col.localacesso = la.codigo
inner join empresa emp on
	la.empresa = emp.codigo
inner join estado es on
	es.codigo = emp.estado
inner join cidade ci on
	ci.codigo = emp.cidade
left join servidorfacial s2 on
	upper(s2.nomecomputador) = upper(la.nomecomputador)
where
	emp.ativa
	and (col.modelo <> 'MODELO_COLETOR_DESCONHECIDO'
		or col.padraocadastro = 'f')
	and col.desativado = 'f'
	and la.descricao not like '%NOME EMPRESA%'
	and la.descricao not like '%PACTO%'
	and la.descricao not like '%GQS%'
	and la.descricao not like '%TESTE%'
	and la.descricao not like '%DESENV%'
	and la.descricao not like '%INTEGR%'
	and la.versaoacesso <> ''
