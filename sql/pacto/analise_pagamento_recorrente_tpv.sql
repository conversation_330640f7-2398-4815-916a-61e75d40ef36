with faixaclientes as (         
                    SELECT 
                      e.cod_empresafinanceiro as unidade_idpacto,
                      case
                        when count(s.codigocliente) <= 100 then '-100'
                        when count(s.codigocliente) > 100 and count(codigocliente) <= 200  then '101-200'
                        when count(s.codigocliente) > 200 and count(codigocliente) <= 300  then '201-300'
                        when count(s.codigocliente) > 300 and count(codigocliente) <= 400  then '301-400'
                        when count(s.codigocliente) > 400 and count(codigocliente) <= 500  then '401-500'
                        when count(s.codigocliente) > 500 and count(codigocliente) <= 800  then '501-800'
                        when count(s.codigocliente) > 800 and count(codigocliente) <= 1000  then '801-1000'
                        else '1000+'
                      end as faixaclientes
                    FROM situacaoclientesinteticodw s
                    inner join empresa e on e.codigo = s.empresacliente
                    WHERE situacao = 'AT'
                    group by unidade_idpacto
        )

-- Tpv-previsao.     Rota do dinheiro da academia.....
select
  sql.*,
  case
    when sql.contrato is not null then p2.descricao 
    else 'OUTROS' end as Produto_Tipo,
  fc.faixaclientes as unidade_faixa_clientes,
--
  con.situacao          as Contrato_Situacao,
  p.descricao as descricao_plano,
  DATE_PART('month', AGE(con.vigenciaate, con.vigenciade)) 
  + 12*DATE_PART('year', AGE(con.vigenciaate, con.vigenciade))
  + case when DATE_PART('day', AGE(con.vigenciaate, con.vigenciade))>=28 then 1 else 0 end
  AS duracaocontratomeses, 
  con.vigenciade as vigenciade,
  con.vigenciaateajustada as vigenciaate,
  sitCli.situacaocontrato as cliente_situacao_contrato
--    */
from (
--
---INICIO Recebido
     select
         e.cod_empresafinanceiro                           as empresa_financeiro,
         'RECEBIDO' as tiporegistro,
         mp.codigo as id_primario,
         mp.pessoa as pessoa,
         case
             when rp.contrato is not null then rp.contrato
             else (select
                       max(m1.contrato)
                   from movparcela m1
                            inner join pagamentomovparcela p1 on p1.movparcela = m1.codigo
                   where p1.movpagamento = mp.codigo) end as contrato,
- -             
         case
             when fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX') then mp.valortotal::numeric
			 when cc.codigo is not null then cc.valor::numeric
			 when ch.codigo is not null then ch.valor::numeric
			 else null end as valor,
-- 
         (select min(mpro.datalancamento)
             from movproduto mpro
             inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo
             inner join pagamentomovparcela pmp on pmp.movparcela = mpp.movparcela
             where pmp.movpagamento = mp.codigo) AS dt_faturamento,
    -- 
         NULL as dt_vencimento,
         fp.descricao as forma_pagamento_descricao,
         'PG' as parcela_status
         from movpagamento mp
             inner join empresa e on e.codigo = mp.empresa
             inner join formapagamento fp on fp.codigo = mp.formapagamento
             left join recibopagamento rp on rp.codigo = mp.recibopagamento
             LEFT JOIN movpagamento mpa1 ON mpa1.movpagamentoorigemcredito = mp.codigo AND mpa1.valor > 0 AND mpa1.credito AND mpa1.movpagamentoorigemcredito IS NOT NULL and fp.tipoformapagamento  = 'CA'
             left join cartaocredito cc on (cc.movpagamento = mp.codigo or cc.movpagamento = mpa1.codigo) AND cc.situacao = 'EA'
             left join cheque ch on ch.movpagamento = mp.codigo and ch.situacao = 'EA'
         where mp.credito = false
---FIM Recebido
--
--
         union all
--
--
---INICIO Previsao
         SELECT
             e.cod_empresafinanceiro                           as empresa_financeiro,
            'PREVISAO' as tiporegistro,
             mp.codigo as id_primario,
             mp.pessoa AS pessoa,
             mp.contrato as contrato,
             mp.valorparcela::numeric          AS valor,
--
             (select min(mpro.datalancamento)
             from movproduto mpro
             inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo
             where mpp.movparcela = mp.codigo) AS dt_faturamento,
--
             mp.datavencimento::timestamp as dt_vencimento,
--
             case
             when bp.codigo is not null then 'BOLETO BANCÁRIO'
             when px.codigo is not null then 'PIX'
             when acc.codigo is not null then 'CARTÃO RECORRENTE'
             when abc.codigo is not null then 'BOLETO BANCÁRIO'
             when fpu.codigo is not null then fpu.descricao
             else 'SEM COBRANÇA CADASTRADA' end AS forma_pagamento_descricao,
--
             mp.situacao as parcela_status
         FROM   movparcela mp
             inner join empresa e on e.codigo = mp.empresa
             left join cliente cli ON cli.pessoa = mp.pessoa
             left join pix px on px.codigo = (select max(pm.pix) from pixmovparcela pm where pm.movparcela = mp.codigo)
             left join boleto bp on bp.codigo = (select max(bm.boleto) from boletomovparcela bm inner join boleto bol on bol.codigo = bm.boleto where bol.situacao in (3,4,5) and bm.movparcela = mp.codigo)
             left join autorizacaocobrancacliente auc on auc.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 1)
             left join conveniocobranca acc on acc.codigo = auc.conveniocobranca
             left join autorizacaocobrancacliente aub on aub.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 3)
             left join conveniocobranca abc on abc.codigo = aub.conveniocobranca
             left join movpagamento mpu on mpu.codigo = (select max(mp1.codigo) from movpagamento mp1 where mp1.pessoa = mp.pessoa)
             left join formapagamento fpu on fpu.codigo = mpu.formapagamento
             left join vendaavulsa va on mp.vendaavulsa = va.codigo
         WHERE  mp.situacao IN ('EA','CA')
           AND mp.valorparcela > 0
--
---FIM Previsao
     ) as sql
--  Para pegar dados do contrato e do Produto do plano -------------------------------------------------------------
    left join contrato con on con.codigo = sql.contrato
    left join plano p on p.codigo  = con.plano
    left join produto p2 on p2.codigo = p.produtopadraogerarparcelascontrato
    left join colaborador c2  on c2.codigo = con.consultor
    left join pessoa pc on pc.codigo = c2.pessoa
--  Para pegar dados pessoa que fez a compra...
    left join pessoa pesCliente on sql.pessoa = pescliente.codigo
    left join situacaoclientesinteticodw sitCli on sql.pessoa = sitCli.codigopessoa
    left join faixaclientes fc on CAST(fc.unidade_idpacto as TEXT) = CAST(sql.empresa_financeiro as TEXT)
    where dt_faturamento >= CURRENT_DATE - interval '3 years'
   