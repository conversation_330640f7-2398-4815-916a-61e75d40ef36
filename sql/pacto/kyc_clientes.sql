--kyc-last-3-months
select distinct
  c.codigo,
  c.matricula,
  p.nome as aluno,
  aut.nometitular<PERSON><PERSON><PERSON>,
  p.data<PERSON>,
  p.cfp as cpf,
  p.data<PERSON><PERSON><PERSON>,
  p.fotokey,
  aut.codigo as cod_aut,
  aut.cart<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o,
  aut.validade<PERSON><PERSON><PERSON>,
  t2.codigo as cod_tran,
  mp.descricao,
  t2.dataprocessamento,
  t2.valor,
  t2.co<PERSON><PERSON><PERSON><PERSON>,
  t2.co<PERSON><PERSON><PERSON><PERSON><PERSON>,
  u.nome as usuario,
  e.nome as empresa,
  e.cnpj,
  c2.descricao as nome_conv,
  CASE t2.tipo
    WHEN 0 THEN 'Nenhuma'
    WHEN 1 THEN 'Aprova Fácil - CobreBem'
    WHEN 2 THEN 'Vindi'
    WHEN 3 THEN 'Cielo'
    WHEN 5 THEN 'Cielo Débito Online'
    WHEN 6 THEN 'MaxiPago'
    WHEN 7 THEN 'Rede'
    WHEN 8 THEN 'Rede Débito Online'
    WHEN 9 THEN 'Fitness Card'
    WHEN 10 THEN 'Getnet'
    WHEN 11 THEN 'Stone'
    WHEN 12 THEN 'Mundipagg'
    WHEN 13 THEN 'Pagar.me'
    WHEN 14 THEN 'PactoPay'
    WHEN 15 THEN 'Stripe'
    WHEN 16 THEN 'PagoLivre'
    WHEN 17 THEN 'ValoriBank'
    WHEN 18 THEN 'OnePayment'
    ELSE 'OUTRO'
  END AS tipo_tran,
  CASE t2.origem
    WHEN 0 THEN 'Nenhuma'
    WHEN 1 THEN 'Caixa em Aberto'
    WHEN 2 THEN 'Retentativa - Manual'
    WHEN 3 THEN 'Automático'
    WHEN 4 THEN 'Vendas Online - Venda'
    WHEN 5 THEN 'Vendas Online - Link Pagamento'
    WHEN 6 THEN 'Pacto Store'
    WHEN 7 THEN 'WebService'
    WHEN 8 THEN 'Verificar cartão'
    WHEN 9 THEN 'Retentativa - PactoPay'
    WHEN 10 THEN 'Cobrar - PactoPay'
    WHEN 11 THEN 'Mailing'
    WHEN 12 THEN 'App do Aluno'
    WHEN 13 THEN 'Link Pagamento - Régua de Cobrança - Cobrança Antecipada'
    WHEN 14 THEN 'Link Pagamento - Régua de Cobrança - Cartão a vencer'
    WHEN 15 THEN 'Link Pagamento - Régua de Cobrança - Comunicação de Atraso'
    WHEN 16 THEN 'Vendas Online - Link Cadastrar Cartão'
    WHEN 17 THEN 'PactoPay'
    WHEN 18 THEN 'Totem'
    WHEN 19 THEN 'Link Pagamento - Régua de Cobrança - Cartão vencido'
    WHEN 20 THEN 'Realizar contato com o aluno - Botão Link para cadastrar cartão online'
    WHEN 21 THEN 'Link Pagamento - Régua de Cobrança - Cobrança Negada'
    WHEN 22 THEN 'Automático - Retentativa'
    WHEN 23 THEN 'Manual - Autorização de Cobrança'
    WHEN 24 THEN 'Renova Fácil Cielo'
    WHEN 25 THEN 'Renova Fácil FacilitePay'
    WHEN 26 THEN 'Adm - Venda Rápida'
    ELSE 'OUTRO'
  END AS origem_tran,
  c2.codigoautenticacao01,
  --t2.outrasinformacoes,
  (
    select
      count(ac.cliente)
    from
      acessocliente ac
    where
      ac.cliente = c.codigo
  ) as qtd_acessos
from
  cliente c
  inner join pessoa p on p.codigo = c.pessoa
  inner join autorizacaocobrancacliente aut on aut.cliente = c.codigo
  inner join movparcela mp on mp.pessoa = p.codigo
  inner join transacaomovparcela tmp on tmp.movparcela = mp.codigo
  inner join transacao t2 on t2.codigo = tmp.transacao
  inner join usuario u on u.codigo = t2.usuarioresponsavel
  inner join conveniocobranca c2 on c2.codigo = t2.conveniocobranca
  inner join empresa e on e.codigo = c.empresa
where
  aut.ativa
  and p.datacadastro >= CURRENT_DATE - INTERVAL '3 months'
  and t2.dataprocessamento >= CURRENT_DATE - INTERVAL '3 months'
order by
  t2.dataprocessamento