select e.nome as empresa_nome,
       e.codigo as empresa_codigo,
       e.cod_empresafinanceiro,
       e.cod_empresafinanceiro || '-' || cli.pessoa        as id_pessoa,
       a.dia::timestamp as dia_meta,
       a.colaboradorresponsavel,
       pes_col.nome as colaboradorresponsavel_nome,
       case f.identificadormeta
           when 'AG' then 'Agendamentos Presenciais'
           when 'LA' then 'Agendados de Amanhã'
           when 'HO' then 'Visitantes 24h'
           when 'RE' then 'Renovação'
           when 'PE' then 'Desistentes'
           when 'VA' then 'Visitantes Antigos'
           when 'EX' then 'Ex-Alunos'
           when 'IN' then 'Indicações'
           when 'CI' then 'Conversão de Indicados'
           when 'CV' then 'Conversão de Agendados'
           when 'CE' then 'Conversão de Ex-Alunos'
           when 'CA' then 'Conversão de Visitantes Antigos'
           when 'CD' then 'Conversão de Desistentes'
           when 'CT' then 'Conversão de Receptivo'
           when 'RI' then 'Grupo de Risco'
           when 'VE' then 'Vencidos'
           when 'PV' then 'Pós Venda'
           when 'FA' then 'Faltosos'
           when 'AN' then 'Aniversariantes'
           when 'SF' then 'Últimas Sessões'
           when 'SA' then 'Sessões sem agenda'
           when 'AL' then 'Agendamentos de Ligações'
           when 'IS' then 'Indicações sem Contato'
           when 'CP' then 'Receptivo'
           when 'GY' then 'Aluno GymPass'
           when 'CR' then 'Meta Extra'
           when 'LH' then 'Leads Hoje'
           when 'LC' then 'Leads Acumuladas'
           when 'VR' then 'Visitas recorrentes'
           when 'CL' then 'Conversão de Lead'
           end as meta,
       cli.matricula,
       pcli.nome as nome_cliente,
       fd.obtevesucesso,
       fd.repescagem,
       fd.contrato as contrato_gerado,
       pas.nome as passivo,
       ind.nomeindicado as indicado,
       case
           when h.resultado like 'Ag.Lig%' then 'Agendamento ligação'
           else h.resultado
           end as resultado,
       case h.tipocontato
           when 'AP' then 'Envio APP'
           when 'TE' then 'Ligação de Telefone'
           when 'CS' then 'Envio de SMS'
           when 'WA' then 'Envio de Whatsapp'
           when 'EM' then 'Email'
           when 'LC' then 'Ligação sem contato'
           when 'PE' then 'Contato pessoal'
           end as tipocontato,
       h.dia as hora_contato
from aberturameta a
         inner join fecharmeta f on f.aberturameta = a.codigo
         inner join fecharmetadetalhado fd on fd.fecharmeta = f.codigo
         inner join empresa e on e.codigo = a.empresa
         inner join colaborador c on c.codigo = a.colaboradorresponsavel
         inner join pessoa pes_col on pes_col.codigo = c.pessoa
         left join cliente cli on cli.codigo = fd.cliente
         left join pessoa pcli on pcli.codigo  = cli.pessoa
         left join passivo pas on pas.codigo = fd.passivo
         left join indicado ind on ind.codigo = fd.indicado
         left join historicocontato h on h.codigo = fd.historicocontato
-- order by a.dia desc, f.codigo desc
