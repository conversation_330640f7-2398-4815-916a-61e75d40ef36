WITH tipo_produto AS (
    SELECT 'MA' AS codigo, 'Matrícula' AS descricao
    UNION ALL SELECT 'RE', 'Remat<PERSON><PERSON><PERSON>'
    UNION ALL SELECT 'RN', 'Renovação'
    UNION ALL SELECT 'PE', 'Produto Estoque'
    UNION ALL SELECT 'PM', 'Mês de Referência Plano'
    UNION ALL SELECT 'SE', 'Serviço'
    UNION ALL SELECT 'CD', 'Convênio de Desconto'
    UNION ALL SELECT 'DE', 'Desconto'
    UNION ALL SELECT 'DV', 'Devolução'
    UNION ALL SELECT 'TR', 'Trancamento'
    UNION ALL SELECT 'RT', 'Retorno Trancamento'
    UNION ALL SELECT 'AA', 'Aula Avulsa'
    UNION ALL SELECT 'DI', 'Diária'
    UNION ALL SELECT 'FR', 'FreePass'
    UNION ALL SELECT 'AH', 'Alterar - Hor<PERSON>rio'
    UNION ALL SELECT 'MM', 'Manutenção Modalidade'
    UNION ALL SELECT 'MC', 'Manutenção Conta Corrente'
    UNION ALL SELECT 'DR', 'Desconto em Renovação Antecipada'
    UNION ALL SELECT 'TP', 'Taxa de Personal'
    UNION ALL SELECT 'SS', 'Sessão'
    UNION ALL SELECT 'DC', 'Devolução de crédito de conta corrente do cliente'
    UNION ALL SELECT 'AT', 'Atestado'
    UNION ALL SELECT 'TD', 'Taxa de Adesão Plano Recorrência'
    UNION ALL SELECT 'TN', 'Taxa de Renegociação'
    UNION ALL SELECT 'CP', 'Crédito de personal'
    UNION ALL SELECT 'TA', 'Taxa de Anuidade Plano Recorrência'
    UNION ALL SELECT 'RD', 'Devolução de recebíveis'
    UNION ALL SELECT 'CC', 'Depósito conta corrente do aluno'
    UNION ALL SELECT 'AC', 'Acerto conta corrente do aluno'
    UNION ALL SELECT 'QU', 'Quitação de dinheiro - Cancelamento'
    UNION ALL SELECT 'AR', 'Armário'
    UNION ALL SELECT 'MJ', 'Multa e Juros'
    UNION ALL SELECT 'CH', 'Cheques devolvidos'
    UNION ALL SELECT 'DS', 'Desafio'
    UNION ALL SELECT 'HM', 'App Home Fit'
    UNION ALL SELECT 'BT', 'Bio Totem'
    UNION ALL SELECT 'CN', 'Consulta Nutricional'
    UNION ALL SELECT 'OC', 'Ordem de compra'
)
SELECT
    datalancamento,
    p.tipoproduto,
    tp.descricao AS descricao_tipoproduto,
    m.descricao AS descricao_produto,
    e.nome AS nome_empresa
FROM
    movproduto m
INNER JOIN
    produto p ON p.codigo = m.produto
INNER JOIN
    tipo_produto tp ON tp.codigo = p.tipoproduto
inner join empresa e on e.codigo = m.empresa
WHERE
    p.tipoproduto IN ('PE', 'SE', 'TR', 'AA', 'DI', 'AR')
ORDER BY
    p.tipoproduto