-- USUARIOS ULTIMA CONFIGURAÇÃO ***** NOVO *****
select 
  u.nome,
  u.username ,
  u.ultimoacesso::timestamp ,
  e.cod_empresafinanceiro , 
  -- c.situacao as situacao_colaborador,
  te.descricao as tela_descricao,
  i.tipoinfo ,
   -- i.info,
  case when i.info = 'true' then 'NOVA' else 'ANTIGA' end as configurada,
  i.origem
   ,(   select case when coalesce(ix.padrao,false) then 'FIXA' else 'OPCIONAL' end as tela_nova
      from infomigracaohistoricoempresa ix 
      where ix.tipoinfo = i.tipoinfo and ix.dataregistro::date = CURRENT_DATE ) as rec_ep_padrao
  --
  from usuario u 
  left join infomigracao i  on i.usuario = u.codigo  
  left join tabelagenericaenum te on upper(te.nomeenum) = 'TIPOINFOMIGRACAOENUM' and te.codigo = i.tipoinfo::text
  left join colaborador c on c.codigo = u.colaborador
  left join empresa e on e.codigo = c.empresa 
where 1=1 -- i.tipoinfo = 4
   and c.situacao = 'AT'
 --  and u.nome like '%MAX%'
   ;