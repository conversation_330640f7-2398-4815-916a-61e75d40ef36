-- Mostra todos os casos em que no recibo tem parcelas de pessoas diferentes ( Pagador e Beneficiado )
select 
 CONCAT(sql.empresa_financeiro,'-',sql.pessoa_Ref) as <PERSON>d_<PERSON><PERSON><PERSON>,
 *
from 
( -- Beneficiado
	select 
	  r.pessoa<PERSON>gador as Pessoa_Ref,
	  'BENEFICIADO' as tipo_PgConjunto,
	  (m2.valorparcela * -1) as ValorConjunto,
	  r.codigo as recibo ,
	  mpg.codigo as Id_movpagamento,
	  mpg.datalancamento as dt_recebimento,
	  p2.movparcela as Id_movparcela,
	  --
	  c2.matricula  as pc_Matricula,
	  m2.pessoa as pc_Pessoa,
	  pe2.nome as pc_Nome,
	  e.cod_empresafinanceiro as empresa_financeiro
	from recibopagamento r 
	  inner join pagamentomovparcela p2  on p2.recibopagamento = r.codigo  
	  inner join movpagamento mpg on mpg.recibopagamento = r.codigo 
	  inner join movparcela m2 on m2.codigo = p2.movparcela and m2.pessoa <> r.pessoapagador
	  inner join pessoa pe2 on pe2.codigo = m2.pessoa 
	  inner join cliente c2 on c2.pessoa = m2.pessoa
	  inner join empresa e on e.codigo = mpg.empresa 
	--  
	union all 
	-- Pagador 
	select 
	  m2.pessoa as Pessoa_Ref ,
	  'PAGADOR' as tipo_PgConjunto,
	  m2.valorparcela as ValorConjunto,
	  r.codigo as recibo ,
	  mpg.codigo as Id_movpagamento,
	  mpg.datalancamento as dt_recebimento,
	  p2.movparcela as Id_movparcela,
	  --
	  c.matricula as pc_Matricula,
	  r.pessoapagador as pc_Pessoa ,
	  p.nome as pc_Nome,
	  e.cod_empresafinanceiro as empresa_financeiro
	from recibopagamento r 
	  inner join pagamentomovparcela p2  on p2.recibopagamento = r.codigo  
	  inner join movpagamento mpg on mpg.recibopagamento = r.codigo 
	  inner join movparcela m2 on m2.codigo = p2.movparcela and m2.pessoa <> r.pessoapagador
	  inner join pessoa p on p.codigo = mpg.pessoa 
	  inner join cliente c on c.pessoa  = p.codigo 
	  inner join empresa e on e.codigo = mpg.empresa 
	 -- 
	 ) as sql