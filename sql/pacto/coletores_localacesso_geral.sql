
WITH 
alunosativos AS (
    SELECT empresa, COUNT(*) as count
    FROM cliente
    WHERE situacao = 'AT'
    GROUP BY empresa
),
ultimoacesso AS (
    SELECT localacesso, MAX(dthrentrada) as dthrentrada
    FROM acessocliente
    GROUP BY localacesso
),
ultimos5dias AS (
    SELECT localacesso, COUNT(*) as qtde, MIN(dthrentrada) as data_i, MAX(dthrentrada) as data_f
    FROM acessocliente
    WHERE dthrentrada BETWEEN (now() - interval '5 day') AND now()
    GROUP BY localacesso
)
SELECT
    emp.nome as academia_nome,
    emp.codigo as academia_codigo,
    emp.cod_empresafinanceiro,
    la.codigo as codlocal,
    la.descricao as localacesso,
    la.nomecomputador,
    la.servidorimpressoes,
    col.codigo as coletor_codigo,
    col.descricao as coletor_descricao,
    col.modelo as coletor_modelo,
    la.databaseoffline,
    la.datadownloadbase,
    COALESCE(aa.count, 0) as alunos_ativos, 
    la.versaoacesso,
    ua.dthrentrada as data_ultimoacesso,
    u5.qtde as ultimos5dias_qtde,
    u5.data_i as ultimos5dias_data_i,
    u5.data_f as ultimos5dias_data_f,
    s2.nomecomputador as servidorfacial
FROM coletor col
INNER JOIN localacesso la ON col.localacesso = la.codigo
INNER JOIN empresa emp ON la.empresa = emp.codigo
LEFT JOIN servidorfacial s2 ON upper(s2.nomecomputador) = upper(la.nomecomputador)
LEFT JOIN alunosativos aa ON aa.empresa = emp.codigo
LEFT JOIN ultimoacesso ua ON ua.localacesso = la.codigo
LEFT JOIN ultimos5dias u5 ON u5.localacesso = la.codigo
WHERE emp.ativa
    AND (col.modelo <> 'MODELO_COLETOR_DESCONHECIDO' OR col.padraocadastro = 'f')
    AND col.desativado = 'f'
--    AND la.descricao NOT LIKE '%nome empresa%'
--    AND la.descricao NOT LIKE '%pacto%'
--    AND la.descricao NOT LIKE '%gqs%'
--    AND la.descricao NOT LIKE '%teste%'
--    AND la.descricao NOT LIKE '%desenv%'
--    AND la.descricao NOT LIKE '%integr%'
    AND la.versaoacesso <> ''
;

