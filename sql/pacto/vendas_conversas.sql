-- --------------- KPIS ----------------
select
		empresa.cod_empresafinanceiro,
        responsavel.username as usuario,
		pessoa.codigo as pessoa_codigo,
        pessoa.nome as pessoa_nome,
		movproduto.vendaavulsa,
		vendaavulsa.nomeComprador,
		vendaavulsa.tipocomprador,
		contrato.codigo as contrato_codigo,
        contrato.situacao as situacaoContrato,
		movproduto.situacao as situacaoMovProduto,
		contrato.plano as plano_codigo,
		contrato.origemsistema,
		pla.descricao as movimentacaoDescricao,
		empresa.nome as nomeEmpresa,
		produto.descricao as nomeProduto,
		case
			when tipoproduto in ('SE') then 1
			else movproduto.quantidade
		end as qtdProduto,
		movProduto.datalancamento::timestamp as movProduto_datalancamento,
		produto.tipoProduto as tipo<PERSON>rod<PERSON> ,
		responsavel.nome as nomeResponsavel,
		dp.aulaavulsadiaria,
		dp.personal,
		dp.modalidadeDiaria,
		sum (movProduto.valorFaturado) as total,
		contratoDuracao.numeromeses as duracao_contrato
from
		movProduto
left join produto on
		movProduto.produto = produto.codigo
left join pessoa on
		movProduto.pessoa = pessoa.codigo
left join vendaavulsa on
		movProduto.vendaavulsa = vendaavulsa.codigo
left join empresa on
		empresa.codigo = movproduto.empresa
left join contrato on
		movProduto.contrato = contrato.codigo
left join contratoDuracao on
		movProduto.contrato = contratoDuracao.contrato
left join plano pla on
		pla.codigo = contrato.plano
left join (
	select
			distinct par.aulaavulsadiaria,
			par.personal,
			mod.nome as modalidadeDiaria,
			mpp.movproduto as codigoproduto
	from
			movparcela par
	left join movprodutoparcela mpp on
			par.codigo = mpp.movparcela
	left join aulaavulsadiaria diaria on
			diaria.codigo = par.aulaavulsadiaria
	left join modalidade mod on
			mod.codigo = diaria.modalidade
	where
			par.aulaavulsadiaria is not null
		or par.personal is not null ) as dp on
		dp.codigoproduto = movProduto.codigo
inner join usuario responsavel on
		responsavel.codigo = movproduto.responsavellancamento
inner join configuracaocrmia crmia on 
        crmia.pactoconversaslogin = responsavel.username
        and crmia.codigoempresa = empresa.codigo
where
		movproduto.descricao not like 'PLANO TRANSFERIDO -%'
	and movProduto.movpagamentocc is null
	-- and movProduto.totalFinal > 0
	--	and movProduto.datalancamento >= '2025-03-01 00:00:00.000'
	--	and movProduto.datalancamento <= '2025-03-31 23:59:59.999'
	and produto.tipoproduto not in ('CC', 'DC', 'DV', 'DE')
    -- and responsavel.username = 'conversasai'
group by
		empresa.cod_empresafinanceiro,
        responsavel.username,
		pessoa.codigo,
        pessoa.nome,
		movProduto_datalancamento,
		contrato.codigo,
		contrato.situacao,
		movproduto.situacao,
		empresa.nome,
		nomeProduto,
		qtdProduto,
		movproduto.vendaavulsa,
		pla.descricao,
		tipoProduto,
		nomeResponsavel,
		vendaavulsa.nomeComprador,
		vendaavulsa.tipocomprador,
		dp.aulaavulsadiaria,
		dp.personal,
		dp.modalidadeDiaria,
		contratoDuracao.numeromeses
order by
		movProduto_datalancamento