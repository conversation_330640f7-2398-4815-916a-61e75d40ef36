-- Este aqui o SQL usado na Pipe line
select
    e.cod_empresafinanceiro AS Unidade_IdPacto,
    e.nome AS Unidade,
    c.codigo AS id_contrato,
    c.situacaocontrato as contrato_tipo,  -- Este para  ( MA e RM)
    c.situacao as contrato_situacao, -- este para analizar os cancelamentos realizados no periodo
    cli.codigocliente id_cliente,
    cli.situacao as situacao_atual_cliente,  -- Situação atual do cliente
    cli.matricula,
    cli.nomecliente,
    cli.duracaocontratomeses as contrato_ducacao_atual,
    c.datalancamento ,
    c.datamatricula,
    c.datarenovarre<PERSON>a,
    c.dataprevistarenovar,
    c.renovavelautomaticamente,
   	c.vigenciade,
	c.vigenciaate ,
	c.vigenciaateajustada ,
	c.datarematricularealizada,
	c.valorb<PERSON>lo ,
	c.valorfinal
FROM
    contrato c
    INNER JOIN SituacaoClienteSinteticoDW cli ON cli.codigopessoa = c.pessoa
    INNER JOIN empresa e ON e.codigo = c.empresa
WHERE
    c.datamatricula >= '2023-01-01'