SELECT
    p.codigo codigo_plano,
    p.descricao descricao_plano,
    e.nome nome_empresa,
    c.data<PERSON>,
    p.plan<PERSON>,
    p.plan<PERSON>,
    c.<PERSON><PERSON><PERSON><PERSON>,
    c.venda<PERSON><PERSON><PERSON><PERSON>,
    c.contra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    c.contra<PERSON><PERSON><PERSON><PERSON><PERSON>,
    c.situa<PERSON>,
    c.v<PERSON>,
    c.orige<PERSON>,
    CASE c.origemsistema
        WHEN 1 THEN 'ZillyonWeb'
        WHEN 2 THEN 'Agenda Web'
        WHEN 3 THEN 'Pacto Treino'
        WHEN 4 THEN 'App Treino'
        WHEN 5 THEN 'App Professor'
        WHEN 6 THEN 'Autoatendimento'
        WHEN 7 THEN 'Site Vendas'
        WHEN 8 THEN 'Buzz Lead'
        WHEN 9 THEN 'Vendas 2.0'
        WHEN 10 THEN 'App do consultor'
        WHEN 11 THEN 'Booking Gympass'
        WHEN 12 THEN 'Fila de espera'
        WHEN 13 THEN 'Importação API'
        WHEN 14 THEN 'Hubspot Lead'
        WHEN 15 THEN 'CRM Meta Diaria'
        WHEN 16 THEN 'Pacto Flow'
        WHEN 17 THEN 'Nova Tela de Negociação'
        ELSE 'Unknown'
    END AS origem_descricao
FROM
    contrato c
    JOIN plano p ON c.plano = p.codigo
    JOIN empresa e ON c.empresa = e.codigo
WHERE
    c.datalancamento IS NOT NULL;
