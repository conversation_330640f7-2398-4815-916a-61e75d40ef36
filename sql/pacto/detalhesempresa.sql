SELECT
  "public"."empresafinanceiro"."codigofinanceiro" AS "codigofinanceiro",
  "public"."empresafinanceiro"."chavezw" AS "chavezw",
  "public"."empresafinanceiro"."cnpj" AS "cnpj",
  "public"."empresafinanceiro"."nomeresumo" AS "nomeresumo",
  "public"."empresafinanceiro"."redeempresa" AS "redeempresa",
  "responsavelpacto_codigo"."nome" AS "responsavelpacto_codigo__nome"
FROM
  "public"."empresafinanceiro"
 
LEFT JOIN "public"."detalheempresa" AS "detalheempresa_codigo" ON "public"."empresafinanceiro"."detalheempresa_codigo" = "detalheempresa_codigo"."codigo"
  LEFT JOIN "public"."customersuccess" AS "responsavelpacto_codigo" ON "detalheempresa_codigo"."responsavelpacto_codigo" = "responsavelpacto_codigo"."codigo"
WHERE
  ("responsavelpacto_codigo"."codigo" IS NOT NULL)
 
   AND (
    "public"."empresafinanceiro"."grupofavorecido" = 'G1-CLIENTE ATIVO'
  )