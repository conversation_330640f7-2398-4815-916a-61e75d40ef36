WITH ultimo_gympass AS (
    SELECT 
        pa.pessoa, 
        max(pa.datalancamento) AS data_ultimo_Gympass
    FROM periodoacessocliente pa
    WHERE pa.tokengympass > ' '
    GROUP BY pa.pessoa
),
-- Para descobrir se este cliente é titular e de quantas pessoas -------------------------------------------------
titular_vinculos AS (
    SELECT titularplanocompartilhado AS codigoSeTitular, count(*) AS QtdeVinculos
    FROM cliente
    WHERE titularplanocompartilhado > 0
    GROUP BY titularplanocompartilhado
),
ultimo_questionario AS (
    SELECT 
        qc1.cliente, 
        qc1.evento, 
        qc1.origemsistema, 
        qc1.data
    FROM questionariocliente qc1
    JOIN ( SELECT cliente, MAX(qci1.codigo) AS max_codigo 
	        FROM questionariocliente qci1
	        JOIN questionario qi1 ON qci1.questionario = qi1.codigo
	        WHERE qi1.tipoquestionario = 'PL'
	        GROUP BY cliente ) qc2 
	    ON qc1.cliente = qc2.cliente AND qc1.codigo = qc2.max_codigo
)
SELECT 
    e.nome AS Unidade,
    e.cod_empresafinanceiro AS Unidade_IdPacto,
    e.codigorede AS Unidade_IdRede,
    uf.sigla AS Unidade_uf,
    cid.nome AS Unidade_cidade,
    s.codigo,
    s.dia,
    s.codigocliente,
    s.matricula,
    s.nomecliente,
    s.datanascimento,
    s.idade,
    replace(s.profissao, 'X', '') AS profissao,
    s.colaboradores,
    s.codigocontrato,
    s.mnemonicocontrato,
    s.saldocontacorrentecliente,
    s.datavigenciade,
    s.datavigenciaate,
    s.datavigenciaateajustada,
    s.datalancamentocontrato,
    s.datarenovacaocontrato,
    s.datarematriculacontrato,
    s.dataultimobv,
    s.datamatricula,
    s.dataultimarematricula,
    s.diasassiduidadeultrematriculaatehoje,
    s.diasacessosemanapassada,
    s.dataultimoacesso,
    s.faseatualcrm,
    s.dataultimocontatocrm,
    s.responsavelultimocontatocrm,
    s.codigoultimocontatocrm,
    s.tipoperiodoacesso,
    s.datainicioperiodoacesso,
    s.datafimperiodoacesso,
    s.diasacessosemana2,
    s.diasacessosemana3,
    s.diasacessosemana4,
    s.vezesporsemana,
    s.diasacessoultimomes,
    s.diasacessomes2,
    s.diasacessomes3,
    s.diasacessomes4,
    s.mediadiasacesso4meses,
    s.telcelcolab,
    s.pesorisco,
    s.enviosmsmarcadoclassif,
    s.smsrisco,
    e.cod_empresafinanceiro || '-' || s.codigopessoa AS id_pessoa,
    s.codigousuariomovel,
    s.empresacliente,
    s.sexocliente,
    s.telefonescliente,
    s.nomeconsulta,
    s.cpf,
    s.codacessocliente,
    s.modalidades,
    s.frequenciasemanal,
    s.saldocreditotreino,
    s.validarsaldocreditotreino,
    s.quantidadediasextra,
    s.nraulaexperimental,
    s.totalcreditotreino,
    s.descricoesmodalidades,
    s.crossfit,
    s.datasaidaacesso,
    s.statusbg,
    s.datacadastro,
    s.existeparcvencidacontrato,
    s.empresausafreepass,
    s.ultimavisita,
    replace(s.cargo, 'X', '') AS cargo,
    s.freepass,
    replace(s.endereco, '"', '') AS endereco,
    s.cidade,
    s.bairro,
    CASE
        WHEN s.estadocivil = 'C' THEN 'Casado(a)'
        WHEN s.estadocivil = 'A' THEN 'Amasiado(a)'
        WHEN s.estadocivil = 'D' THEN 'Divorciado(a)'
        WHEN s.estadocivil = 'S' THEN 'Solteiro(a)'
        WHEN s.estadocivil = 'U' THEN 'União Estavel'
        WHEN s.estadocivil = 'P' THEN 'Separado(a)'
        WHEN s.estadocivil = 'V' THEN 'Viuvo(a)'
        ELSE '?'
    END AS estadocivil,
    s.rg,
    s.uf,
    s.telefonesconsulta,
    s.cpfconsulta,
    cli.titularplanocompartilhado,
    e.cod_empresafinanceiro || '-' || qc.evento AS id_evento,
    coalesce(ev.descricao, 'SEM EVENTO') AS descricao_evento,
    CASE qc.origemsistema
        WHEN 1 THEN 'ZillyonWeb'
        WHEN 2 THEN 'Agenda Web'
        WHEN 3 THEN 'Pacto Treino'
        WHEN 4 THEN 'App Treino'
        WHEN 5 THEN 'App Professor'
        WHEN 6 THEN 'Autoatendimento'
        WHEN 7 THEN 'Site Vendas'
        WHEN 8 THEN 'Buzz Lead'
        WHEN 9 THEN 'Vendas 2.0'
        WHEN 10 THEN 'App do consultor'
        WHEN 11 THEN 'Booking Gympass'
        WHEN 12 THEN 'Fila de espera'
        WHEN 13 THEN 'Importação API'
        ELSE 'BV não encontrado'
    END AS origemsistema,
    qc.data AS data_bv_matricula,
    con.bolsa AS contrato_bolsa,
    CASE
        WHEN cli.titularplanocompartilhado IS NULL THEN s.situacao
        ELSE s2.situacao
    END AS situacao,
    CASE
        WHEN cli.titularplanocompartilhado IS NULL THEN s.situacaocontrato
        ELSE s2.situacaocontrato
    END AS situacaocontrato,
    CASE
        WHEN cli.titularplanocompartilhado IS NULL THEN s.situacaomatriculacontrato
        ELSE s2.situacaomatriculacontrato
    END AS situacaomatriculacontrato,
    CASE
        WHEN cli.titularplanocompartilhado IS NULL THEN s.situacaocontratooperacao
        ELSE s2.situacaocontratooperacao
    END AS situacaocontratooperacao,
    CASE
        WHEN cli.titularplanocompartilhado IS NULL THEN s.nomeplano
        ELSE s2.nomeplano
    END AS nomeplano,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN (s.valorfaturadocontrato / (s3.QtdeVinculos + 1))
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN (s2.valorfaturadocontrato / (s3.QtdeVinculos + 1))
        ELSE s.valorfaturadocontrato
    END AS valorfaturadocontrato,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN (s.valorpagocontrato / (s3.QtdeVinculos + 1))
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN (s2.valorpagocontrato / (s3.QtdeVinculos + 1))
        ELSE s.valorpagocontrato
    END AS valorpagocontrato,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN (s.valorparcabertocontrato / (s3.QtdeVinculos + 1))
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN (s2.valorparcabertocontrato / (s3.QtdeVinculos + 1))
        ELSE s.valorparcabertocontrato
    END AS valorparcabertocontrato,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN s.duracaocontratomeses
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN s2.duracaocontratomeses
        ELSE s.duracaocontratomeses
    END AS duracaocontratomeses,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN 'Titular'
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN 'Dependente'
        ELSE 'Sem Vinculo'
    END AS Vinculo,
    CASE
        WHEN ((cli.titularplanocompartilhado IS NULL) AND (s3.codigoSeTitular IS NOT NULL)) THEN s3.codigoSeTitular
        WHEN cli.titularplanocompartilhado IS NOT NULL THEN cli.titularplanocompartilhado
        ELSE NULL
    END AS CodigoVinculado,
    s.valorfaturadocontrato AS valorfaturadocontrato_titular,
    s3.codigoSeTitular,
    s3.QtdeVinculos,
    p2.descricao AS produto_plano,
    (SELECT array_to_string(array_agg(e.email), ',')  FROM email e WHERE e.pessoa = coalesce(s.codigopessoa,0) ) AS emails,
    cat.codigo as codigocategoria,
    cat.nome as nomecategoria,
    ug.data_ultimo_Gympass
FROM situacaoclientesinteticodw s
INNER JOIN empresa e ON e.codigo = s.empresacliente
LEFT JOIN estado uf ON uf.codigo = e.estado
LEFT JOIN cidade cid ON cid.codigo = e.cidade
INNER JOIN cliente cli ON s.codigocliente = cli.codigo
left join categoria cat on cat.codigo = cli.categoria
LEFT JOIN ultimo_questionario qc ON qc.cliente = cli.codigo
LEFT JOIN evento ev ON qc.evento = ev.codigo
 -- Para pegar os dados do titular de um dependentes --------------------------------------------------------------
left join situacaoclientesinteticodw s2 on (cli.titularplanocompartilhado is not null and
                                            cli.titularplanocompartilhado = s2.codigocliente)
LEFT JOIN titular_vinculos s3 ON s3.codigoSeTitular = s.codigocliente
LEFT JOIN contrato con ON con.codigo = coalesce(s2.codigocontrato, s.codigocontrato)
LEFT JOIN plano p ON p.codigo = con.plano
LEFT JOIN produto p2 ON p2.codigo = p.produtopadraogerarparcelascontrato
LEFT JOIN ultimo_gympass ug ON ug.pessoa = cli.pessoa
WHERE e.cod_empresafinanceiro IS NOT NULL AND e.cod_empresafinanceiro <> 0
--and  s.matricula in ('000017','000050','000034')
