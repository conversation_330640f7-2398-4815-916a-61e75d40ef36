select
    hcup.chavePortadorCupom as chave,
    (select e.nomefantasia from empresafinanceiro e where e.chaveZW = hcup.chavePortadorCupom and e.empresazw = hcup.empresaportadorcupom limit 1) as empresa,
    hcup.nomeportadorcupom,
    hcup.datapremioportadorcupom,
    hcup.contrato,
    hcup.contratoestornado,
    hcup.numerocupom,
    hcup.cupomnomefixo,
    camp.id as campanha_id,
    camp.descricaocampanha
    from HistoricoUtilizacaoCupomDesconto hcup
inner join campanhaCupomDesconto camp on camp.id = hcup.campanhaCupomDesconto_id
    where 1 = 1
    and ((hcup.dataPremioPortadorCupom is not null) or (hcup.dataPremioAluno is not null))