select e.nome                                            as unidade,
       e.cod_empresafinanceiro                           as id_favorecido,
       e.codigorede,
       case
           when col.codigo is not null then 'COLABORADOR'
           when cli.codigo is not null then 'CLIENTE'
           else 'CONSUMIDOR'
           end                                           as tipo,
       e.cod_empresafinanceiro || '-' || p.codigo        as id_pessoa,
       case
           when con.codigo is not null then e.cod_empresafinanceiro || '-C-' || con.codigo
           when va.codigo is not null then e.cod_empresafinanceiro || '-VA-' || va.codigo
           else 'ANALISAR'
           end                                           as id_venda,
       case
           when con.codigo is not null then 'CONTRATO'
           when va.codigo is not null then 'VENDA_AVULSA'
           else 'OUTROS'
           end                                           as tipoVenda,        
       case
           when va.codigo is not null then va.nomecomprador
           else p.nome
           end                                           as nome,
       case
           when con.codigo is not null then e.cod_empresafinanceiro || '-' || con.codigo
           else NULL
           end                                           as id_contrato,
       coalesce(pl.descricao, '')                        as nomeplano,
       e.cod_empresafinanceiro || '-' || prod.codigo     as id_produto,
       prod.descricao                                    as descricao_produto,
       e.cod_empresafinanceiro || '-' || mprod.codigo    as id_movproduto,
       mprod.descricao                                   as item,
       mprod.quantidade,
       mprod.totalfinal::numeric                         as valor_produto,
       mprod.valorfaturado::numeric                      as valor_faturado,
       mprod.datalancamento                              as data_da_venda,
       mprod.situacao                                    as situacao_movproduto,
       to_date(mprod.mesreferencia, 'MM/YYYY')           as mes_referencia,
       coalesce(fp.descricao, 'EM ABERTO')               as formapagamento,
       super_movpagamento.valor                          as valor_forma_pagamento,
       u.nome                                            as responsavel_lancamento,
       fp_prev.descricao                                 as formapagamento_prevista,
       now()                                             as dataconsulta,
       col_vinculo.codigo || '-' || pes_col_vinculo.nome as consultor_atual,
       con.valorfinal                                    as valor_final_contrato,
       con.bolsa                                         as contrato_bolsa,
       mprod.situacao                                    as situacao_produto_venda
from movproduto mprod
         left join contrato con on mprod.contrato = con.codigo
         left join plano pl on con.plano = pl.codigo
         left join vendaavulsa va on mprod.vendaavulsa = va.codigo
         inner join empresa e on mprod.empresa = e.codigo
         inner join produto prod on mprod.produto = prod.codigo
         left join pessoa p on mprod.pessoa = p.codigo
         inner join usuario u on mprod.responsavellancamento = u.codigo
         left join cliente cli on p.codigo = cli.pessoa
         left join vinculo vi ON cli.codigo = vi.cliente AND vi.tipovinculo = 'CO'
         left join colaborador col_vinculo on vi.colaborador = col_vinculo.codigo
         left join pessoa pes_col_vinculo ON col_vinculo.pessoa = pes_col_vinculo.codigo
         left join colaborador col on p.codigo = col.pessoa
         left join (
    SELECT codigo,
           formapagamento,
           split_part( unnest(string_to_array(right(produtospagos, -1), '|')), ',', 1)::int as movproduto,
           split_part( unnest(string_to_array(right(produtospagos, -1), '|')), ',', 2) as tipoproduto,
           split_part( unnest(string_to_array(right(produtospagos, -1), '|')), ',', 3)::int as contrato,
           split_part( unnest(string_to_array(right(produtospagos, -1), '|')), ',', 4)::numeric as valor
    FROM movpagamento
    where produtospagos like '|%'
) as super_movpagamento on super_movpagamento.movproduto = mprod.codigo
         left join formapagamento fp on super_movpagamento.formapagamento = fp.codigo
         left join formapagamento fp_prev on mprod.formapagamento = fp_prev.codigo
WHERE prod.tipoproduto not in ('DE', 'DR', 'MC', 'CC');
