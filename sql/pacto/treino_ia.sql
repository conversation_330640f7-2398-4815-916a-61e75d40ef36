WITH log_ia AS (
  SELECT 
    l.*, 
    CASE 
      WHEN l.valorcampoalterado ILIKE '%PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA: True%' THEN 'ativo'
      WHEN l.valorcampoalterado ILIKE '%PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA: False%' THEN 'inativo'
      ELSE 'outro'
    END AS status_ia
  FROM log l
  WHERE l.valorcampoalterado ILIKE '%PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA%'
)
SELECT * 
FROM log_ia
WHERE status_ia IN ('ativo', 'inativo')
ORDER BY codigo DESC