select e.cod_empres<PERSON><PERSON><PERSON><PERSON> as empresa_financeiro, 
	e.nome as empresa,
	c.nome as nome_aluno,
	c.codigo<PERSON><PERSON>,
	c.co<PERSON><PERSON><PERSON>,
	c.data<PERSON><PERSON><PERSON> as data<PERSON><PERSON>tro ,
	u.dataregistrousoapp as ultima_abertura_app,
	u.versaodoapp,
	u.apputilizado 
from clientesintetico c 
	inner join empresa e on c.empresa = e.codzw
	inner join usuario u on u.cliente_codigo = c.codigo
where u.dataregistrousoapp is not null