select
    i.empresa,
    e.cod_empresafinanceiro as empresa_financeiro,
    i.dataregistro::date as data_registro,
    to_char(i.dataregistro, 'YYYY-MM-DD') as data_registro_str,
    i.dataregistro::date::timestamp AS data_registro_tms,
    i.tipoinfo as recurso_id,
    te.descricao as recurso_descricao,
    i.tiposinfomigracaopadrao,
    case when coalesce(i.padrao,false) then 'SIM' else 'NAO' end as padraostr,
    coalesce(i.padrao,false) as padrao
from infomigracaohistoricoempresa i
         inner join empresa e on e.codigo = i.empresa
         left join tabelagenericaenum te on upper(te.nomeenum) = 'TIPOINFOMIGRACAOENUM' and te.codigo = i.tipoinfo::text
where i.tipoinfo not in (2,3)
  and e.ativa = true
  and e.cod_empresafinanceiro > 0