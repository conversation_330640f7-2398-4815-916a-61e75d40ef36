
--select 
----sql.situacao_colaborador,
----sql.perfis_acesso,
----sql.tiposColaborador,
----sql.umovel_status,
--  count(*) as Qtde,
--  count(distinct id_pessoa) Colaboradores_uniq,
--  count(usuario_codigo) as usuarios,
--  count(distinct usuario_codigo) as usuarios_uniq,
--  count(umovel_codigo) as usuarios_movel_qtde,
--  count(usuario_geral) as usuario_geral_qtde
-- from 
--  (
select
	col.codigo as pk_colaborador,
	e.cod_empresafinanceiro as id_Pacto,
	e.cod_empresafinanceiro || '-' || col.pessoa    as id_pessoa,
	e.codigorede as id_rede,
	e.codigo as empresa,
	uf.sigla as uf,
	cid.nome as cidade,
	e.nome as nome_empresa,
	pes.nome as nome_colaborador,
	dp.nome as departamento,
	array_to_string(
		array(
			select p.nome
			from usuario usu
				inner join usuarioperfilacesso upa on usu.codigo = upa.usuario
				inner join perfilacesso p on upa.perfilacesso = p.codigo
			where usu.colaborador = col.codigo
		), ',', ''
	) as perfis_acesso,
	p.nome as perfil_acesso,
	prof.descricao as profissao,
	pes.datanasc as data_nascimento,
	coalesce(nullif(pes.contatoemergencia, ''), '#') as contato_emergencia,
	coalesce(nullif(pes.telefoneemergencia, ''), '#') as telefone_emergencia,
	pes.cfp as cpf,
	coalesce(nullif(pes.rg, ''), '#') as rg,
	coalesce(nullif(col.cref, ''), '#') as cref,
	col.cargahoraria as carga_horaria,
	array_to_string(
		array(
			select tc.descricao
			from tipocolaborador tc
			where tc.colaborador = col.codigo
		), ',', ''
	) as tiposColaborador,
	array_to_string(
		array(
			select email
			from email em
			where em.pessoa = col.pessoa
		), ',', ''
	) as emails,
	array_to_string(
		array(
			select tel.numero
			from telefone tel
			where tel.pessoa = col.pessoa
		), ',', ''
	) as telefones,
	coalesce(inforh.tamanhoUniformeCamisa, '#') as tamanho_camisa,
	coalesce(inforh.tamanhoUniformeCalca, 0) as tamanho_calca,
	inforh.valorsalario,
	coalesce(cast(inforh.observacao as varchar(144)), '#') as infrh_obs,
	pes.sexo,
	pes.genero,
	pes.datacadastro as data_cadastro,
	um.ativo as umovel_status,
	um.nome as umovel_nome,
	um.codigo as umovel_codigo,
	col.situacao as situacao_colaborador,
	p.nome as perfil_usuario,
	case
		when p.tipo = 0 then 'TODOS'
		when p.tipo = 1 then 'ADMINISTRADOR'
		when p.tipo = 2 then 'CONSULTOR'
		when p.tipo = 3 then 'GERENTE'
		when p.tipo = 4 then 'PROFESSOR'
		when p.tipo is null then NULL
		else 'Outros'
	end perfil_tipo,
	u.username as usuario_nome,
	u.codigo as usuario_codigo, 
	u.usuariogeral as usuario_geral,
	coalesce(u.dataultimaalteracaosenha, u.dataalteracaosenha) as data_senha,
	ut.numero verificado_telefone,
	ue.email as verificado_email,
    up.codigo as pk_usuarioperfilacesso,
	ut.codigo as pk_usuarioTelefone,
	ue.codigo as pk_usuarioEmail,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 1 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_negociacao,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 4 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_tela_aluno,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 5 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_venda_avulsa,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 6 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_configuracoes,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 7 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_caixa_em_aberto,
    case when exists(select codigo from infomigracao where upper(info) = 'TRUE' and tipoinfo = 8 and usuario = u.codigo) then 'SIM' else 'NAO' end as nova_incluir_cliente,
    (select max(datalancamento) from contrato where responsavelcontrato = u.codigo) as dt_ultimo_contrato,
    (select max(dataregistro) from vendaavulsa where responsavel = u.codigo) as dt_ultima_venda_avulsa,
    case when u.ultimoacesso is not null then u.ultimoacesso else (select max(dataregistro) from logcontroleusabilidade l where entidade = 'LOGADO' and usuario = u.codigo) end as dt_ultimo_acesso
from colaborador col
	inner join pessoa pes on col.pessoa = pes.codigo
	inner join empresa e on col.empresa = e.codigo
	left join colaboradorInfoRH inforh on col.codigo = inforh.colaborador
	left join profissao prof on pes.profissao = prof.codigo
	left join departamento dp on col.departamento = dp.codigo
	left join usuariomovel um on um.colaborador = col.codigo
	left join usuario u on u.colaborador = col.codigo
	left join usuarioperfilacesso up on up.usuario = u.codigo
	left join perfilacesso p on p.codigo = up.perfilacesso
	left join estado uf on uf.codigo = e.estado
	left join cidade cid on cid.codigo = e.cidade
	left join usuarioTelefone ut on ut.usuario = u.codigo and ut.verificado
	left join usuarioEmail ue on ue.usuario = u.codigo and ue.verificado
--) sql
-- where sql.situacao_colaborador = 'AT'
----group by 1


--
--	CASE
--        WHEN tp.descricao = 'PR' THEN 'Professor'
--        WHEN tp.descricao = 'TW' THEN 'Professor (TreinoWeb)'
--        WHEN tp.descricao = 'PT' THEN 'Personal Trainer'
--        WHEN tp.descricao = 'OR' THEN 'Orientador'
--        WHEN tp.descricao = 'CO' THEN 'Consultor'
--        WHEN tp.descricao = 'PI' THEN 'Personal Interno'
--        WHEN tp.descricao = 'PE' THEN 'Personal Externo'
--        WHEN tp.descricao = 'TE' THEN 'Terceirizado'
--        WHEN tp.descricao = 'ES' THEN 'Estúdio'
--        WHEN tp.descricao = 'FO' THEN 'Fornecedor'
--        WHEN tp.descricao = 'CR' THEN 'Coordenador'
--        WHEN tp.descricao = 'MD' THEN 'Médico'
--        WHEN tp.descricao = 'FC' THEN 'Funcionário'
--        WHEN tp.descricao = 'AD' THEN 'Administrador'
--        ELSE 'Outro'
--    END AS tipo_colaborador,
-- limit 3
	
	