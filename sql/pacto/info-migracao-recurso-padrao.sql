select
    to_char(sql.data_registro, 'YYYY-MM-DD')            as data_registro,
    sql.recursoid as recurso_id,
    sql.recurso as recurso_descricao,
    sum(case when sql.recurso_ativo then 1 else 0 end) as qtd_usuario_ativado,
    sum(case when sql.recurso_ativo then 0 else 1 end) as qtd_usuario_desativado
from (
         select
             distinct
             i.dataregistro::date as data_registro,
             i.usuario,
             i.tipoinfo as recursoid,
             te.descricao             as recurso,
             i.info,
             (upper(i.info) = 'TRUE') as recurso_ativo
         from infomigracaohistorico i
                  left join tabelagenericaenum te on upper(te.nomeenum) = 'TIPOINFOMIGRACAOENUM' and te.codigo = i.tipoinfo::text
         inner join usuario u on u.codigo = i.usuario
         where i.tipoinfo not in (2,3)
           and upper (u.username) not in ('ADMIN','PACTOBR','RECOR')
           and i.usuario_ativO
     ) as sql
group by 1,2,3
--order by 1 desc