select e.cod_empresaf<PERSON><PERSON><PERSON> as empresa_financeiro, 
	e.nome as empresa,
	n.nome as contato,
	count(n.codigo) as enviadas,
	SUM(CASE WHEN lida = TRUE THEN 1 ELSE 0 END) AS lidas
from clientesintetico c
	inner join empresa e on c.empresa = e.codzw
	inner join notificacao n on n.cliente_codigo = c.codigo 
where n.tipo = 18
and n.dataregistro >= CURRENT_DATE - interval '2 months'
group by 1,2,3