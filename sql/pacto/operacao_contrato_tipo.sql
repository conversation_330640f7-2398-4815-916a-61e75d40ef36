
SELECT c.dataoperacao::timestamp,
       CASE
           WHEN c.tipoOperacao = 'RM' THEN 'Rematrícula'
           WHEN c.tipoOperacao = 'TS' THEN 'Transferência Saída'
           WHEN c.tipoOperacao = 'MA' THEN 'Matrícula'
           WHEN c.tipoOperacao = 'AD' THEN 'Alteração Duração'
           WHEN c.tipoOperacao = 'RE' THEN 'Renovação'
           WHEN c.tipoOperacao = 'BA' THEN 'Bônus - Acréscimo de dias'
           WHEN c.tipoOperacao = 'BR' THEN 'Bônus - Redução de dias'
           WHEN c.tipoOperacao = 'CR' THEN 'Férias'
           WHEN c.tipoOperacao = 'TE' THEN 'Transferência Entrada'
           WHEN c.tipoOperacao = 'CA' THEN 'Cancelamento'
           WHEN c.tipoOperacao = 'AH' THEN 'Alteração Horário'
           WHEN c.tipoOperacao = 'TR' THEN 'Trancamento'
           WHEN c.tipoOperacao = 'TV' THEN 'Trancamento Vencido'
           WHEN c.tipoOperacao = 'RT' THEN 'Retorno Trancamento'
           WHEN c.tipoOperacao = 'IM' THEN 'Incluir Modalidade'
           WHEN c.tipoOperacao = 'EM' THEN 'Excluir Modalidade'
           WHEN c.tipoOperacao = 'AM' THEN 'Alterar Modalidade'
           WHEN c.tipoOperacao = 'AC' THEN 'Alteração Contrato'
           WHEN c.tipoOperacao = 'AT' THEN 'Atestado'
           WHEN c.tipoOperacao = 'RA' THEN 'Retorno - Atestado'
           WHEN c.tipoOperacao = 'LV' THEN 'Liberação de Vaga'
           WHEN c.tipoOperacao = 'BC' THEN 'Afastamento Coletivo'
           WHEN c.tipoOperacao = 'TD' THEN 'Transferência dos Direitos de uso'
           WHEN c.tipoOperacao = 'RD' THEN 'Retorno dos Direitos de uso'
           ELSE c.tipoOperacao
       END AS tipo,
        e.nome as nome_empresa,
        e.codigo as codigo_empresa,
        u.nome nome_usuario
FROM contratooperacao c
inner join contrato ct on c.contrato = ct.codigo
inner join empresa e on ct.empresa = e.codigo
inner join usuario u on c.responsavel = u.codigo;