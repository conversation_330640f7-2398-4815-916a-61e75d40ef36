select
	e.identificadoremp<PERSON>a,
	e.chave,
	e.infoinfra,
	ni.des<PERSON>,
	ef.ativazw,
	ef.nomeempresazw,
	ef.chavezw,
	ef.empresazw,
	ef.no<PERSON><PERSON><PERSON>,
	ef.estado,
	ef.codigo<PERSON><PERSON><PERSON>,
	ef.cnpj,
	re.chaverede,
	re.nome,
	d.codigo as detalhe_codigo
FROM
	empresa e
	left join empresafinanceiro ef on e.chave = ef.chavezw
	LEFT JOIN redeempresa re ON ef.redeempresa_id = re.id
	LEFT JOIN detalheempresa d ON ef.detalheempresa_codigo = d.codigo
	left join nomeinfra ni on ni.infoinfra = e.infoinfra
where
	ativazw is true
