
SELECT DATE(dataassinaturacontrato) AS dia,
       COUNT(*) AS quantidade_contratos,
       'email' AS tipo
FROM contrato
WHERE dataassinaturacontrato IS NOT NULL
GROUP BY DATE(dataassinaturacontrato)

UNION ALL

SELECT DATE(lancamento) AS dia,
       COUNT(*) AS quantidade_contratos,
       'tablet' AS tipo
FROM contratoassinaturadigital
WHERE lancamento IS NOT NULL
GROUP BY DATE(lancamento)

UNION ALL

SELECT DATE(lancamento) AS dia,
       COUNT(*) AS quantidade_contratos,
       'cancelamento' AS tipo
FROM cancelamentoassinaturadigital
WHERE lancamento IS NOT NULL
GROUP BY DATE(lancamento)

ORDER BY dia, tipo;
