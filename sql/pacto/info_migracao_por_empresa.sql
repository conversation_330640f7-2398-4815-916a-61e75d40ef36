  -- PEGA OS RECURSOS POR EMPRESA **** NOVO ******
  select 
  i.codigo,
  i.dataregistro ,
  i.tipoinfo,
  --i.empresa ,
  --i.padrao as recurso_fixado,
  case when coalesce(i.padrao,false) then 'FIXA' else 'OPCIONAL' end as rec_ep_padrao,
  e.cod_empresafinanceiro,
  te.descricao as tela_descricao,
  total_usu.na_antiga,
  total_usu.na_nova
  from infomigracaohistoricoempresa i
     inner join empresa e on e.codigo = i.empresa
     left join tabelagenericaenum te on upper(te.nomeenum) = 'TIPOINFOMIGRACAOENUM' and te.codigo = i.tipoinfo::text
     left join ( 
				select
					i.dataregistro::date as dataregistro,    
					i.tipoinfo,
				    count(case when i.info = 'true'  then 1 end)  as na_nova,
				    count(case when i.info = 'false' then 1 end)  as na_antiga
				from infomigracaohistorico i
				inner join usuario u on u.codigo = i.usuario
				where i.tipoinfo not in (2, 3)
				  and upper(u.username) not in ('ADMI<PERSON>', 'RECOR')
				  and i.usuario_ativo = true
		--		  and i.dataregistro > '2025-06-19'
				group by 1,2
		) total_usu on total_usu.tipoinfo = i.tipoinfo and total_usu.dataregistro = i.dataregistro::date
where i.tipoinfo not in (2,3)
  and e.ativa = true
  and e.cod_empresafinanceiro > 3
 -- and i.dataregistro > '2025-06-19 00:00:00'
  AND i.dataregistro >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '2 months'
--order by i.tipoinfo , i.dataregistro 