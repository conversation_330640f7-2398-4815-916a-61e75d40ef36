SELECT id,
       numerocupom,
       datapremioportadorcupom,
       valorpremioportadorcupom,
       valorpremioprodutosportadorcupom,
       valorpremiomensalidadeportadorcupom,
       chaveportadorcupom,
       CASE
           WHEN valorpremioprodutosportadorcupom IS NOT NULL THEN 'PRODUTO'
           WHEN valorpremiomensalidadeportadorcupom IS NOT NULL THEN 'MENSALIDADE'
           ELSE 'outro'
           END AS tipo
FROM cupomdesconto
where 1 = 1
  {incremental_query}
ORDER BY id
