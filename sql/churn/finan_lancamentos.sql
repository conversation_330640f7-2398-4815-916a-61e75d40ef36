-- finan_lancamentos -- tem todos os dados do modulo financeiro menos os recebiveis do modulo ADM.
select
--    movconta.codigo as movconta.codigo,
	movconta.datavencimento as movconta_datavencimento,
	tdoc.descricao  as movconta_tipodocumento,
	fpg.descricao as movconta_ds_formapg,
	pessoa.nome as pessoa_nome,
    fornecedor.cnpj as pessoa_cnpj,
	case 
		when colunasmovcontaRateio.tipoes IN (2, 3) then (colunasmovcontaRateio.valor * -1)
		else colunasmovcontaRateio.valor
	end as movconta_valor,
    case
    when colunasmovcontaRateio.tipoes IN (2, 3) then (movconta.valorpago * -1)
    else movconta.valorpago
    end as movconta_valorpago,
    case
    when colunasmovcontaRateio.tipoes IN (2, 3) then (movconta.valororiginalalterado * -1)
    else movconta.valororiginalalterado end as movconta_valororiginal,
	---- Para o caso de ter a mesma movconta com mais de um rateio
	---- Isso estava dando erro no valor pago, pois estava pegando o valor da movconta e não o valor do rateio
	---- Feito para o BI de Contabilidade da EDC
	---
	---- Valor Pago Auditado:
	case
		when movconta.valorpago = 0 then movconta.valorpago
		when (
		select
			count(cmr.movconta)
		from
			movcontarateio cmr
		where
			cmr.movconta = movconta.codigo
	    ) > 1 

	    then 
	        case
			when colunasmovcontaRateio.tipoes in (2, 3) 
	            then (colunasmovcontaRateio.valor * -1)
			else colunasmovcontaRateio.valor
		end
		else 
	        case
			when colunasmovcontaRateio.tipoes in (2, 3) 
	            then (movconta.valorpago * -1)
			else movconta.valorpago
		end
	end as movconta_valorpago_auditado,
	---
	---- Valor Previsto Auditado
	case
		when (
		select
			count(cmr.movconta)
		from
			movcontarateio cmr
		where
			cmr.movconta = movconta.codigo
	    ) > 1 

	    then 
	        case
			when colunasmovcontaRateio.tipoes in (2, 3) 
	            then (colunasmovcontaRateio.valor * -1)
			else colunasmovcontaRateio.valor
		end
		else 
	        case
			when colunasmovcontaRateio.tipoes in (2, 3) 
	            then (movconta.valororiginalalterado * -1)
			else movconta.valororiginalalterado
		end
	end as movconta_valor_previsto_auditado,
	--
	colunasmovcontaRateio.descricao as movconta_descricao,
	movconta.dataquitacao as movconta_dataquitacao,
	conta.descricao as conta_descricao,
	b.codigobanco  as conta_codigobanco,
	--
	case
		when colunasmovcontaRateio.tipoes = 1 then 'Entrada'
		when colunasmovcontaRateio.tipoes = 2 then 'Saída'
		when colunasmovcontaRateio.tipoes = 3 then 'Investimento'
		else colunasmovcontaRateio.tipoes::varchar
    end as tipoes,
	pc.codigoplanocontas as planocontas,
	pc.nome as nome_planocontas,
	cc.codigocentrocustos  as centrocustos,
	cc.nome as nome_centrocustos,
	caixa.codigo as caixa_codigo,
	--
	movconta.tipooperacao as movconta_tipooperacao,
    CASE
        WHEN movconta.tipooperacao = 1 THEN 'Pagamento'
        WHEN movconta.tipooperacao = 2 THEN 'Recebimento'
        WHEN movconta.tipooperacao = 3 THEN 'Depósito'
        WHEN movconta.tipooperacao = 4 THEN 'Transferência'
        WHEN movconta.tipooperacao = 5 THEN 'Estorno'
        WHEN movconta.tipooperacao = 6 THEN 'Troca de Forma de Pagamento'
        WHEN movconta.tipooperacao = 7 THEN 'Ajuste de Saldo'
        WHEN movconta.tipooperacao = 8 THEN 'Custódia'
        WHEN movconta.tipooperacao = 9 THEN 'Recebível Avulso'
        WHEN movconta.tipooperacao = 10 THEN 'Retirada de recebível de lote'
        WHEN movconta.tipooperacao = 11 THEN 'Fluxo de Caixa'
        WHEN movconta.tipooperacao = 12 THEN 'Devolução de cheques'
        ELSE 'Outro'
    END	as movconta_descricao_tipooperacao,
	movconta.pessoa as movconta_pessoa,
	movconta.app as movconta_app,
	movconta.usuario as movconta_usuario,
	movconta.conta as movconta_conta,
	movconta.observacoes as movconta_observacoes,
	movconta.dataUltimaAlteracao 	as movconta_dataultimaalteracao,
	movconta.datalancamento 		as movconta_datalancamento,
	movconta.datacompetencia 		as movconta_datacompetencia,
	movconta.agendamentoFinanceiro as movconta_agendamentoFinanceiro,
	movconta.nrParcela as movconta_nrParcela,
	movconta.lote as movconta_lote,
	movconta.contaorigem as movconta_contaorigem,
	movconta.apresentarnocaixa as movconta_apresentarnocaixa,
	movconta.nfseemitida as movconta_nfseemitida,
	movconta.lotePagouConta as movconta_lotePagouConta,
	movconta.conjuntopagamento as movconta_conjuntopagamento,
	movconta.numerodocumento as movconta_numerodocumento,
	usuario.nome as usuario_nome,
	usuario.colaborador as usuario_colaborador,
	usuario.tipoUsuario as usuario_tipoUsuario,
	 lote.codigo as lote_codigo,
	 lote.usuarioresponsavel as lote_usuarioresponsavel,
	 lote.descricao as lote_descricao,
	 lote.dataLancamento 		as lote_datalancamento,
	 lote.dataDeposito 			as lote_datadeposito,
	 lote.valor as lote_valor,
	 lote.pagamovconta as lote_pagamovconta,
	 lote.avulso as lote_avulso,
	nfseemitida.rps as nfseemitida_rps,
	empresa.cod_empresafinanceiro,
	  split_part(pc.codigoplanocontas,'.',1) as pc_n1, pc1.nome as pc_n1_nome,
	  substring(pc.codigoplanocontas FROM 1 FOR 7) as pc_n2, pc2.nome as pc_n2_nome,
	  substring(pc.codigoplanocontas FROM 1 FOR 11) as pc_n3, pc3.nome as pc_n3_nome
from
	movconta movconta
left join conta conta on 	conta.codigo = movconta.conta
left join banco b on b.codigo = conta.banco 
inner join movcontarateio colunasmovcontaRateio on 	colunasmovcontaRateio.movconta = movconta.codigo
left join planoconta pc on colunasmovcontaRateio.planoconta = pc.codigo 
left join centrocusto cc on colunasmovcontaRateio.centrocusto = cc.codigo 
inner join empresa empresa on 	empresa.codigo = movconta.empresa
left join pessoa pessoa on 	pessoa.codigo = movconta.pessoa
left join fornecedor fornecedor on pessoa.codigo = fornecedor.pessoa 
left join lote lote on 	lote.codigo = movconta.lote
--left join caixamovconta caixamovconta on 	caixamovconta.movconta = movconta.codigo
left join caixa caixa on caixa.codigo = (select max(x1.caixa) from caixamovconta x1 where x1.movconta = movconta.codigo) -- Este foi feito por ser possivel duplicação do do caixamovconta
inner join usuario usuario on 	usuario.codigo = movconta.usuario
left join nfseemitida nfseemitida on 	movconta.codigo = nfseemitida.movconta
  left join planoconta pc1 on pc1.codigoplanocontas = split_part(pc.codigoplanocontas,'.',1)
  left join planoconta pc2 on pc2.codigoplanocontas = substring(pc.codigoplanocontas FROM 1 FOR 7)
  left join planoconta pc3 on pc3.codigoplanocontas = substring(pc.codigoplanocontas FROM 1 FOR 11)
left join tipodocumento tdoc on tdoc.codigo = colunasmovcontaRateio.tipodocumento
left join formapagamento fpg on fpg.codigo = colunasmovcontaRateio.formapagamento
    -- where
    -- 	1 = 1
    -- 	and movconta.empresa in (1)
    -- 	and movconta.dataquitacao  >= '2023-04-03 00:00:00'
    -- 	and movconta.dataquitacao  <= '2023-04-11 23:59:59'
    -- order by
    -- 	movconta.dataquitacao  desc
--select 
--  split_part(pc.codigoplanocontas,'.',1) as PC_N1, pc1.nome as PC_N1_Nome,
--  substring(pc.codigoplanocontas FROM 1 FOR 7) as PC_n2, pc2.nome as PC_N2_Nome,
--  substring(pc.codigoplanocontas FROM 1 FOR 11) as PC_n3, pc3.nome as PC_N3_Nome,
--  substring(pc.codigoplanocontas FROM 1 FOR 15) as PC_n4,
--  substring(pc.codigoplanocontas FROM 1 FOR 19) as PC_n5,
--  pc.codigoplanocontas ,
--  pc.nome 
--from planoconta pc
--  left join planoconta pc1 on pc1.codigoplanocontas = split_part(pc.codigoplanocontas,'.',1)
--  left join planoconta pc2 on pc2.codigoplanocontas = substring(pc.codigoplanocontas FROM 1 FOR 7)
--  left join planoconta pc3 on pc3.codigoplanocontas = substring(pc.codigoplanocontas FROM 1 FOR 11)
--order by pc.codigoplanocontas
