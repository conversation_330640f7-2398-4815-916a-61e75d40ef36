
WITH
meses AS (
    SELECT
        _chave,
        codigo,
        DATE_TRUNC('month', dataentrada) AS mes,
        COUNT(*) AS total_mes
    FROM
        acessocliente
    WHERE
        dataentrada >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months'
    GROUP BY
        _chave, codigo, DATE_TRUNC('month', dataentrada)
),
semanas AS (
    SELECT
        _chave,
        codigo,
        DATE_TRUNC('week', dataentrada) AS semana,
        COUNT(*) AS total_semana
    FROM
        acessocliente
    WHERE
        dataentrada >= DATE_TRUNC('month', CURRENT_DATE)
    GROUP BY
        _chave, codigo, DATE_TRUNC('week', dataentrada)
)

-- Agregação final
SELECT
    m._chave,
    m.codigo,
    COALESCE(SUM(CASE WHEN m.mes = DATE_TRUNC('month', CURRENT_DATE) THEN m.total_mes END), 0) AS mes_atual,
    COALESCE(SUM(CASE WHEN m.mes = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month' THEN m.total_mes END), 0) AS mes_passado,
    COALESCE(SUM(CASE WHEN m.mes = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '2 months' THEN m.total_mes END), 0) AS mes_3,
    COALESCE(SUM(CASE WHEN m.mes = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' THEN m.total_mes END), 0) AS mes_4,
    COALESCE(SUM(CASE WHEN s.semana = DATE_TRUNC('week', CURRENT_DATE) THEN s.total_semana END), 0) AS semana_atual,
    COALESCE(SUM(CASE WHEN s.semana = DATE_TRUNC('week', CURRENT_DATE) - INTERVAL '1 week' THEN s.total_semana END), 0) AS semana_1,
    COALESCE(SUM(CASE WHEN s.semana = DATE_TRUNC('week', CURRENT_DATE) - INTERVAL '2 weeks' THEN s.total_semana END), 0) AS semana_2,
    COALESCE(SUM(CASE WHEN s.semana = DATE_TRUNC('week', CURRENT_DATE) - INTERVAL '3 weeks' THEN s.total_semana END), 0) AS semana_3
FROM
    meses m
LEFT JOIN
    semanas s
    ON m._chave = s._chave AND m.codigo = s.codigo
GROUP BY
    m._chave, m.codigo
ORDER BY
    m._chave, m.codigo;