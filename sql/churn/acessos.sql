WITH acessos AS (
    SELECT
        ac.cliente AS s_codigocliente,
        ac.dthrentrada AS ac_dthrentrada
    FROM acessocliente ac
),
acessos_por_mes AS (
    SELECT
        s_codigocliente,
        COUNT(DISTINCT DATE(ac_dthrentrada)) AS qtd_acessos,
        EXTRACT(YEAR FROM ac_dthrentrada) AS ano,
        EXTRACT(MONTH FROM ac_dthrentrada) AS mes
    FROM acessos
    WHERE ac_dthrentrada < DATE_TRUNC('month', CURRENT_DATE) -- Ignorar o mês atual
    GROUP BY s_codigocliente, ano, mes
),
acessos_por_semana AS (
    SELECT
        s_codigocliente,
        COUNT(DISTINCT DATE(ac_dthrentrada)) AS qtd_acessos,
        EXTRACT(YEAR FROM ac_dthrentrada) AS ano,
        EXTRACT(WEEK FROM ac_dthrentrada) AS semana
    FROM acessos
    GROUP BY s_codigocliente, ano, semana
)
SELECT 
    'aluno' AS ac_de,
    e.nome AS e_nome,
    e.cod_empresafinanceiro AS e_empresafinanceiro,
    e.codigo AS e_codigo,
    pes.nome AS ac_nomepessoa,
    ac.cliente AS s_codigocliente,
    ac.codigo AS ac_codigo,
    ac.dthrentrada AS ac_dthrentrada,
    ac.dthrsaida AS ac_dthrsaida,
    ac.meioidentificacaoentrada AS ac_identificacao,
    ac.liberacaoacesso AS ac_liberacaoacesso,
    ac.localacesso AS ac_localacesso,
    loc.descricao AS ac_localdescricao,

    -- Contagem de acessos por mês (ignorando o mês atual)
    COALESCE(m2.qtd_acessos, 0) AS diasacessomes2,
    COALESCE(m3.qtd_acessos, 0) AS diasacessomes3,
    COALESCE(m4.qtd_acessos, 0) AS diasacessomes4,
    COALESCE(ulmes.qtd_acessos, 0) AS diasacessoultimomes,

    -- Contagem de acessos por semana (começando na semana atual)
    COALESCE(s2.qtd_acessos, 0) AS diasacessosemana2,
    COALESCE(s3.qtd_acessos, 0) AS diasacessosemana3,
    COALESCE(s4.qtd_acessos, 0) AS diasacessosemana4,
    COALESCE(ultsemana.qtd_acessos, 0) AS diasacessosemanapassada

FROM acessocliente ac
    INNER JOIN cliente cli ON cli.codigo = ac.cliente 
    INNER JOIN pessoa pes ON pes.codigo = cli.pessoa
    INNER JOIN empresa e ON e.codigo = cli.empresa 
    LEFT JOIN localacesso loc ON loc.codigo = ac.localacesso

    -- Junção para contagem mensal
    LEFT JOIN acessos_por_mes m2 ON m2.s_codigocliente = ac.cliente AND m2.mes = EXTRACT(MONTH FROM CURRENT_DATE) - 2
    LEFT JOIN acessos_por_mes m3 ON m3.s_codigocliente = ac.cliente AND m3.mes = EXTRACT(MONTH FROM CURRENT_DATE) - 3
    LEFT JOIN acessos_por_mes m4 ON m4.s_codigocliente = ac.cliente AND m4.mes = EXTRACT(MONTH FROM CURRENT_DATE) - 4
    LEFT JOIN acessos_por_mes ulmes ON ulmes.s_codigocliente = ac.cliente AND ulmes.mes = EXTRACT(MONTH FROM CURRENT_DATE) - 1

    -- Junção para contagem semanal
    LEFT JOIN acessos_por_semana s2 ON s2.s_codigocliente = ac.cliente AND s2.semana = EXTRACT(WEEK FROM CURRENT_DATE)
    LEFT JOIN acessos_por_semana s3 ON s3.s_codigocliente = ac.cliente AND s3.semana = EXTRACT(WEEK FROM CURRENT_DATE) - 1
    LEFT JOIN acessos_por_semana s4 ON s4.s_codigocliente = ac.cliente AND s4.semana = EXTRACT(WEEK FROM CURRENT_DATE) - 2
    LEFT JOIN acessos_por_semana ultsemana ON ultsemana.s_codigocliente = ac.cliente AND ultsemana.semana = EXTRACT(WEEK FROM CURRENT_DATE) - 1

LIMIT 500;
