ALTER VIEW [dbo].[GC01.1_Receita_Compliance_NF_V2]
AS
SELECT
 
 CAT.DS_<PERSON>TEGORIA									AS CATEGORIA,
 SUB.DS_CATEGORIA									AS SUBCATEGORIA,
 F.ID_FAVORECIDO									AS ID_CLIENTE,
 F.NO_FAVORECIDO									AS CLIENTE,
 AP.DESCRICAO										AS DESCRICAO,
 API.VL_ITEM										AS VALOR,
 DATEPART(YEAR, AP.DTHR_QUITACAO)					AS ANO_QUIT,
 DATEPART(MONTH, AP.DTHR_QUITACAO)					AS MES_QUIT,
 DATEPART(DAY, AP.DTHR_QUITACAO)					AS DIA_QUIT,
 DATEPART(YEAR, AP.DTHR_VENCIMENTO)					AS ANO_VENC,
 DATEPART(MONTH, AP.DTHR_VENCIMENTO)				AS MES_VENC,
 DATEPART(DAY, AP.DTHR_VENCIMENTO)					AS DIA_VENC,
 CASE
 WHEN AP.NumNFSe = '' OR AP.NumNFSe IS NULL
 THEN 'NF NAO EMITIDA'
 ELSE AP.NumNFSe
 END												AS NUM_NF,
 DATEPART(YEAR, AP.DataProcessamentoNFSe)			AS ANO_EMISSAO_NF,
 DATEPART(MONTH, AP.DataProcessamentoNFSe)			AS MES_EMISSAO_NF,
 DATEPART(DAY, AP.DataProcessamentoNFSe)			AS DIA_EMISSAO_NF,
 DIP.Valor											AS VALOR_DESCONTO,
 DIP.Descricao										AS DESCRICAO_VALOR,
 US.NOMEUSUARIO										AS CEDENTE_DESCONTO

FROM

			FI_FAVORECIDO F
LEFT JOIN	FI_AGENDAMENTO A						ON F.ID_FAVORECIDO = A.ID_FAVORECIDO
INNER JOIN	FI_AGENDPARCELAS AP						ON A.ID_AGENDAMENTO = AP.ID_AGENDAMENTO
INNER JOIN	FI_AGENDPARCITENS API					ON AP.ID_AGENDPARCELA = API.ID_AGENDPARCELA
INNER JOIN	FI_CATEGORIA SUB						ON SUB.ID_CATEGORIA = API.ID_CATEGORIA
INNER JOIN	FI_CATEGORIA CAT						ON CAT.ID_CATEGORIA = SUB.ID_CATMAE
LEFT JOIN	DescontoItemParcela DIP					ON API.ID_AGENDPARCITEM = DIP.CodItemParcela
LEFT JOIN	CM_USUARIOFINANCEIRO US					ON DIP.CodUsuario = US.CODPESSOA

WHERE

CAT.DS_CATEGORIA IN ('1- RECEITA BRUTA')
AND API.VL_ITEM >= 1
AND DATEPART(YEAR, AP.DTHR_QUITACAO) IN ('2023','2024') 