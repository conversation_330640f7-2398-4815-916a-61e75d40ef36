
select
  FORMAT(ap.DATACOMPETENCIA, '01/MM/yyyy') AnoMes_Comp,
  FORMAT(ap.dthr_vencimento, '01/MM/yyyy') AnoMes_Venc,
  FORMAT(ap.DTHR_QUITACAO, '01/MM/yyyy') AnoMes_Quit,
  ctm.ds_categoria Categoria,
  ct.id_categoria id_SubCategoria,
  ct.ds_categoria SubCategoria,
  subcla.ds_classificacao Classificacao,
  cla.id_classificacao id_SubClassificacao,
  cla.ds_classificacao SubClassificacao,
  grupoN1.tipo ds_grupotipo,
  CASE
    WHEN ct.tipomovimento = 1 THEN 'Saidas (-)'
    ELSE 'Entradas (+)'
  END as EntSai,
  CASE
    WHEN ct.tipomovimento = 1 THEN (api.vl_item*-1)
    ELSE api.vl_item
  END as ValorItem,
  api.DESCRICAO as DescricaoDoItem,
  ap.descricao AS DescricaoDaParcela,
  ap.dthr_lancamento,
  DATEPART(Year, dthr_lancamento) as Ano_Lanc, DATEPART(Month, dthr_lancamento) as Mes_Lanc, DATEPART(Day, dthr_lancamento) as Dia_Lanc,
  ap.dthr_quitacao,
  DATEPART(Year, dthr_quitacao) as Ano_Quit, DATEPART(Month, dthr_quitacao) as Mes_Quit, DATEPART(Day, dthr_quitacao) as Dia_Quit,
  ap.dthr_vencimento,
  DATEPART(Year, dthr_vencimento) as Ano_Venc, DATEPART(Month, dthr_vencimento) as Mes_Venc, DATEPART(Day, dthr_vencimento) as Dia_Venc,
  ap.DATACOMPETENCIA,
  DATEPART(Year, DATACOMPETENCIA) as Ano_Comp, DATEPART(Month, DATACOMPETENCIA) as Mes_Comp, DATEPART(Day, DATACOMPETENCIA) as Dia_Comp,
  ap.nr_cheque,
  ap.nr_documento,
  ap.nr_parcela,
  ap.ID_AGENDPARCELA as ID_PARCELA,
  api.ID_AGENDPARCITEM ID_ITEMPARCELA, 
  a.PERIODICO AS AGD_PERIODICO,
  a.PERIODICIDADE AS AGD_PERIODICIDADE,
  co.ABREVIACAO as ContaOrigem,
  cd.ABREVIACAO as ContaDestino,
  f.NO_FAVORECIDO,
--  f.ENDERECO,
  f.CIDADE,
  f.ESTADO,
  substring(F.TELEFONE, 1, 220) AS TELEFONE,
  f.ABREVIACAO as AbreviacaoFavorecido,
  f.EMPRESA as NO_FAVOREC_Empresa,
  f.CONTATO as PessoaDeContato,
--  f.FAX,
  substring(F.EMAIL, 1, 250) AS EMAIL,
  F.DATAATUALIZACAO as DataAtualizacaoCadastro,
  F.DATACADASTRO,
  DATEPART(Year, F.DATACADASTRO) as Ano_Cad, DATEPART(Month, F.DATACADASTRO) as Mes_Cad, DATEPART(Day, F.DATACADASTRO) as Dia_Cad,
  F.Data_Expiracao,
  F.CHAVEZW,
  F.CODIGOZW,
  F.CGC,
  CASE
    WHEN DATEPART(YEAR, COALESCE(AP.DTHR_QUITACAO, '1899-12-30')) < 1900 THEN 'ABERTO'
    ELSE 'QUITADO'
  END AS QUITADO,
  CASE
    WHEN AP.DTHR_VENCIMENTO < (GETDATE()-1)  THEN 'VENCIDO'
    ELSE 'NAO'
  END AS VENCIDO,
  CASE
    WHEN AP.DATACOMPETENCIA < (GETDATE()-1)  THEN 'VENCIDA'
    ELSE 'FUTURA'
  END AS STATUSCOMPETENCIA,
  CASE
    WHEN ( ap.ID_CONTADESTINO > 0
        AND ap.ID_CONTAORIGEM > 0 ) THEN 'SIM'
    ELSE 'NAO'
  END AS TRANFERENCIA,
  CASE
    WHEN DATEPART(YEAR, COALESCE(AP.DTHR_QUITACAO, '1899-12-30')) < 1900 THEN 'NAO PAGO'
    WHEN DATEDIFF(DAY, CONVERT(DATE, ap.DATACOMPETENCIA), CONVERT(DATE, ap.DTHR_QUITACAO)) <= -1 THEN 'ANTECIPADO'
    WHEN DATEDIFF(DAY, CONVERT(DATE, ap.DATACOMPETENCIA), CONVERT(DATE, ap.DTHR_QUITACAO)) <= 3 THEN  'ATE03-EM DIA'
    WHEN DATEDIFF(DAY, CONVERT(DATE, ap.DATACOMPETENCIA), CONVERT(DATE, ap.DTHR_QUITACAO)) <= 10 THEN 'ATE10-ATRASADO'
    WHEN DATEDIFF(DAY, CONVERT(DATE, ap.DATACOMPETENCIA), CONVERT(DATE, ap.DTHR_QUITACAO)) <= 30 THEN 'ATE30-MUITO ATRASADO '
    ELSE 'MAIS DE 30 ATRASADO'
  END AS PAGAEMDIA,
  CASE
    WHEN DATEPART(YEAR, COALESCE(AP.DTHR_QUITACAO, '1899-12-30')) > 1900
      THEN DATEDIFF(DAY, CONVERT(DATE, ap.DTHR_QUITACAO), CONVERT(DATE, ap.DATACOMPETENCIA))
    ELSE   DATEDIFF(DAY, GETDATE(), CONVERT(DATE, ap.DATACOMPETENCIA))
  END
   AS DIAS_ATRASO,
  CASE
     WHEN ct.EQUIVALENCIADRE = 1 THEN 'NAO ENTRAR NO DRE'
     WHEN ct.EQUIVALENCIADRE = 2 THEN '01 - RECEITA BRUTA'
     WHEN ct.EQUIVALENCIADRE = 3 THEN '02 - CUSTO ESPECIFICO'
     WHEN ct.EQUIVALENCIADRE = 4 THEN '03 - DESPESA OPERACIONAL'
     ELSE 'NAO ENTRAR NO DRE ---- VAZIO'
  END AS DRE,
  CASE
     WHEN AP.EFEITO = 4 THEN 'NOTA MATERIAL'
     WHEN AP.EFEITO = 3 THEN 'FISCAL'
     WHEN AP.EFEITO = 2 THEN 'GERENCIA'
     ELSE '---VERIFICAR'
  END AS EFEITO,
  evt.DS_EVENTO,
  CASE DATEPART(Month, CASE WHEN DATEPART(YEAR, COALESCE(AP.DTHR_QUITACAO, '1899-12-30')) > 1900 THEN AP.DTHR_QUITACAO ELSE NULL END)
     WHEN 1 THEN 1
     WHEN 2 THEN 1
     WHEN 3 THEN 1
     WHEN 4 THEN 2
     WHEN 5 THEN 2
     WHEN 6 THEN 2
     WHEN 7 THEN 3
     WHEN 8 THEN 3
     WHEN 9 THEN 3
     WHEN 10 THEN 4
     WHEN 11 THEN 4
     WHEN 12 THEN 4
     ELSE 0
  END AS TRIMESTRE,
  CASE ap.TIPOCOBRANCA
      When 1 then 'Nenhum'
      When 2 then 'Boleto'
      When 3 then 'DCC'
      When 4 then 'DCO'
      When 5 then 'Carteira'
      When 6 then 'Depósito'
      When 7 then 'TEF'
      When 8 then 'Outros'
      When 9 then 'Serasa'
      When 10 then 'Jurídico'
      When 11 then 'Perdido'
      ELSE ''
  END AS TIPOCOBRANCA,
--  ap.META,
  ap.NUMNFSE,
  DATEPART(Year, ap.DATAPROCESSAMENTONFSE) as Ano_NotaEmitida, 
     DATEPART(Month, ap.DATAPROCESSAMENTONFSE) as Mes_NotaEmitida, 
     DATEPART(Day, ap.DATAPROCESSAMENTONFSE) as Dia_NotaEmitida,
  F.OBSERVACAOZW,
  ap.DATAPROCESSAMENTONFSE,
  DATEPART(YEAR, prc.DATAINICIOCONTRATO) AnoInicio,
     DATEPART(MONTH, prc.DATAINICIOCONTRATO) MesInicio,
     DATEPART(DAY, prc.DATAINICIOCONTRATO) DiaInicio,
  p.NOME NOMEPRODUTO,
  RE.Nome RedeEmpresas,
  ap.Meta,  
  --
  dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N1]) + ' - ' + PC.[Plano Contas Filho N1] [Plano Contas N1], 
  dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N2]) + ' - ' + PC.[Plano Contas Filho N2] [Plano Contas N2], 
  dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N3]) + ' - ' + PC.[Plano Contas Filho N3] [Plano Contas N3],
  dbo.TratarCodigoPlanoContas(PCIN1.CodigoPlanoContas) + ' - ' + PCIN1.Nome [Centro Custo N1],
  dbo.TratarCodigoPlanoContas(PCIN2.CodigoPlanoContas) + ' - ' + PCIN2.Nome [Centro Custo N2]
  --
         , ATU.CodIntegracaoFinanceiroOAMD as id_Ioamd
         , CAST(atu.DataHora AS date) [DataPegaTotAlunosZW]
         , DATEPART(MONTH, atu.DataHora) MesDataPegaTotAlunosZW
         , DATEPART(YEAR, atu.DataHora) AnoDataPegaTotAlunosZW
         , atu.Ativos	AlunosAtivos
         , atu.Vencidos AlunosVencidos
         , atu.Ativos + atu.Vencidos AlunosAtivos_e_Vencidos
		 , CASE WHEN f.TipoCobrancaZW = 1 THEN 'A+V'
		        WHEN f.TipoCobrancaZW = 2 THEN 'So A'
				ELSE 'Verificar'
		   END TipoCobrancaZW 
		 ,CASE
            WHEN (f.TipoCobrancaZW = 1) AND (atu.Ativos + atu.Vencidos) <= 10 THEN '00 - FAIXA0_010'
            WHEN (f.TipoCobrancaZW = 1) AND (atu.Ativos + atu.Vencidos) <= 200 THEN '01 - FAIXA1_350'
            WHEN (f.TipoCobrancaZW = 1) AND (atu.Ativos + atu.Vencidos) <= 500 THEN '02 - FAIXA2_500'
            WHEN (f.TipoCobrancaZW = 1) AND (atu.Ativos + atu.Vencidos) <= 800 THEN '03 - FAIXA3_750'
            WHEN (f.TipoCobrancaZW = 1) AND (atu.Ativos + atu.Vencidos) > 800 THEN '04 - FAIXA4_1100'
		    WHEN (f.TipoCobrancaZW = 2) AND (atu.Ativos <= 10 ) THEN '00 - FAIXA0_010'
            WHEN (f.TipoCobrancaZW = 2) AND (atu.Ativos <= 200) THEN '01 - FAIXA1_350'
            WHEN (f.TipoCobrancaZW = 2) AND (atu.Ativos <= 500) THEN '02 - FAIXA2_500'
            WHEN (f.TipoCobrancaZW = 2) AND (atu.Ativos <= 800) THEN '03 - FAIXA3_750'
            WHEN (f.TipoCobrancaZW = 2) AND (atu.Ativos > 800 ) THEN '04 - FAIXA4_1100'
            ELSE NULL
         END AS faixa_clientes_ativos,
  --       
  grupoN1.DESCRICAO as Grupo_N1,     
  grupoN2.DESCRICAO as Grupo_N2,
  grupoN3.DESCRICAO as Grupo_N3,
  CASE WHEN (DATEPART(YEAR, COALESCE(F.DATADESATIVACAO, '1899-12-30'))) < 1900 THEN NULL
       ELSE F.DATADESATIVACAO  
  END AS DATADESATIVACAO,
  DATEPART(Year, F.DATADESATIVACAO) as Ano_Desat, DATEPART(Month, F.DATADESATIVACAO) as Mes_Desat, DATEPART(Day, F.DATADESATIVACAO) as Dia_Desat,
  f.ID_FAVORECIDO,
  p.ContratoWeb,
  f.Vinculo,
  f.Funcao,
  CASE
    WHEN (grupoN1.DESCRICAO like '%COLABORADOR%' ) THEN 
      (select top 1 x1.NO_FAVORECIDO from FI_FAVORECIDO x1 where x1.ID_FAVORECIDO = f.ID_FAVORECIDO)
    ELSE   
      (select top 1 fip.VENDEDOR
					from FI_PRODUTOSCLIENTE fip 
					WHERE ID_FAVORECIDO = f.ID_FAVORECIDO
					and fip.CODPRODUTO in ('189','196','241')
					and fip.VENDEDOR is not null
					order by fip.DTHR_COMPRA desc)
  END as VENDEDOR,

--VALOR DO DESCONTO CONCEDIDO, ACRESCENTADO POR CARLOS EDUARDO EM 14/11/2023

DIP.CodDescontoItemParcela AS ID_DESCONTO,
DIP.Valor AS VALOR_DESCONTO,
DIP.Descricao AS DESCRICAO_VALOR,
US.NOMEUSUARIO AS CEDENTE_DESCONTO


from
  fi_categoria ct
left outer join fi_categoria ctm
  on ct.id_catmae = ctm.id_categoria
inner join fi_agendparcitens api
  on ct.id_categoria = api.id_categoria
inner join fi_agendparcelas ap
  on ap.id_agendparcela = api.id_agendparcela
left outer join fi_conta co
  on ap.id_contaorigem = co.id_conta
left outer join fi_conta cd
  on ap.id_contadestino = cd.id_conta
inner join fi_agendamento a
  on ap.id_agendamento = a.id_agendamento
left outer join fi_favorecido f
  on a.id_favorecido = f.id_favorecido
left outer join fi_classificacao cla
  on api.id_classificacao = cla.id_classificacao
left outer join fi_classificacao subcla
  on cla.id_classmae = subcla.id_classificacao
left outer join fi_gruposfavorecido grupoN1 
  on f.id_grupofavorecido = grupoN1.id_grupofavorecido
left outer join fi_gruposfavorecido grupoN2 
  on f.CodGrupoFuncionario = grupoN2.id_grupofavorecido
left outer join fi_gruposfavorecido grupoN3 
  on f.CodSubGrupoFuncionario = grupoN3.id_grupofavorecido
left outer join FI_EVENTO evt
  on ap.id_evento = evt.id_evento
-- Produto do lançamento ----------------------
left outer join FI_PRODUTO p
  on a.CodProduto = p.CODPRODUTO
left outer join (
        SELECT ID_FAVORECIDO
             , CODPRODUTO
             , MAX(ID_PRODUTOSCLIENTE) ID_PRODUTOSCLIENTE
          FROM FI_PRODUTOSCLIENTE
      GROUP BY ID_FAVORECIDO
             , CODPRODUTO) pc1
  on f.ID_FAVORECIDO = pc1.ID_FAVORECIDO and
     pc1.CODPRODUTO = p.CODPRODUTO
left outer join FI_PRODUTOSCLIENTE prc
  on pc1.ID_PRODUTOSCLIENTE = prc.ID_PRODUTOSCLIENTE
-----------------------------------------------
 LEFT JOIN (    SELECT C1.ID_CATEGORIA [ID Categoria Mãe]
                    , C1.DS_CATEGORIA [Categoria Mãe]
                    , PCC11.CodigoPlanoContas [Cód. Plano Contas Mãe N1]
                    , PCC11.Nome [Plano Contas Mãe N1]
                    , PCC12.CodigoPlanoContas [Cód. Plano Contas Mãe N2]
                    , PCC12.Nome [Plano Contas Mãe N2]
                    , PCC13.CodigoPlanoContas [Cód. Plano Contas Mãe N3]
                    , PCC13.Nome [Plano Contas Mãe N3]
                    , C2.ID_CATEGORIA [ID Categoria Filho]
                    , C2.DS_CATEGORIA [Categoria Filho]
                    , ISNULL(PCC21.CodigoPlanoContas, PCC11.CodigoPlanoContas) [Cód. Plano Contas Filho N1]
                    , ISNULL(PCC21.Nome, PCC11.Nome) [Plano Contas Filho N1]
                    , ISNULL(PCC22.CodigoPlanoContas, PCC12.CodigoPlanoContas) [Cód. Plano Contas Filho N2]
                    , ISNULL(PCC22.Nome, PCC12.Nome) [Plano Contas Filho N2]
                    , ISNULL(PCC23.CodigoPlanoContas, PCC13.CodigoPlanoContas) [Cód. Plano Contas Filho N3]
                    , ISNULL(PCC23.Nome, PCC13.Nome) [Plano Contas Filho N3]
                 FROM (SELECT * FROM FI_CATEGORIA WHERE ID_CATMAE IS NULL) C1
            LEFT JOIN (SELECT * FROM FI_CATEGORIA WHERE ID_CATMAE IS NOT NULL) C2 ON C1.ID_CATEGORIA = C2.ID_CATMAE
            LEFT JOIN PlanoContas PCC10 ON C1.CodPlanoContas = PCC10.CodPlanoContas
            LEFT JOIN PlanoContas PCC11 ON LEFT(PCC10.CodigoPlanoContas, 3) = PCC11.CodigoPlanoContas
            LEFT JOIN PlanoContas PCC12 ON LEFT(PCC10.CodigoPlanoContas, 7) = PCC12.CodigoPlanoContas
                                                        AND PCC11.CodPlanoContas <> PCC12.CodPlanoContas
            LEFT JOIN PlanoContas PCC13 ON LEFT(PCC10.CodigoPlanoContas, 11) = PCC13.CodigoPlanoContas
                                                    AND PCC12.CodPlanoContas <> PCC13.CodPlanoContas
            LEFT JOIN PlanoContas PCC20 ON C2.CodPlanoContas = PCC20.CodPlanoContas
    LEFT JOIN PlanoContas PCC21 ON LEFT(PCC20.CodigoPlanoContas, 3) = PCC21.CodigoPlanoContas
            LEFT JOIN PlanoContas PCC22 ON LEFT(PCC20.CodigoPlanoContas, 7) = PCC22.CodigoPlanoContas
                                                  AND PCC21.CodPlanoContas <> PCC22.CodPlanoContas
            LEFT JOIN PlanoContas PCC23 ON LEFT(PCC20.CodigoPlanoContas, 11) = PCC23.CodigoPlanoContas
                                                        AND PCC22.CodPlanoContas <> PCC23.CodPlanoContas
                WHERE ISNULL(PCC20.CodigoPlanoContas, PCC10.CodigoPlanoContas) IS NOT NULL) PC ON api.ID_CATEGORIA = PC.[ID Categoria Filho]
 LEFT JOIN PlanoContas PCIN2 ON api.CodPlanoContas = PCIN2.CodPlanoContas
 LEFT JOIN PlanoContas PCIN1 ON LEFT(PCIN2.CodigoPlanoContas, 3) = PCIN1.CodigoPlanoContas
 LEFT JOIN RedeEmpresas RE ON F.CodRedeEmpresas = RE.CodRedeEmpresas
----------------
/* Para pegar o numreo de alunos ativos usado para gerar o valor da mensalidade */
 LEFT JOIN (   SELECT   IFOAMD_X.Chave  AS IFOAMD_X_Chave
                     , IFOAMD_X.CodigoEmpresa AS IFOAMD_X_CodigoEp
					 , DATEPART(YEAR, IFOAMD_X.DataHora) AS IFOAMD_X_Ano
					 , DATEPART(MONTH, IFOAMD_X.DataHora) AS IFOAMD_X_Mes 
                     , MAX(IFOAMD_X.CodIntegracaoFinanceiroOAMD) IFOAMD_X_id 
              FROM IntegracaoFinanceiroOAMD IFOAMD_X 
			  GROUP BY  IFOAMD_X.Chave, IFOAMD_X.CodigoEmpresa , DATEPART(YEAR, IFOAMD_X.DataHora), DATEPART(MONTH, IFOAMD_X.DataHora) ) IFOAMD
	 ON (IFOAMD.IFOAMD_X_Chave = f.ChaveZW)  AND (IFOAMD.IFOAMD_X_CodigoEp = f.CodigoZW)
		AND (   IFOAMD.IFOAMD_X_Mes = DATEPART(MONTH, DATEADD(MONTH,-1,ap.DATACOMPETENCIA) ) 
			AND  IFOAMD.IFOAMD_X_Ano = DATEPART(YEAR, DATEADD( MONTH ,-1,ap.DATACOMPETENCIA) ) )
		AND (P.Nome like '%CONTRATO ZW - MÉTODO%') 
 LEFT JOIN IntegracaoFinanceiroOAMD atu ON atu.CodIntegracaoFinanceiroOAMD = IFOAMD.IFOAMD_X_id

--Informação de desconto concedido no financeiro, acrescentado por Carlos Eduardo em 14/11/2023

LEFT JOIN	DescontoItemParcela DIP					ON API.ID_AGENDPARCITEM = DIP.CodItemParcela
LEFT JOIN	CM_USUARIOFINANCEIRO US					ON DIP.CodUsuario = US.CODPESSOA



where
      ap.DataCompetencia BETWEEN '2018-01-01' and '2920-04-30 23:59:58'
   OR ap.DTHR_QUITACAO   BETWEEN '2018-01-01' and '2920-12-31 23:59:58'
   OR ap.DTHR_VENCIMENTO BETWEEN '2018-01-01' and '2920-12-31 23:59:58';
