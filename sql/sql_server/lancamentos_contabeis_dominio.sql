SELECT 
CASE
	WHEN CATM.DS_<PERSON>TEGORIA IS NULL THEN 'TRA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'
	WHEN CAT.TIPOMOVIMENTO = 1 THEN 'DESPESA'
	ELSE 'RECEITA'
END AS TIPO,
F.NO_FAVORECIDO AS FAVORECIDO,
AP.DESCRICAO,
ROUND(API.VL_ITEM, 2) AS VALOR,
AP.NumNFSe AS [NUM NOTA FISCAL],
CAST(AP.DataCompetencia AS DATE) AS [DATA COMPETENCIA],
CAST(AP.DTHR_QUITACAO AS DATE) AS [DATA QUITAÇÃO],
CASE
	WHEN DATEPART(YEAR, AP.DTHR_QUITACAO) = '1899' THEN NULL
	WHEN DATEPART(YEAR, AP.DTHR_QUITACAO) IS NULL THEN NULL
	ELSE DATEPART(YEAR, AP.DTHR_QUITACAO)
END AS [ANO QUIAÇÃO],
CASE
	WHEN DATEPART(YEAR, AP.DTHR_QUITACAO) = '1899' THEN NULL
	WHEN DATEPART(YEAR, AP.DTHR_QUITACAO) IS NULL THEN NULL
	ELSE DATEPART(<PERSON>ONTH, AP.DTHR_QUITACAO)
END AS [MÊS QUITAÇÃO],
CD.DS_CONTA AS [CONTA DESTINO],
CO.DS_CONTA AS [CONTA ORIGEM],
CASE
	WHEN CAT.TIPOMOVIMENTO = 1
		THEN PC.[Cód. Contábil]
	WHEN CAT.TIPOMOVIMENTO = 2
		THEN CD.CodigoContabil
	ELSE CD.CodigoContabil
END AS DEBITO,
CASE
	WHEN CAT.TIPOMOVIMENTO = 1
		THEN CO.CodigoContabil
	WHEN CAT.TIPOMOVIMENTO = 2
		THEN PC.[Cód. Contábil]
	ELSE CO.CodigoContabil
END AS CREDITO,

dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N1]) + ' - ' + PC.[Plano Contas Filho N1] [Plano Contas N1],
dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N2]) + ' - ' + PC.[Plano Contas Filho N2] [Plano Contas N2], 
dbo.TratarCodigoPlanoContas(PC.[Cód. Plano Contas Filho N3]) + ' - ' + PC.[Plano Contas Filho N3] [Plano Contas N3],
dbo.TratarCodigoPlanoContas(PCIN1.CodigoPlanoContas) + ' - ' + PCIN1.Nome [Centro de Custo N1],
dbo.TratarCodigoPlanoContas(PCIN2.CodigoPlanoContas) + ' - ' + PCIN2.Nome [Centro de Custo N2]

FROM
FI_AGENDAMENTO A
INNER JOIN FI_AGENDPARCELAS AP ON A.ID_AGENDAMENTO = AP.ID_AGENDAMENTO
INNER JOIN FI_AGENDPARCITENS API ON AP.ID_AGENDPARCELA = API.ID_AGENDPARCELA
LEFT JOIN FI_CATEGORIA CAT ON API.ID_CATEGORIA = CAT.ID_CATEGORIA
LEFT JOIN FI_CATEGORIA CATM ON CATM.ID_CATEGORIA = CAT.ID_CATMAE
LEFT JOIN FI_CONTA CO ON AP.ID_CONTAORIGEM = CO.ID_CONTA
LEFT JOIN FI_CONTA CD ON AP.ID_CONTADESTINO = CD.ID_CONTA
LEFT JOIN FI_FAVORECIDO F ON F.ID_FAVORECIDO = A.ID_FAVORECIDO
LEFT JOIN GC_Plano_contas_contabil PC ON api.ID_CATEGORIA = PC.[ID Categoria Filho]
LEFT JOIN PlanoContas PCIN2 ON api.CodPlanoContas = PCIN2.CodPlanoContas
LEFT JOIN PlanoContas PCIN1 ON LEFT(PCIN2.CodigoPlanoContas, 3) = PCIN1.CodigoPlanoContas

 WHERE AP.DTHR_QUITACAO >= DATEFROMPARTS(2024,01,01)
 AND API.VL_ITEM > 0.03
 AND PC.[Cód. Plano Contas Filho N2] NOT IN ('010.011','010.012')
 ORDER BY 7 OFFSET 1 ROW
