SELECT     e.idexterno AS idexternoempresa,
           cl.matricula,
           p.nome,
           regexp_replace(p.cfp, '[^0-9]+', '', 'g') as cpf,
           pr.descricao                                          AS produto,
           mp.precounitario                                      AS preco,
           mp.quantidade,
           mp.valordesconto AS desconto,
           mp.totalfinal    AS total,
           CASE
               WHEN mp.situacao = 'PG' THEN 'PAGO'
               WHEN mp.situacao = 'EA' THEN 'EM_ABERTO'
               ELSE ''
               END               AS situacao,
           mp.datalancamento AS datalancamento,
           rp.data           AS datapagamento
FROM       movproduto mp
               INNER JOIN produto pr
                          ON         pr.codigo = mp.produto
               INNER JOIN pessoa p
                          ON         p.codigo = mp.pessoa
               LEFT JOIN  cliente cl
                          ON         cl.pessoa = mp.pessoa
               INNER JOIN empresa e
                          ON         e.codigo = mp.empresa
               LEFT JOIN  recibopagamento rp
                          ON         rp.codigo =
                                     (
                                         SELECT     Max(rb.codigo)
                                         FROM       recibopagamento rb
                                                        INNER JOIN movprodutoparcela mpp
                                                                   ON         mpp.recibopagamento = rb.codigo
                                         WHERE      mpp.movproduto = mp.codigo)
WHERE      Length(COALESCE(e.idexterno,'')) > 0
  AND        e.ativa
  AND        pr.tipoproduto IN ('PE')
  AND        mp.situacao    IN ('PG',
                                'EA')