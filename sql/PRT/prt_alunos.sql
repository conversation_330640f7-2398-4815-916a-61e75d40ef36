SELECT
    ('https://app.pactosolucoes.com.br/api/prest/cliente/'|| pmg.chave ||'/foto/' || p.codigo ) as foto,
    sw.matricula,
    sw.nomecliente AS nome,
    regexp_replace(sw.cpf, '[^0-9]+', '', 'g') as cpf,
    sw.datanascimento::date::timestamp,
        CASE
            WHEN sw.sexocliente = 'M' THEN 'MASCULINO'
            WHEN sw.sexocliente = 'F' THEN 'FEMININO'
            ELSE ''
            END AS sexo,
    array_to_string(array
                    (
                        SELECT email
                  FROM   email
                  WHERE  email.pessoa = sw.codigopessoa), '; ', '') AS emails,
    array_to_string(array
                    (
                        SELECT numero
                  FROM   telefone
                  WHERE  telefone.pessoa = sw.codigopessoa), ' - ', '') AS telefones,
    sw.datacadastro::                                               date::timestamp,
        sw.datamatricula::                                              date::timestamp,
        (select datamatricula::date::timestamp from contrato where codigo = (select max(codigo) from contrato where situacaocontrato = 'MA' and pessoa = sw.codigopessoa and empresa = sw.empresacliente)) as data_matricula_ultimo_contrato,
        pl.descricao                                                 AS plano,
    c.vigenciade::         date::timestamp                                          AS iniciocontrato,
        c.vigenciaateajustada::date::timestamp                                          AS finalcontrato,
        sw.dataultimoacesso,
    CASE
        WHEN COALESCE(p.assinaturabiometriafacial , '') = '' THEN 'NAO'
        WHEN COALESCE(p.assinaturabiometriafacial , '') <> '' THEN 'SIM'
        ELSE ''
        END AS facialcadastrado,
    CASE
        WHEN sw.situacao = 'IN' THEN 'INATIVO'
        WHEN sw.situacao = 'AT' THEN 'ATIVO'
        WHEN sw.situacao = 'VI' THEN 'VISITANTE'
        WHEN sw.situacao = 'TR' THEN 'TRANCADO'
        ELSE ''
        END AS situacao,
    (
        SELECT    pec.nome
        FROM      vinculo v
                      LEFT JOIN colaborador col
                                ON        col.codigo = v.colaborador
                      LEFT JOIN pessoa pec
                                ON        pec.codigo = col.pessoa
        WHERE     v.tipovinculo = 'CO'
          AND       v.cliente = sw.codigocliente limit 1) AS consultor,
           (
                     SELECT    pec.nome
                     FROM      vinculo v
                     LEFT JOIN colaborador col
                     ON        col.codigo = v.colaborador
                     LEFT JOIN pessoa pec
                     ON        pec.codigo = col.pessoa
                     WHERE     v.tipovinculo = 'TW'
                     AND       v.cliente = sw.codigocliente limit 1) AS professor,
           um.nome                                                   AS usuariomovel,
           CASE
                      WHEN um.codigo IS NULL THEN 'APP - NAO CADASTRADO'
                      WHEN um.nome NOT ilike '%@%' THEN 'APP - INCORRETO'
                      WHEN um.nome ilike '%@%' THEN 'APP - COMPLETO'
                      ELSE ''
END AS situacaoapp,
           (
                  SELECT count(*)
                  FROM   movparcela
                  WHERE  situacao = 'PG'
                  AND    valorparcela > 0
                  AND    contrato = sw.codigocontrato) AS qtdparcelaspagas,
           e.nome                                      AS empresa,
           e.idexterno                                 AS id_empresa,
           ende.bairro                                 AS enderecobairro,
           ende.endereco                               AS enderecologradouro,
           ende.complemento                            AS enderecocomplemento,
           ende.numero                                 AS endereconumero,
           ende.cep                                    AS enderecocep,
           p.rg,
           c.datalancamento AS datalancamentocontrato,
           CASE
                      WHEN c.situacaocontrato = 'MA' THEN 'MATRICULA'
                      WHEN c.situacaocontrato = 'RE' THEN 'REMATRICULA'
                      WHEN c.situacaocontrato = 'RN' THEN 'RENOVACAO'
                      ELSE ''
END                                                                           AS situacaocontrato,
           coop.datainicioefetivacaooperacao::date::timestamp                                       AS inicio_trancamento,
           coop.datafimefetivacaooperacao::   date::timestamp                                       AS fim_trancamento,
           ci.nome                                                                       AS cidade,
           es.sigla                                                                      AS estado,
           substr(au.cartaomascaradointerno, (length(au.cartaomascaradointerno) - 3), 4) AS ultimos_dig,
           au.validadecartao as card_validade,
           CASE
                      WHEN sw.diasacessoultimomes < 0 THEN 0
                      ELSE sw.diasacessoultimomes
END AS acessos_mes_atual,
           CASE
                      WHEN sw.diasacessomes2 < 0 THEN 0
                      ELSE sw.diasacessomes2
END AS acessos_mes_anterior_1,
           CASE
                      WHEN sw.diasacessomes3 < 0 THEN 0
                      ELSE sw.diasacessomes3
END AS acessos_mes_anterior_2,
           CASE
                      WHEN sw.diasacessosemanapassada < 0 THEN 0
                      ELSE sw.diasacessosemanapassada
END AS acessos_ultimos_7dias,
ac.nomeCodEmpresaAcessou as nome_empresa_acessou,
c.codigo AS codigoContrato
FROM       situacaoclientesinteticodw sw
LEFT JOIN dadosgerencialpmg pmg on pmg.codigo = (select max(codigo) from dadosgerencialpmg)
LEFT JOIN  contrato c
ON         c.codigo = sw.codigocontrato
INNER JOIN pessoa p
ON         p.codigo = sw.codigopessoa
LEFT JOIN  cidade ci
ON         ci.codigo = p.cidade
LEFT JOIN  estado es
ON         es.codigo = p.estado
LEFT JOIN  plano pl
ON         pl.codigo = c.plano
INNER JOIN empresa e
ON         e.codigo = sw.empresacliente
LEFT JOIN  endereco ende
ON         ende.codigo =
           (
                  SELECT max(codigo)
                  FROM   endereco
                  WHERE  pessoa = sw.codigopessoa)
LEFT JOIN  usuariomovel um
ON         um.codigo =
           (
                  SELECT max(codigo)
                  FROM   usuariomovel
                  WHERE  ativo
                  AND    cliente IS NOT NULL
                  AND    cliente = sw.codigocliente)
LEFT JOIN  contratooperacao coop
ON         coop.codigo =
           (
                  SELECT max(codigo)
                  FROM   contratooperacao
                  WHERE  tipooperacao = 'TR'
                  AND    contrato = c.codigo)
AND        sw.situacao = 'TR'
LEFT JOIN  autorizacaocobrancacliente au
ON         au.codigo =
           (
                  SELECT max(codigo)
                  FROM   autorizacaocobrancacliente
                  WHERE  ativa
                  AND    tipoautorizacao = 1
                  AND    cliente = sw.codigocliente)
inner join cliente cli on cli.codigo = sw.codigocliente
left join acessocliente ac on ac.codigo = cli.uacodigo
WHERE      length(COALESCE(e.idexterno,'')) > 0
AND        e.ativa
