SELECT 
    cli.matricula,
    pes.nome AS cliente,
    mpro.descricao,
    mpro.datalancamento,
    pla.descricao AS plano,
    array_to_string(array(select tel.numero from telefone tel where tel.pessoa = mpro.pessoa),', ') as telefones
FROM movproduto mpro
	INNER JOIN  cliente cli ON cli.pessoa = mpro.pessoa
	INNER JOIN  pessoa pes ON pes.codigo = cli.pessoa
	LEFT JOIN contrato con ON con.codigo = mpro.contrato
	LEFT JOIN plano pla ON pla.codigo = con.plano
WHERE 1 = 1
AND mpro.descricao LIKE '%VEM PARA PRATIQUE%'
--AND con.datalancamento >= '2024-12-01' 
--AND con.datalancamento <= '2024-12-31';