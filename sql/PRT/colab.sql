select
	e.codigorede,
	e.cod_empresafinanceiro as idfavorecido,
	e.cod_empresafinanceiro || '-' || col.pessoa    as id_pessoa,
	e.codigo as codigo_unidade,
	uf.sigla as uf,
	cid.nome as cidade,
	e.nome as nome_empresa,
	col.codigo as codigo_colaborador,
	pes.nome as nome_colaborador,
	dp.nome as departamento,
	array_to_string(
		array(
			select p.nome
			from usuario usu
				inner join usuarioperfilacesso upa on usu.codigo = upa.usuario
				inner join perfilacesso p on upa.perfilacesso = p.codigo
			where usu.colaborador = col.codigo
		), ',', ''
	) as perfis_acesso,
	prof.descricao as profissao,
	pes.datanasc as data_nascimento,
	coalesce(nullif(pes.contatoemergencia, ''), '#') as contato_emergencia,
	coalesce(nullif(pes.telefoneemergencia, ''), '#') as telefone_emergencia,
	pes.cfp as cpf,
	coalesce(nullif(pes.rg, ''), '#') as rg,
	coalesce(nullif(col.cref, ''), '#') as cref,
	col.cargahoraria as carga_horaria,
	array_to_string(
		array(
			select tc.descricao
			from tipocolaborador tc
			where tc.colaborador = col.codigo
		), ',', ''
	) as tiposColaborador,
	array_to_string(
		array(
			select email
			from email em
			where em.pessoa = col.pessoa
		), ',', ''
	) as emails,
	array_to_string(
		array(
			select tel.numero
			from telefone tel
			where tel.pessoa = col.pessoa
		), ',', ''
	) as telefones,
	coalesce(inforh.tamanhoUniformeCamisa, '#') as tamanho_camisa,
	coalesce(inforh.tamanhoUniformeCalca, 0) as tamanho_calca,
	inforh.valorsalario,
	coalesce(cast(inforh.observacao as varchar(144)), '#') as infrh_obs,
	pes.sexo,
	pes.genero,
	pes.datacadastro as data_cadastro,
	um.ativo as umovel_status,
	um.nome as umovel_nome,
	col.situacao as situacao_colaborador,
	p.nome as perfil_usuario,
	case
		when p.tipo = 0 then 'TODOS'
		when p.tipo = 1 then 'ADMINISTRADOR'
		when p.tipo = 2 then 'CONSULTOR'
		when p.tipo = 3 then 'GERENTE'
		when p.tipo = 4 then 'PROFESSOR'
		when p.tipo is null then NULL
		else 'Outros'
	end perfil_tipo
from colaborador col
	inner join pessoa pes on col.pessoa = pes.codigo
	inner join empresa e on col.empresa = e.codigo
	left join colaboradorInfoRH inforh on col.codigo = inforh.colaborador
	left join profissao prof on pes.profissao = prof.codigo
	left join departamento dp on col.departamento = dp.codigo
	left join estado uf on uf.codigo = e.estado
	left join usuariomovel um on um.colaborador = col.codigo
	left join usuario u on u.colaborador = col.codigo
	left join usuarioperfilacesso up on up.usuario = u.codigo
	left join perfilacesso p on p.codigo = up.perfilacesso
	left join cidade cid on cid.codigo = e.cidade;
