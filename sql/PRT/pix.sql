select
p.txid ,
	p.recebedor_chave as <PERSON><PERSON>_<PERSON>,
	emp.nome as empresa,
	p.devedor_nome as No<PERSON>_quem_<PERSON><PERSON><PERSON>,
	p.devedor_cpf as C<PERSON>_quem_<PERSON>go<PERSON>,
	mp.descricao as descricao_parcela,
	mp.valorparcela as valor_parcela,
	p.valor as valor_pix,
	p.status as situacao,
	c.descricao as convenio,
	p.datapagamento
from
	pixmovparcela pmp
inner join movparcela mp on
	mp.codigo = pmp.movparcela
inner join pix p on
	p.codigo = pmp.pix
inner join pessoa pe on
	pe.codigo = p.pessoa
inner join empresa emp on
	emp.codigo = p.empresa
inner join conveniocobranca c on
 c.codigo = p.conveniocobranca
order by
	p.devedor_nome asc