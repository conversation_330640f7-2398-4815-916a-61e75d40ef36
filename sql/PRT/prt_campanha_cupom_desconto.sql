select
    c.id as campanha_id,
    c.redeempresa_id,
    c.desc<PERSON><PERSON><PERSON><PERSON> as campanha_descricao,
    c.vigenciainicial as campanha_vigenciainicial,
    c.vigenciafinal as campanha_vigenciafinal,
    c.nomeprodutopremioportadorcupom as campanha_produtos,
    c.planosqueparticiparaodacampanha as campanha_planos,
    cd.numerocupom as cupom_numero,
    cd.lote as cupom_lote,
    cd.datalancamento as cupom_dt_geracao,
    cd.empresaportadorcupom as cupom_empresa,
    cd.chaveportadorcupom as cupom_portador_chave,
    cd.datapremioportadorcupom as cupom_dt_utilizacao,
    cd.codigoclienteportadorcupom as cupom_portador_cliente,
    cd.nomeportadorcupom as cupom_portador_nome,
    cd.valorpremioportadorcupom as cupom_valor,
    cd.valorpremioprodutosportadorcupom as cupom_valor_produtos,
    cd.valorpremiomensalidadeportadorcupom as cupom_valor_planos
from campanhacupomdesconto c
         left join cupomdesconto cd on cd.campanhacupomdesconto_id  = c.id
         left join historicoutilizacaocupomdesconto hcd on hcd.idcupom = cd.id
         left join redeempresa r on r.id = c.redeempresa_id
--where r.chaverede = '341b908afd7637c1d5b09f248d3498f1'
where r.chaverede = '{0}'