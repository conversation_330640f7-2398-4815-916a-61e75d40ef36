SELECT cli.matricula,
       p.nome,
       pa.datainicioacesso :: date::timestamp AS dataregistro,
        pa.tokengympass             AS token,
       pa.tipogympass              AS tipotoken,
       e.nome                      AS empresa,
       em.email,
       tel.numero                  AS telefone
FROM   cliente cli
           INNER JOIN pessoa p
                      ON p.codigo = cli.pessoa
           INNER JOIN empresa e
                      ON e.codigo = cli.empresa
           INNER JOIN periodoacessocliente pa
                      ON pa.pessoa = cli.pessoa
           LEFT JOIN email em
                     ON em.codigo = (SELECT Max(codigo)
                                     FROM   email
                                     WHERE  pessoa = p.codigo)
           LEFT JOIN telefone tel
                     ON tel.codigo = (SELECT Max(codigo)
                                      FROM   telefone
                                      WHERE  pessoa = p.codigo)
WHERE  Length(Coalesce(pa.tokengympass, '')) > 0
  AND pa.tipoacesso = 'PL'
  AND pa.datainicioacesso :: date > (current_date-1)
       AND Length(Coalesce(e.idexterno, '')) > 0
       AND e.ativa