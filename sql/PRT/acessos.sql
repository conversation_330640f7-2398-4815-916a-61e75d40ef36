select * 
  from (
select
  'aluno'                      as ac_de,
  e.nome                       as e_nome,
  e.cod_empresafinanceiro      as e_empresafinanceiro,
  e.codigo                     as e_codigo,
  pes.nome                     as ac_nomepessoa,
  ac.cliente                   as s_codigocliente,
  ac.codigo                    as ac_codigo,
  ac.dthrentrada               as ac_dthrentrada,
  ac.dthrsaida                 as ac_dthrsaida,
  ac.meioidentificacaoentrada  as ac_identificacao,
  ac.liberacaoacesso           as ac_liberacaoacesso,
  ac.localacesso 	           as ac_localacesso,
  loc.descricao                as ac_localdescricao  
from acessocliente ac
  inner join cliente cli on cli.codigo  = ac.cliente 
  inner join pessoa pes on pes.codigo = cli.pessoa
  inner join empresa e on e.codigo = cli.empresa 
  left join localacesso loc on loc.codigo = ac.localacesso
--
  union
--  
SELECT 
  'colaborador'                as ac_de,
  e.nome                       as e_nome,
  e.cod_empresafinanceiro      as e_empresafinance<PERSON>,
  e.codigo                     as e_codigo,
  pes.nome                     as ac_nomepessoa,
  null                         as s_codigocliente,
  ac.codigo 				   as ac_codigo, 
  ac.dthrentrada 			   as ac_dthrentrada, 
  ac.dthrsaida                 as ac_dthrsaida,
  ac.meioidentificacaoentrada  as ac_identificacao, 
  ac.liberacaoacesso           as ac_liberacaoacesso,
  ac.localacesso 	           as ac_localacesso,
  loc.descricao                as ac_localdescricao
from acessocolaborador ac 
inner join colaborador co on ac.colaborador = co.codigo 
inner join pessoa pes on pes.codigo = co.pessoa 
inner join empresa e on e.codigo = co.empresa
left join localacesso loc on loc.codigo = ac.localacesso
--
) as sql 
 --  
--where sql.ac_de = 'colaborador' 
-- limit 500