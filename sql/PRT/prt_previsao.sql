SELECT *
FROM   (
           SELECT     cl.matricula,
                      pe.nome,
                      regexp_replace(pe.cfp, '[^0-9]+', '', 'g') as cpf,
                      CASE
                          WHEN fp.tipoformapagamento = 'CA' THEN 'Cartao de Credito'
                          WHEN fp.tipoformapagamento = 'CD' THEN 'Cartao de Debito'
                          WHEN fp.tipoformapagamento = 'CH' THEN 'Cheque'
                          WHEN fp.tipoformapagamento = 'AV' THEN 'Dinheiro'
                          WHEN fp.tipoformapagamento = 'CC' THEN 'Conta Corrente'
                          WHEN fp.tipoformapagamento = 'BB' THEN 'Boleto Bancario'
                          WHEN fp.tipoformapagamento = 'PF' THEN 'Dotz'
                          ELSE 'Outro'
                          END          AS tipo,
                      fp.descricao AS formapagamento,
                      rp.contrato,
                      pl.codigo    AS codplano,
                      pl.descricao AS plano,
                      rp.codigo    AS recibo,
                      rp.data,
                      CASE
                          WHEN fp.tipoformapagamento = 'CA' THEN ca.valor::numeric
                                        ELSE mp.valortotal::                             numeric
                             END AS valor,
                             CASE
                                        WHEN fp.tipoformapagamento = 'CA' THEN ca.datacompesancao
                                        ELSE mp.datapagamento
                             END AS datacompensacao,
                             mp.nsu,
                             mp.autorizacaocartao AS autorizacao,
                             op.descricao         AS operadora,
                             u.nome               AS usuario,
                             (
                                       SELECT    pec.nome
                                       FROM      vinculo v
                                       LEFT JOIN colaborador col
                                       ON        col.codigo = v.colaborador
                                       LEFT JOIN pessoa pec
                                       ON        pec.codigo = col.pessoa
                                       WHERE     v.tipovinculo = 'CO'
                                       AND       v.cliente = cl.codigo limit 1) AS consultor,
                             e.nome                                             AS empresa
           FROM       movpagamento mp
               INNER JOIN recibopagamento rp
           ON         mp.recibopagamento = rp.codigo
               INNER JOIN empresa e
               ON         e.codigo = mp.empresa
               LEFT JOIN  cartaocredito ca
               ON         ca.movpagamento = mp.codigo
               LEFT JOIN  operadoracartao op
               ON         op.codigo = mp.operadoracartao
               INNER JOIN formapagamento fp
               ON         fp.codigo = mp.formapagamento
               INNER JOIN usuario u
               ON         u.codigo = rp.responsavellancamento
               INNER JOIN pessoa pe
               ON         pe.codigo = rp.pessoapagador
               LEFT JOIN  cliente cl
               ON         cl.pessoa = pe.codigo
               LEFT JOIN  contrato co
               ON         co.codigo = rp.contrato
               LEFT JOIN  plano pl
               ON         pl.codigo = co.plano
           WHERE      fp.tipoformapagamento NOT IN ('PF',
               'CC')
             AND        mp.credito = false
             AND        length(COALESCE(e.idexterno,'')) > 0
             AND        e.ativa) AS sql
WHERE  sql.valor > 0