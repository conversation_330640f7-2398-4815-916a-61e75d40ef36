SELECT   sql.matricula,
         sql.nome,
         sql.tipo,
         sql.venda<PERSON><PERSON><PERSON>,
         sql.contrato,
         sql.sit<PERSON><PERSON><PERSON><PERSON><PERSON>,
         sql.du<PERSON><PERSON>,
         sql.codp<PERSON>o,
         sql.plano,
         sql.produto,
         sql.data,
         Sum(sql.valor) AS valor,
         CASE
             WHEN sql.tipo = 'MANUTENCAO MODALIDADE' THEN 1
             ELSE
                 (
                     SELECT count(*)
                     FROM   movparcela
                     WHERE  (
                                 contrato = sql.contrato
                             OR     vendaavulsa = sql.vendaavulsa)
                       AND    situacao = 'PG'
                       AND    valorparcela > 0)
             END AS qtdparcelaspagas,
         CASE
             WHEN sql.tipo = 'MANUTENCAO MODALIDADE' THEN 1
             ELSE
                 (
                     SELECT count(*)
                     FROM   movparcela
                     WHERE  (
                                 contrato = sql.contrato
                             OR     vendaavulsa = sql.vendaavulsa)
                       AND    situacao NOT IN ('RG',
                                               'CA')
                       AND    valorparcela > 0)
             END AS qtdtotal<PERSON><PERSON><PERSON>,
         sql.usuario,
         sql.consultor,
         sql.empresa
FROM     (
             SELECT
                 CASE
                     WHEN cl.codigo IS NOT NULL THEN cl.matricula
                     WHEN co.codigo IS NOT NULL THEN 'COLABORADOR'
                     ELSE ''
                     END AS matricula,
                 pe.nome,
                 CASE
                     WHEN mp.contrato IS NOT NULL
                         AND        pr.tipoproduto <> 'MM' THEN 'CONTRATO'
                     WHEN mp.contrato IS NOT NULL
                         AND        pr.tipoproduto = 'MM' THEN 'MANUTENCAO MODALIDADE'
                     WHEN mp.vendaavulsa IS NOT NULL THEN 'VENDA AVULSA'
                     ELSE ''
                     END AS tipo,
                 mp.vendaavulsa,
                 mp.contrato,
                 CASE
                     WHEN mp.contrato IS NOT NULL
                         AND        con.situacaocontrato = 'RN' THEN 'RENOVACAO'
                     WHEN mp.contrato IS NOT NULL
                         AND        con.situacaocontrato = 'MA' THEN 'MATRICULA'
                     WHEN mp.contrato IS NOT NULL
                         AND        con.situacaocontrato = 'RE' THEN 'REMATRICULA'
                     ELSE ''
                     END                                AS situacaocontrato,
                 cd.numeromeses                     AS duracao,
                 pl.codigo                          AS codplano,
                 pl.descricao                       AS plano,
                 pr.descricao                       AS produto,
                 mp.datalancamento::date::timestamp AS data,
                     mp.situacao                        AS situacaoproduto,
                 mp.valorfaturado::numeric          AS valor,
                     pr.tipoproduto,
                 u.nome AS usuario,
                 (
                     SELECT    pec.nome
                     FROM      vinculo v
                                   LEFT JOIN colaborador col
                                             ON        col.codigo = v.colaborador
                                   LEFT JOIN pessoa pec
                                             ON        pec.codigo = col.pessoa
                     WHERE     v.tipovinculo = 'CO'
                       AND       v.cliente = cl.codigo limit 1) AS consultor,
         e.nome                                             AS empresa
    FROM       movproduto mp
                    INNER JOIN usuario u
ON         u.codigo = mp.responsavellancamento
    INNER JOIN pessoa pe
    ON         pe.codigo = mp.pessoa
    INNER JOIN produto pr
    ON         pr.codigo = mp.produto
    LEFT JOIN  cliente cl
    ON         cl.pessoa = pe.codigo
    LEFT JOIN  colaborador co
    ON         co.pessoa = pe.codigo
    LEFT JOIN  vendaavulsa va
    ON         va.codigo = mp.vendaavulsa
    LEFT JOIN  contrato con
    ON         con.codigo = mp.contrato
    LEFT JOIN  contratoduracao cd
    ON         cd.contrato = con.codigo
    LEFT JOIN  plano pl
    ON         pl.codigo = con.plano
    INNER JOIN empresa e
    ON         e.codigo = mp.empresa
WHERE      mp.situacao NOT IN ('CA',
    '')
  AND        pr.tipoproduto NOT IN ('CC',
    'MC')
  AND        length(COALESCE(e.idexterno,'')) > 0
  AND        e.ativa) AS sql
GROUP BY sql.matricula,
    sql.nome,
    sql.tipo,
    sql.vendaavulsa,
    sql.contrato,
    sql.situacaocontrato,
    sql.duracao,
    sql.codplano,
    sql.plano,
    sql.produto,
    sql.data,
    sql.usuario,
    sql.consultor,
    sql.empresa