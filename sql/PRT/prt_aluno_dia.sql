select
    sw.matricula,
    sw.nomecliente AS nome,
    regexp_replace(p.cfp, '[^0-9]+', '', 'g') as cpf,
    t.numero as telefone,
    em.email,
    CASE
        WHEN sw.situacao = 'IN' THEN 'INATIVO'
        WHEN sw.situacao = 'AT' THEN 'ATIVO'
        WHEN sw.situacao = 'VI' THEN 'VISITANTE'
        WHEN sw.situacao = 'TR' THEN 'TRANCADO'
        ELSE ''
        END AS situacao,
    sw.datanascimento::date::timestamp as datanasc,
        e.idexterno as idexternoempresa,
    sw.datamatricula,
    pl.descricao as plano,
    c.vigenciade  as inicio,
    c.vigenciaateajustada as fim,
    array_to_string(array (
                        SELECT mo.nome
                  FROM   contratomodalidade cm
                  inner join modalidade mo on mo.codigo = cm.modalidade
                  WHERE  cm.contrato = c.codigo), ' -- ', '') AS modalidades,
    pcol.nome as consultor
from situacaoclientesinteticodw sw
         inner join pessoa p on p.codigo = sw.codigopessoa
         inner join empresa e on e.codigo = sw.empresacliente
         left join telefone t on t.codigo = (select max(codigo) from telefone where pessoa = p.codigo)
         left join email em on em.codigo = (select max(codigo) from email where pessoa = p.codigo)
         left join contrato c on c.codigo = sw.codigocontrato
         left join plano pl on pl.codigo = c.plano
         left join vinculo vic on vic.codigo = (select max(codigo) from vinculo where cliente = sw.codigocliente and tipovinculo = 'CO')
         left join colaborador col on col.codigo = vic.colaborador
         left join pessoa pcol on pcol.codigo = col.pessoa
WHERE length(COALESCE(e.idexterno,'')) > 0
  and e.ativa
  and sw.datamatricula::date = current_date