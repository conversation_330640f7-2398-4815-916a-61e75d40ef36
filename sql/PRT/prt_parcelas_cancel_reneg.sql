select
    p.nome,
    mp.codigo,
    mp.descricao,
    mp.situacao,
    coalesce(o.dataoperacao,
             l.dataalteracao) as dataAlteracao,
    coalesce(o.justificativa,
             '') as justificativa,
    coalesce(o.usua<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
             l.responsavelaltera<PERSON>o) as responsavelOperacao,
    emp.nome as empresa,
    l.valorcampoanterior as renegociacaoAnterior,
    l.v<PERSON>rcampoalterado as renegociacaoAlterado
from
    movparcela mp
        inner join pessoa p on p.codigo = mp.pessoa
        inner join empresa emp on emp.codigo = mp.empresa
        left join observacaooperacao o on
        o.movparcela = mp.codigo
        left join log l on
        l.operacao = 'RENEGOCIAÇÃO - PARCELA'
            and l.valorcampoanterior like '%' || mp.codigo || '%'
where
    mp.situacao in ('CA', 'RG')