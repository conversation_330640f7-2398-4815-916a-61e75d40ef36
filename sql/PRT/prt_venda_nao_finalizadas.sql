SELECT DISTINCT cli.matricula,
                p.nome,
                log.dataalteracao::date::timestamp AS data,
        array_to_string(array
                        (
                            SELECT numero
                       FROM   telefone
                       WHERE  telefone.pessoa = p.codigo), ' - ', '') AS telefones,
                array_to_string(array
                                (
                                    SELECT email
                       FROM   email
                       WHERE  email.pessoa = p.codigo), '; ', '') AS emails,
                log.valorcampoalterado                            AS mensagem,
                e.nome                                            AS empresa
FROM            cliente cli
                    INNER JOIN      empresa e
                                    ON              e.codigo = cli.empresa
                    INNER JOIN      pessoa p
                                    ON              cli.pessoa = p.codigo
                    INNER JOIN      log
                                    ON              cast(log.chaveprimaria AS integer) = cli.codigo
WHERE           operacao = 'ESTORNO_COBRANCA_NAO_AUTORIZADA'
  AND             cli.situacao = 'VI'
  AND             length(COALESCE(e.idexterno,'')) > 0
  AND             e.ativa