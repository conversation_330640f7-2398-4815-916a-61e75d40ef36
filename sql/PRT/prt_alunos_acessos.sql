WITH emails_aggregated AS (
    SELECT
        c.codigo,
        array_to_string(array_agg(DISTINCT lower(em.email)), ',') AS emails
    FROM cliente c
             INNER JOIN email em ON em.pessoa = c.pessoa
    GROUP BY c.codigo
),
     primeiros_acessos AS (
         SELECT *
         FROM (
                  SELECT
                      ac.*,
                      ROW_NUMBER() OVER (
                PARTITION BY ac.cliente, DATE(ac.dataregistro), ac.nomecodempresaacessou
                ORDER BY ac.dataregistro ASC
            ) AS rn
                  FROM acessocliente ac
                  WHERE
                      ac.nomecodempresaacessou IS NOT NULL
                    AND ac.dataregistro IS NOT NULL
                    AND ac.dataregistro >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months'
              ) sub
         WHERE rn = 1
     )
SELECT
    c.matricula,
    p.nome,
    ea.emails,
    e.idexterno,
    e.nome AS empresa_origem,
    EXTRACT(MONTH FROM ac.dataregistro) AS mes_acesso,
    ac.nomecodempresaacessou AS empresa_acesso,
    COUNT(ac.nomecodempresaacessou) AS qtd_acesso
FROM primeiros_acessos ac
         INNER JOIN cliente c ON c.codigo = ac.cliente
         INNER JOIN pessoa p ON p.codigo = c.pessoa
         INNER JOIN empresa e ON e.codigo = c.empresa
         LEFT JOIN emails_aggregated ea ON ea.codigo = c.codigo
GROUP BY
    c.matricula,
    p.nome,
    ea.emails,
    e.idexterno,
    e.nome,
    EXTRACT(MONTH FROM ac.dataregistro),
    ac.nomecodempresaacessou;