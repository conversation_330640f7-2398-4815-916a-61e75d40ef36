select
    emp.nome as empresa,
    cc.descricao as nomeConvenio,
    (case
         when cc.situacao = 1 then 'ATIVO'
         else 'INATIVO'
        end) as situacaoConvenio,
    ccr.descricao as nomeRateio,
    p.descricao as nomeProdutoRateio,
    ccr.tipoproduto as tipoProdutoRateio,
    ccr.padrao,
    ccri.nomerecebedor as rateio,
    ccri.porcentagem as porcentagemRateio
from
    conveniocobrancarateio ccr
        inner join empresa emp on
        emp.codigo = ccr.empresa
        inner join conveniocobranca cc on
        cc.codigo = ccr.conveniocobranca
        left join produto p on
        p.codigo = ccr.produto
        inner join conveniocobrancarateioitem ccri on
        ccri.conveniocobrancarateio = ccr.codigo