select
    cc.descricao as convenio,
    tcc.tipo_cobranca as tipoConvenio,
    (case
         when (cc.situacao = 0) then 'INATIVO'
         else 'ATIVO'
        end) as situacao,
    cc.pix_chave,
    cc.pix_client_id,
    cc.pix_client_secret,
    emp.nome as empresa
from
    conveniocobranca cc
        inner join tipoconveniocobranca tcc on
        tcc.codigo = cc.tipoconvenio
        inner join conveniocobrancaempresa cce on
        cce.conveniocobranca = cc.codigo
        inner join empresa emp on
        emp.codigo = cce.empresa
where
    upper(tcc.tipo_cobranca) = 'PIX'