SELECT
    CASE
        WHEN cl.codigo IS NOT NULL THEN cl.matricula
        WHEN co.codigo IS NOT NULL THEN 'COLABORADOR'
        ELSE ''
        END AS matricula,
    pe.nome,
    CASE
        WHEN sw.situacao = 'IN' THEN 'INATIVO'
        WHEN sw.situacao = 'AT' THEN 'ATIVO'
        WHEN sw.situacao = 'VI' THEN 'VISITANTE'
        WHEN sw.situacao = 'TR' THEN 'TRANCADO'
        ELSE sw.situacao
        END AS situacao,
    array_to_string(array
                    (
                        select email
                  FROM   email
                  WHERE  email.pessoa = pe.codigo), '; ', '') AS emails,
    array_to_string(array
                    (
                        SELECT numero
                  FROM   telefone
                  WHERE  telefone.pessoa = pe.codigo), ' - ', '') AS telefones,
    mp.vendaavulsa,
    mp.contrato,
    pl.codigo              AS codplano,
    pl.descricao           AS plano,
    mp.dataregistro::  date::timestamp AS datalancamento,
        mp.datavencimento::date::timestamp AS datavencimento,
        mp.codigo               AS codparcela,
    mp.descricao            AS parcela,
    mp.valorparcela         AS valor,
    u.nome                  AS usuario,
    e.nome                  AS empresa,
    e.idexterno             AS id_empresa,
    (
        SELECT t.codigoretornodescricao
        FROM   transacao t
        WHERE  t.codigo IN
               (
                   SELECT     max(t.codigo)
                   FROM       transacao t
                                  INNER JOIN transacaomovparcela tm
                                             ON         tm.transacao = t.codigo
                   WHERE      tm.movparcela = mp.codigo)) AS retorno_cobranca,
    CASE
        WHEN cl.codigo IS NOT NULL THEN ('https://vendas.online.sistemapacto.com.br/pagamento?un='
            ||mp.empresa
            ||'&k='
            ||
                                         (
                                             SELECT   chave
                                             FROM     dadosgerencialpmg d
                                             ORDER BY codigo DESC limit 1)
            || '&cliente='
            || cl.matricula
            || '')
                      ELSE ''
END AS link_pagamento,
regexp_replace(pe.cfp, '[^0-9]+', '', 'g') as cpf
FROM       movparcela mp
INNER JOIN usuario u
ON         u.codigo = mp.responsavel
INNER JOIN pessoa pe
ON         pe.codigo = mp.pessoa
LEFT JOIN  situacaoclientesinteticodw sw
ON         sw.codigopessoa = pe.codigo
LEFT JOIN  cliente cl
ON         cl.pessoa = pe.codigo
LEFT JOIN  colaborador co
ON         co.pessoa = pe.codigo
LEFT JOIN  contrato con
ON         con.codigo = mp.contrato
LEFT JOIN  plano pl
ON         pl.codigo = con.plano
INNER JOIN empresa e
ON         e.codigo = mp.empresa
WHERE      mp.situacao IN ('EA')
AND        mp.datavencimento::date < CURRENT_DATE
AND        length(COALESCE(e.idexterno,'')) > 0
AND        e.ativa
ORDER BY   pe.nome,
           mp.datavencimento