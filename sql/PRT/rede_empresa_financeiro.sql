select
	r.nome as rede_nome,
	r.id as rede_id,
	ef.chavezw,
	ef.empresazw,
	ef.cnpj,
	ef.razaosocial,
	ef.nomefantasia,
	ef.nomere<PERSON><PERSON>,
	ef.identificador,
    ef.nomeempresaz<PERSON>,	
	ef.endere<PERSON>,
	ef.bairro,
	ef.cidade,
	ef.estado,
	ef.pais,
	ef.codigofinanceiro,
	ef.email,	
	ef.codigorede ,
	ef.tipoempresa, 
	ef.grupofavorecido,
	ef.tipogrupofavorecido,
	ef.gestor,
	ef.responsavelfinanceiro,
    ef.permitevendanosite,
    ef.ultimaatualizacao,
	ef.datadesativacao,
	ef.datasuspensaoempresazw,	
	ef.metragem,
	ef.nicho,
	CASE
	  WHEN nicho = 'AFSH' 	THEN 'Academia Full Service High Cost'
	  WHEN nicho = 'AFSLC' 	THEN 'Academia Full Service Low Cost'
	  WHEN nicho = 'AN'		THEN 'Academia de Natação'
	  WHEN nicho = 'AL' 	THEN 'Academia de Lutas'  
	  WHEN nicho = 'BCF' 	THEN 'Box Cross / Funcional'  
	  WHEN nicho = 'ST' 	THEN 'Studios'  
	  WHEN nicho = 'EQA' 	THEN 'Esportes de Quadra e Areia'
	  WHEN nicho = 'AU'		THEN 'Autarquias'
	  WHEN nicho = 'ACP'	THEN 'Associações / Clube Particular'
	  ELSE nicho
	end as nicho_Descricao,
	de.inicioimplantacao, 
	de.inicioproducao, 
	de.finalimplantacao,
	de.tipocliente
from
	redeempresa r
inner join empresafinanceiro ef on 	ef.redeempresa_id = r.id
left join detalheempresa de on ef.detalheempresa_codigo = de.codigo 
-- where r.chaverede = 'fd10e19d777ffea95ae185d53fb6c10c'
where r.chaverede = '{0}'
  /*and ( ef.datasuspensaoempresazw is null or ef.datasuspensaoempresazw > current_date )
  and ( ef.datadesativacao is null or ef.datadesativacao > current_date )*/
--  and ( de.inicioproducao is not null and de.inicioproducao < current_date )
order by r.nome

	