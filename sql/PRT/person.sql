
select 
       e.cod_empresaf<PERSON><PERSON><PERSON>,
       s.codigo,
       s.dia,
       s.codigo<PERSON><PERSON>e,
       s.matricula,
       s.nome<PERSON>liente,
       s.datanas<PERSON>mento,
       s.idade,
       replace(s.profissao, 'X', '')                                                                        as profissa<PERSON>,
       s.colaboradores,
       s.codigo<PERSON>tra<PERSON>,
       s.mnemonicocontrato,
       s.saldo<PERSON>tacorrentecliente,
       s.datavigenciade,
       s.datavigenciaate,
       s.datavigenciaateajustada,
       s.datalancamentocontrato,
       s.datarenovacaocontrato,
       s.datarematriculacontrato,
       s.dataultimobv,
       s.datamatricula,
       s.dataultimarematricula,
       s.diasassiduidadeultrematriculaatehoje,
       s.diasacessosemanapassada,
       s.dataultimo<PERSON>sso,
       s.faseatualcrm,
       s.dataultimocontatocrm,
       s.responsavelultimocontatocrm,
       s.codigoultimocontatocrm,
       s.tip<PERSON>,
       s.data<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
       s.data<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
       s.diasacessosemana2,
       s.diasacessosemana3,
       s.diasacessosemana4,
       s.ve<PERSON><PERSON><PERSON><PERSON>,
       s.diasacessoultimo<PERSON>,
       s.diasacessomes2,
       s.diasacessomes3,
       s.diasacessomes4,
       s.mediadiasacesso4meses,
       s.telcelcolab,
       s.pesorisco,
       s.enviosmsmarcadoclassif,
       s.smsrisco,
       e.cod_empresafinanceiro || '-' || s.codigopessoa                                                     as id_pessoa,
       s.codigousuariomovel,
       s.empresacliente,
       s.sexocliente,
       s.telefonescliente,
       s.nomeconsulta,
       s.cpf,
       s.codacessocliente,
       s.modalidades,
       s.frequenciasemanal,
       s.saldocreditotreino,
       s.validarsaldocreditotreino,
       s.quantidadediasextra,
       s.nraulaexperimental,
       s.totalcreditotreino,
       s.descricoesmodalidades,
       s.crossfit,
       s.datasaidaacesso,
       s.statusbg,
       s.datacadastro,
       s.existeparcvencidacontrato,
       s.empresausafreepass,
       s.ultimavisita,
       replace(s.cargo, 'X', '')                                                                            as cargo,
       s.freepass,
       replace(s.endereco, '"', '')                                                                         as endereco,
       s.cidade,
       s.bairro,
        CASE
           WHEN s.estadocivil = 'C' THEN 'Casado(a)'
           WHEN s.estadocivil = 'A' THEN 'Amasiado(a)'
           WHEN s.estadocivil = 'D' THEN 'Divorciado(a)'
           WHEN s.estadocivil = 'S' THEN 'Solteiro(a)'
           WHEN s.estadocivil = 'U' THEN 'União Estavel'
           WHEN s.estadocivil = 'P' THEN 'Separado(a)'
           WHEN s.estadocivil = 'V' THEN 'Viuvo(a)'
           ELSE '?'
           END                                                                                              as estadocivil,
       s.rg,
       s.uf,
       s.telefonesconsulta,
       s.cpfconsulta,
       cli.titularplanocompartilhado,
       e.cod_empresafinanceiro || '-' || qc.evento                                                          as id_evento,
       coalesce(ev.descricao, 'SEM EVENTO')                                                                 as descricao_evento,
       CASE
           WHEN qc.origemsistema = 1 THEN 'ZillyonWeb'
           WHEN qc.origemsistema = 2 THEN 'Agenda Web'
           WHEN qc.origemsistema = 3 THEN 'Pacto Treino'
           WHEN qc.origemsistema = 4 THEN 'App Treino'
           WHEN qc.origemsistema = 5 THEN 'App Professor'
           WHEN qc.origemsistema = 6 THEN 'Autoatendimento'
           WHEN qc.origemsistema = 7 THEN 'Site Vendas'
           WHEN qc.origemsistema = 8 THEN 'Buzz Lead'
           WHEN qc.origemsistema = 9 THEN 'Vendas 2.0'
           WHEN qc.origemsistema = 10 THEN 'App do consultor'
           WHEN qc.origemsistema = 11 THEN 'Booking Gympass'
           WHEN qc.origemsistema = 12 THEN 'Fila de espera'
           WHEN qc.origemsistema = 13 THEN 'Importação API'
           ELSE 'BV não encontrato'
           END                                                                                              as origemsistema,
       qc.data                                                                                              as data_bv_matricula,
       con.bolsa                                                                                            as contrato_bolsa,
--
       case when cli.titularplanocompartilhado is null then s.situacao else s2.situacao end                 as situacao,
       case
           when cli.titularplanocompartilhado is null then s.situacaocontrato
           else s2.situacaocontrato end                                                                     as situacaocontrato,
       case
           when cli.titularplanocompartilhado is null then s.situacaomatriculacontrato
           else s2.situacaomatriculacontrato end                                                            as situacaomatriculacontrato,
       case
           when cli.titularplanocompartilhado is null then s.situacaocontratooperacao
           else s2.situacaocontratooperacao end                                                             as situacaocontratooperacao,
       case
           when cli.titularplanocompartilhado is null then s.nomeplano
           else s2.nomeplano end                                                                            as nomeplano,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null))
               then (s.valorfaturadocontrato / (QtdeVinculos+1))
           when cli.titularplanocompartilhado is not null then (s2.valorfaturadocontrato / (QtdeVinculos+1))
           else s.valorfaturadocontrato end                                                                 as valorfaturadocontrato,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null))
               then (s.valorpagocontrato / (QtdeVinculos+1))
           when cli.titularplanocompartilhado is not null then (s2.valorpagocontrato / (QtdeVinculos+1))
           else s.valorpagocontrato end                                                                     as valorpagocontrato,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null))
               then (s.valorparcabertocontrato / (QtdeVinculos+1))
           when cli.titularplanocompartilhado is not null then (s2.valorparcabertocontrato / (QtdeVinculos+1))
           else s.valorparcabertocontrato end                                                               as valorparcabertocontrato,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null))
               then s.duracaocontratomeses
           when cli.titularplanocompartilhado is not null then s2.duracaocontratomeses
           else s.duracaocontratomeses end                                                                  as duracaocontratomeses,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null)) then 'Titular'
           when cli.titularplanocompartilhado is not null then 'Dependente'
           else 'Sem Vinculo'
           end                                                                                              as Vinculo,
--
       case
           when ((cli.titularplanocompartilhado is null) and (s3.codigoSeTitular is not null)) then s3.codigoSeTitular
           when cli.titularplanocompartilhado is not null then cli.titularplanocompartilhado
           else null
           end                                                                                              as CodigoVinculado,
       s.valorfaturadocontrato                                                                              as valorfaturadocontrato_titular,
       s3.codigoSeTitular,
       s3.QtdeVinculos,
       p2.descricao                                                                                         as produto_plano,
       array_to_string(array(SELECT e.email FROM email e WHERE e.pessoa = coalesce(s.codigopessoa,0)), ',', '') as emails
from situacaoclientesinteticodw s
         inner join empresa e on e.codigo = s.empresacliente
         inner join cliente cli ON s.codigocliente = cli.codigo
         left join questionariocliente qc ON cli.codigo = qc.cliente and qc.tipobv = 1
         left join evento ev on qc.evento = ev.codigo
    -- Para pegar os dados do titular de um dependentes --------------------------------------------------------------
         left join situacaoclientesinteticodw s2 on ((cli.titularplanocompartilhado is not null) and
                                                     (cli.titularplanocompartilhado = s2.codigocliente))
    -- Para descobrir se este cliente é titular e de quantas pessoas -------------------------------------------------
         left join (select titularplanocompartilhado as codigoSeTitular, count(*) as QtdeVinculos
                   from cliente
                    where titularplanocompartilhado > 0 group by 1 ) s3 on s3.codigoSeTitular = s.codigocliente
    -- Para pegar dados do contrato e do Produto do plano -------------------------------------------------------------
         left join contrato con on con.codigo = coalesce(s2.codigocontrato,s.codigocontrato)
         left join plano p on p.codigo  = con.plano 
         left join produto p2 on p2.codigo = p.produtopadraogerarparcelascontrato
-- where s.codigopessoa in (72940,82979,96843,82681)       
-- where s.codigopessoa in (72940)       
-- 
-- 
--select c.nomesocial,c.pessoa ,c.codigomatricula , c.codigo , c.titularplanocompartilhado 
--from cliente c
--where titularplanocompartilhado > 0
--  and titularplanocompartilhado = 56479 
--and c.pessoa in (72940,82979,96843) 
-- and titularplanocompartilhado in ( 56479, 66518, 80382 )
