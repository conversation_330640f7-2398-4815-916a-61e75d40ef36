SELECT
    cl.matricula,
    p.nome,
    regexp_replace(p.cfp, '[^0-9]+', '', 'g') as cpf,
    mp.codigo as parcela_codigo,
    mp.descricao as parcela_descricao,
    mp.valorparcela as parcela_valor,
    mp.datavencimento::date::timestamp as parcela_vencimento
from movparcela mp
         inner join pessoa p on p.codigo = mp.pessoa
         left join cliente cl on cl.pessoa = p.codigo
         inner join empresa e on e.codigo = cl.empresa
where length(COALESCE(e.idexterno,'')) > 0
  and e.ativa
  and mp.situacao = 'EA'