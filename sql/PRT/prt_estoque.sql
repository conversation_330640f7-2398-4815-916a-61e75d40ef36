SELECT     e.idexterno      AS empresa_idexterno,
           e.codigo         AS empresa_codigo,
           e.nome           AS empresa_nome,
           p.codigo         AS produto_codigo,
           p.descricao      AS produto_nome,
           pe.estoque       AS estoque_atual,
           pe.estoqueminimo AS estoque_minimo,
           (
               SELECT Sum(quantidade)
               FROM   movproduto
               WHERE  empresa = e.codigo
                 AND    produto = p.codigo
                 AND    situacao NOT IN ('CA')
                 AND    datalancamento::date >= (date_trunc('month', CURRENT_DATE) - interval '1 month')::date
    AND    datalancamento::date <= (date_trunc('month', CURRENT_DATE) - interval '1 day')::date ) AS vendas_mes_anterior,
           (
                  SELECT sum(quantidade)
                  FROM   movproduto
                  WHERE  empresa = e.codigo
                  AND    produto = p.codigo
                  AND    situacao NOT IN ('CA')
                  AND    datalancamento::date >= date_trunc('month', CURRENT_DATE)::date) AS vendas_mes_atual,
           (
                  SELECT sum(quantidade)
                  FROM   movproduto
                  WHERE  empresa = e.codigo
                  AND    produto = p.codigo
                  AND    situacao NOT IN ('CA')
                  AND    datalancamento::date = (CURRENT_DATE - interval '1 day')::date) AS vendas_ontem,
           (
                  SELECT sum(quantidade)
                  FROM   movproduto
                  WHERE  empresa = e.codigo
                  AND    produto = p.codigo
                  AND    situacao NOT IN ('CA')
                  AND    datalancamento::date = CURRENT_DATE) AS vendas_hoje
FROM       produtoestoque pe
    INNER JOIN produto p
ON         p.codigo = pe.produto
    INNER JOIN empresa e
    ON         e.codigo = pe.empresa
WHERE      p.desativado = false
  AND        length(COALESCE(e.idexterno,'')) > 0
  AND        e.ativa