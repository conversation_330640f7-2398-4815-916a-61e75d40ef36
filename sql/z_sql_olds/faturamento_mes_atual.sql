select
	distinct r.codigo as "cod_recibo",
	e.nome as unidade,
	e.codigo as empresazw,
	e.cod_empresafinanceiro,
	'CLIENTE' as tipo,
	substring(		trim(mp.nomepagador)		from			'^([^ ]+)'	) as nome,
	substring(		trim(mp.nomepagador)		from			'([^ ]+)$'	) as sobrenome,
	pl.descricao || ' - ' || m.descricao || ' - Venc: ' || cast(m.datavencimento as date) as "item",
	mpr.quantidade as "quantidade",
	pmp.valorpago as "valor",
	r."data" as "data_da_venda",
	m.datavencimento,
	f.descricao as "forma_de_pagamento",
	u.nome as "comissao_do_colaborador",
	pr.descricao as "familia_contrato",
	case
		when pr.tipoproduto = 'MA' then 'Matrícula'
		when pr.tipoproduto = 'RE' then 'Rematrícula'
		when pr.tipoproduto = 'RN' then 'Renovação'
		when pr.tipoproduto = 'PE' then 'Produto Estoque"'
		when pr.tipoproduto = 'PM' then 'Contrato'
		when pr.tipoproduto = 'SE' then 'Serviço'
		when pr.tipoproduto = 'CD' then 'Convênio de Desconto'
		when pr.tipoproduto = 'DE' then 'Desconto'
		when pr.tipoproduto = 'DV' then 'Devolução'
		when pr.tipoproduto = 'TR' then 'Trancamento'
		when pr.tipoproduto = 'RT' then 'Retorno Trancamento'
		when pr.tipoproduto = 'AA' then 'Aula Avulsa'
		when pr.tipoproduto = 'DI' then 'Diária'
		when pr.tipoproduto = 'FR' then 'FreePass'
		when pr.tipoproduto = 'AH' then 'Alterar - Horário'
		when pr.tipoproduto = 'MM' then 'Manutenção Modalidade'
		when pr.tipoproduto = 'MC' then 'Manutenção Conta Corrente'
		when pr.tipoproduto = 'DR' then 'Desconto em Renovação Antecipada'
		when pr.tipoproduto = 'TP' then 'Taxa de Personal'
		when pr.tipoproduto = 'SS' then 'Sessão'
		when pr.tipoproduto = 'DC' then 'Devolução de crédito de conta corrente do cliente'
		when pr.tipoproduto = 'AT' then 'Atestado'
		when pr.tipoproduto = 'TD' then 'Taxa de Adesão Plano Recorrência'
		when pr.tipoproduto = 'TN' then 'Taxa de Renegociação'
		when pr.tipoproduto = 'CP' then 'Crédito de personal'
		when pr.tipoproduto = 'TA' then 'Taxa de Anuidade Plano Recorrência'
		when pr.tipoproduto = 'RD' then 'Devolução de recebíveis'
		when pr.tipoproduto = 'CC' then 'epósito conta corrente do aluno'
		when pr.tipoproduto = 'AC' then 'Acerto conta corrente do aluno'
		when pr.tipoproduto = 'QU' then 'Quitação de dinheiro - Cancelamento'
		when pr.tipoproduto = 'AR' then 'Armário'
		when pr.tipoproduto = 'MJ' then 'Multa e Juros'
		when pr.tipoproduto = 'CH' then 'Cheques devolvidos'
		else 'Outro'
	end as "produto",
	case
		when extract(dow	from		r."data") = 0 then 'Domingo'
		when extract(dow	from		r."data") = 1 then 'Segunda-feira'
		when extract(dow	from		r."data") = 2 then 'Terça-feira'
		when extract(dow	from		r."data") = 3 then 'Quarta-feira'
		when extract(dow	from		r."data") = 4 then 'Quinta-feira'
		when extract(dow	from		r."data") = 5 then 'Sexta-feira'
		when extract(dow	from		r."data") = 6 then 'Sábado'
	end as "dia_da_semana",
	mp.pessoa 
from
	movpagamento mp
inner join recibopagamento r 		on	mp.recibopagamento = r.codigo
inner join empresa e 				on	r.empresa = e.codigo
inner join pagamentomovparcela pmp 	on	pmp.movpagamento = mp.codigo
inner join movparcela m 			on	m.codigo = pmp.movparcela
inner join movprodutoparcela mpc 	on	mpc.movparcela = pmp.movparcela
inner join movproduto mpr 			on	mpr.codigo = mpc.movproduto
inner join usuario u 				on	u.codigo = r.responsavellancamento
inner join formapagamento f 		on	f.codigo = mp.formapagamento
inner join produto pr 				on	pr.codigo = mpr.produto
left  join contrato ct 				on	ct.codigo = m.contrato
left  join plano pl 				on 	pl.codigo = ct.plano
where
   -- Ultimos 30 dias 
      cast(r."data" as date) >= ( current_date - interval '30 day')
 -- and r.codigo = 4613 
order by
	r.codigo