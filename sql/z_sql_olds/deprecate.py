

# util.generate_data_all_zones('movparcela', date_cols=['datavencimento', 'dataregistro', 'inicio_contrato', 'final_contrato', 'lancamento_contrato'], 
#                              dict_other_types={
#                                'id_contrato': 'str',
#                                'id_empresa': 'str',
#                                'id_parcela': 'str',
#                                'id_pessoa': 'str'
#                                })



# util.generate_data_all_zones('tpv', date_cols=['data_compensacao', 'data_compensacao_original', 'data_faturamento'],                              
#                              numeric_cols=['nr_vezes_zw', 'cliente_contrato', 'cliente_pessoa'])


# util.generate_data_all_zones('vendas_detalhadas', date_cols=['data_da_venda', 'mes_referencia', 'dataconsulta'])

# util.generate_data_all_zones('movparcela', date_cols=['datavencimento', 'dataregistro', 'inicio_contrato', 'final_contrato', 'lancamento_contrato'])
