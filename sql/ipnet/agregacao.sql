SELECT
  C.codigo as contrato_codigo,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  C.<PERSON>,
  C.<PERSON>,
  C.<PERSON>,
  <PERSON><PERSON>,
  C.<PERSON>,
  C.<PERSON>,
  CD.codigo as contratoduracao_codigo,  
  CD.numeromeses,
  CD.valordesejadomensal,
  CD.quantidadediasextra,
  CO.codigo as contratooperacao_codigo,  
  CO.tipooperacao,
  CO.operacaopaga,
  CO.tipojustificativa,
  CO.origemsistema,
  CM.codigo as contratomodalidade_codigo,  
  CM.modalidade,
  CM.vezessemana,
  CM.valorfinalmodalidade,
  CM.valormodalidade,
  M.codigo as modalidade_codigo,
  <PERSON><PERSON>no<PERSON>,
  M<PERSON>,
  M.<PERSON>,
  M<PERSON>til<PERSON>,
  M.utiliza<PERSON>,
  M<PERSON>,
  TC.codigo as trancamentocontrato_codigo,  
  TC.datafim<PERSON>,
  T<PERSON>.data<PERSON>,
  <PERSON><PERSON><PERSON>data<PERSON>,
  <PERSON><PERSON><PERSON>,
  TC<PERSON>v<PERSON>,
  T<PERSON><PERSON>v<PERSON>,
  AC.cliente,
  AC.codigo as acessocliente_codigo,
  AC.dthrentrada,
  AC.sentido,
  S.codigo as situacaoclientesinteticodw_codigo,
  S.codigocliente,
  S.codigocontrato,
  S.codigopessoa,
  S.sexocliente,
  S.datanascimento,
  S.valorfaturadocontrato,
  S.valorpagocontrato,
  S.valorparcabertocontrato
from
  situacaoclientesinteticodw S
  INNER JOIN contrato AS C ON C.pessoa = S.codigopessoa
  LEFT JOIN contratoduracao CD ON C.codigo = CD.contrato
  LEFT JOIN contratooperacao CO ON C.codigo = CO.contrato
  INNER JOIN acessocliente AC ON AC.cliente = S.codigocliente
  and AC.dthrentrada >= date_trunc('month', current_date - interval '1 month')
  and AC.dthrentrada < date_trunc('month', current_date)
  LEFT JOIN contratomodalidade CM ON C.codigo = CM.contrato
  LEFT JOIN modalidade M ON CM.modalidade = M.codigo
  LEFT JOIN trancamentocontrato TC ON C.codigo = TC.contrato
WHERE
  S.situacao = 'AT'