select 
	sql.empresa_codigo,
	sql.empresa_financeiro,
	sql.cliente_pessoa,
	sql.cliente_nome,
	sql.username, 
	sql.cliente_contrato,
	sql.produto_Contrato,
	sql.data_faturamento::date,
	sql.data_compensacao::date,
	case when sql.data_compensacao::date = sql.data_compensacao_original::date then null
	else sql.data_compensacao_original::date end as data_compensacao_original,
	sql.forma_pagamento,
	sql.forma_pagamento_descricao,
       case
           when coalesce(sql.tipo_convenio, 0) = 24 then 'BOLETO_ONLINE_PJBANK'
           when coalesce(sql.tipo_convenio, 0) = 33 then 'BOLETO_ONLINE_ITAU'
--
           when coalesce(sql.tipo_convenio, 0) = 28 then 'PIX_BB'
           when coalesce(sql.tipo_convenio, 0) = 29 then 'PIX_BRADESCO'
           when coalesce(sql.tipo_convenio, 0) = 30 then 'PIX_SANTANDER'
--
           when coalesce(sql.tipo_convenio, 0) = 3 then 'DEBITO_CONTA_BB'
           when coalesce(sql.tipo_convenio, 0) = 5 then 'DEBITO_CONTA_ITAU'
           when coalesce(sql.tipo_convenio, 0) = 7 then 'DEBITO_CONTA_HSBC'
           when coalesce(sql.tipo_convenio, 0) in (4, 19) then 'DEBITO_CONTA_BRADESCO'
           when coalesce(sql.tipo_convenio, 0) in (6, 23) then 'DEBITO_CONTA_CAIXA'
           when coalesce(sql.tipo_convenio, 0) in (10, 22) then 'DEBITO_CONTA_SANTANDER'
--
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 1) then 'BOLETO_BB'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 237) then 'BOLETO_BRADESCO'
           when ((coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 341) or coalesce(sql.tipo_convenio, 0) in (9, 25)) then 'BOLETO_ITAU'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco in (104, 151)) then 'BOLETO_CAIXA'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 756) then 'BOLETO_SICOOB'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 33) then 'BOLETO_SANTANDER'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 70) then 'BOLETO_BRB'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 4) then 'BOLETO_BNB'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 748) then 'BOLETO_SICREDI'
           when (coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 422) then 'BOLETO_SAFRA'
           when ((coalesce(sql.tipo_convenio, 0) = 1 and sql.banco = 707) or coalesce(sql.tipo_convenio, 0) = 17) then 'BOLETO_DAYCOVAL'
           when (coalesce(sql.tipo_convenio, 0) = 24 and sql.banco = 301) then 'BOLETO_ONLINE_PJBANK'
           when coalesce(sql.tipo_convenio, 0) = 1 then 'BOLETO'
--
           when coalesce(sql.tipo_convenio, 0) in (2, 8, 11) then 'REMESSA'
           when coalesce(sql.tipo_convenio, 0) in (12, 13, 16, 20, 21, 26, 27, 31, 32, 34) then 'TRANSACAO'
           when coalesce(sql.tipo_convenio, 0) = 0 then 'FISICO'
           else '' end                    as tipo_pagamento,
--
       case
           when coalesce(sql.origem, 0) = 1 then 'CAIXA_EM_ABERTO'
           when coalesce(sql.origem, 0) = 2 then 'RETENTATIVA_MANUAL_ZW'
           when (coalesce(sql.origem, 0) = 3 or UPPER(coalesce(sql.username, '')) = 'RECOR') then 'AUTOMATICO'
           when coalesce(sql.origem, 0) = 4 then 'VENDAS_ONLINE'
           when coalesce(sql.origem, 0) = 5 then 'LINK_DE_PAGAMENTO'
           when coalesce(sql.origem, 0) = 6 then 'PACTO_STORE'
           when coalesce(sql.origem, 0) = 7 then 'WEBSERVICE'
           when coalesce(sql.origem, 0) = 8 then 'VERIFICACAO'
           when coalesce(sql.origem, 0) = 9 then 'RETENTATIVA_PACTOPAY'
           when coalesce(sql.origem, 0) = 10 then 'PACTOPAY'
           when coalesce(sql.origem, 0) = 11 then 'MAILING'
           when (coalesce(sql.tipo_convenio, 0) in (2, 8, 11) and UPPER(coalesce(sql.username, '')) not in ('RECOR')) then 'MANUAL'
           when UPPER(coalesce(sql.username, '')) in ('RECOR') then 'RECORRENCIA'
           when UPPER(coalesce(sql.username, '')) in ('ADMIN') then 'ADMINISTRADOR'
           else '' end                    as origem,
--
       case
           when sql.adquirente ilike '%STONE%' then 'STONE'
           when sql.adquirente ilike '%PAGARME%' then 'PAGAR_ME'
           when sql.adquirente ilike '%SAFRA%' then 'SAFRA'
           when sql.adquirente ilike '%GLOBAL%' then 'GLOBAL_PAYMENTS'
           when sql.adquirente ilike '%REDE%' then 'REDE'
           else UPPER(sql.adquirente) end as adquirente,
--
       sql.adquirente_contrato,
--
       case
           when sql.bandeira ilike '%ELECTRON%' then 'ELECTRON'
           when sql.bandeira ilike '%MASTER%' then 'MASTERCARD'
           when sql.bandeira ilike '%VISA%' then 'VISA'
           when (sql.bandeira ilike 'ELO CREDITO' or sql.bandeira ilike 'ELO DEBITO') then 'ELO'
           when (sql.bandeira ilike '%AMERICAN EXPRESS%' or sql.bandeira ilike '%AMERICAN_EXPRESS%') then 'AMEX'
           else UPPER(sql.bandeira) end   as bandeira,
--
       sql.nr_vezes_zw,
       sql.qtd_parcelas              as qtd_parcelas,
       sql.recibo,
       sql.valor                    as valor,
       sql.valor_receita            as valor_receita,
       sql.pagamento_cc
from (
         select e.codigo                as empresa_codigo,
                e.cod_empresafinanceiro as empresa_financeiro,
--
                case
                    when fp.tipoformapagamento = 'AV' then 'DINHEIRO'
                    when fp.tipoformapagamento = 'CH' then 'CHEQUE'
                    when fp.tipoformapagamento = 'CA' then 'CARTAO_CREDITO'
                    when fp.tipoformapagamento = 'CD' then 'CARTAO_DEBITO'
                    when fp.tipoformapagamento = 'CO' then 'CONVENIO'
                    when fp.tipoformapagamento = 'CC' then 'CONTA_CORRENTE'
                    when fp.tipoformapagamento = 'BB' then 'BOLETO'
                    when fp.tipoformapagamento = 'PD' then 'PAGAMENTO_DIGITAL'
                    when fp.tipoformapagamento = 'LO' then 'LOTE'
                    when fp.tipoformapagamento = 'PF' then 'PARCEIRO_FIDELIDADE'
                    when fp.tipoformapagamento = 'TB' then 'TRANSFERENCIA_BANCARIA'
                    when fp.tipoformapagamento = 'PX' then 'PIX'
                    else 'OUTRA' end    as forma_pagamento,
--
                fp.descricao            as forma_pagamento_descricao,
--
                case
                    when coalesce(ccb.tipoconvenio, 0) > 0 then ccb.tipoconvenio
                    when coalesce(ccp.tipoconvenio, 0) > 0 then ccp.tipoconvenio
                    when coalesce(ccr.tipoconvenio, 0) > 0 then ccr.tipoconvenio
                    when coalesce(cct.tipoconvenio, 0) > 0 then cct.tipoconvenio
                    else 0 end          as tipo_convenio,
--
                case
                    when coalesce(ccb.tipoconvenio, 0) in (12,13,16,20,21,27) then ccb.codigoAutenticacao01
                    when coalesce(ccp.tipoconvenio, 0) in (12,13,16,20,21,27) then ccp.codigoAutenticacao01
                    when coalesce(ccr.tipoconvenio, 0) in (12,13,16,20,21,27) then ccr.codigoAutenticacao01
                    when coalesce(cct.tipoconvenio, 0) in (12,13,16,20,21,27) then cct.codigoAutenticacao01
--
                    when coalesce(ccb.tipoconvenio, 0) in (1,2,8,11) then ccb.numerocontrato
                    when coalesce(ccp.tipoconvenio, 0) in (1,2,8,11) then ccp.numerocontrato
                    when coalesce(ccr.tipoconvenio, 0) in (1,2,8,11) then ccr.numerocontrato
                    when coalesce(cct.tipoconvenio, 0) in (1,2,8,11) then cct.numerocontrato
--
                    when coalesce(ccb.tipoconvenio, 0) in (24) then ccb.chavepjbank
                    when coalesce(ccp.tipoconvenio, 0) in (24) then ccp.chavepjbank
                    when coalesce(ccr.tipoconvenio, 0) in (24) then ccr.chavepjbank
                    when coalesce(cct.tipoconvenio, 0) in (24) then cct.chavepjbank
--
                    else '' end          as adquirente_contrato,
--
                case
                    when coalesce(bar.codigobanco, 0) > 0 then bar.codigobanco
                    else 0 end          as banco,
--
                case
                    when coalesce(tr.origem, 0) > 0 then tr.origem
                    when coalesce(bl.origem, 0) > 0 then bl.origem
                    when coalesce(px.origem, 0) > 0 then px.origem
                    else 0 end          as origem,
--
                case
                    when coalesce(usb.username, '') <> '' then usb.username
                    when coalesce(usr.username, '') <> '' then usr.username
                    when coalesce(usp.username, '') <> '' then usp.username
                    when coalesce(ust.username, '') <> '' then ust.username
                    when coalesce(usm.username, '') <> '' then usm.username
                    else '' end         as username,
--
                case
                    when (tr.tipo = 11) then 'STONE'
                    when (tr.tipo = 3 or re.tipo = 2) then 'CIELO'
                    when (re.tipo = 12) then 'BIN'
                    when (tr.tipo = 10 or re.tipo = 8) then 'GETNET'
                    when (tr.tipo = 2) then 'VINDI'
                    when (tr.tipo = 13) then 'PAGAR_ME'
                    when (tr.tipo = 15) then 'STRIPE'
                    when (tr.tipo = 16) then 'PAGO_LIVRE'
                    when (tr.tipo = 17) then 'VALORI_BANK'
                    when (tr.tipo = 7) then 'REDE'
                    when (tr.tipo = 19) then 'FACILITE_PAY'
                    when (tr.tipo = 21) then 'CEOPAG'
                    when (ad.nome ilike '%STONE%') then 'STONE'--stone tem q vir primeiro que rede.. devido nome REDE STONE
                    when (ad.nome ilike '%CIELO%') then 'CIELO'
                    when (ad.nome ilike '%BIN%') then 'BIN'
                    when (ad.nome ilike '%GETNET%' or ad.nome ilike '%GET NET%') then 'GETNET'
                    when (ad.nome ilike '%VINDI%') then 'VINDI'
                    when (ad.nome ilike '%PAGARME%') then 'PAGAR_ME'
                    when (ad.nome ilike '%STRIPE%') then 'STRIPE'
                    when (ad.nome ilike '%PAGOLIVRE%') then 'PAGO_LIVRE'
                    when (ad.nome ilike '%FACILITEPAY%') then 'FACILITE_PAY'
                    when (ad.nome ilike '%CEOPAG%') then 'CEOPAG'
                    when (ad.nome ilike '%VALORIBANK%' or ad.nome ilike '%VALORI BANK%') then 'VALORI_BANK'
                    when ad.nome ilike '%SAFRA%' then 'SAFRA'
                    when ad.nome ilike '%FROGPAY%' then 'FROGPAY'
                    when ad.nome ilike '%SIPAG%' then 'SIPAG'
                    when ad.nome ilike '%SISPAG%' then 'SISPAG'
                    when ad.nome ilike '%GLOBAL%' then 'GLOBAL_PAYMENTS'
                    when ad.nome ilike '%VIPPAY%' then 'VIPPAY'
                    when ad.nome ilike '%KREDIT%' then 'KREDIT'
                    when ad.nome ilike '%FLEXPAG%' then 'FLEXPAG'
                    when ad.nome ilike '%BCPAG%' then 'BCPAG'
                    when (ad.nome ilike '%MERCADOPAGO%' or ad.nome ilike '%MERCADO PAGO%')then 'MERCADO_PAGO'
                    when (ad.nome ilike '%PAGSEGURO%' or ad.nome ilike '%PAG SEGURO%')then 'PAG_SEGURO'
                    when (ad.nome ilike '%REDE%') then 'REDE'
                    when ((fp.tipoformapagamento = 'CA' or fp.tipoformapagamento = 'CD') and ad.codigo is null) then 'NAO_INFORMADO'
                    when ad.codigo is null then ''
                    else 'OUTRA' end    as adquirente,
--
                case
                    when coalesce(split_part(split_part(mp.respostarequisicaopinpad, 'cardBrandName":"', 2), '"', 1),'') <> '' then split_part(split_part(mp.respostarequisicaopinpad, 'cardBrandName":"', 2), '"', 1)
                    when coalesce(split_part(split_part(tr.outrasinformacoes, 'cartaoBandeira":"', 2), '"', 1), '') <> '' then split_part(split_part(tr.outrasinformacoes, 'cartaoBandeira":"', 2), '"', 1)
                    when coalesce(split_part(split_part(split_part(ri.props, 'Bandeira=', 2), ',', 1), '}', 1), '') <> '' then split_part(split_part(split_part(ri.props, 'Bandeira=', 2), ',', 1), '}', 1)
                    when o.descricao ilike '%MAESTRO%' then 'MAESTRO'
                    when o.descricao ilike '%ELECTRON%' then 'ELECTRON'
                    when o.descricao ilike '%VISA%' then 'VISA'
                    when o.descricao ilike '%MASTER%' then 'MASTERCARD'
                    when o.descricao ilike '%HIPERCARD%' then 'HIPERCARD'
                    when o.descricao ilike '%HIPER%' then 'HIPER'
                    when (o.descricao ilike '%AMERICAN%' or o.descricao ilike '%AMEX%') then 'AMEX'
                    when o.descricao ilike '%ALELO%' then 'ALELO'
                    when o.descricao ilike '%CABAL%' then 'CABAL'
                    when o.descricao ilike '%DISCOVER%' then 'DISCOVER'
                    when o.descricao ilike '%JCB%' then 'JCB'
                    when (o.descricao ilike '%DINERS%' or o.descricao ilike '%DINNERS%') then 'DINERS'
                    when o.descricao ilike '%ELO%' then 'ELO'
                    when ((fp.tipoformapagamento = 'CA' or fp.tipoformapagamento = 'CD') and o.codigo is null) then 'NAO_INFORMADO'
                    when o.codigo is null then ''
                    else 'OUTRA' end    as bandeira,
--
                case
                    when fp.tipoformapagamento = 'CA' then coalesce(mp.nrparcelacartaocredito, 0)
                    else null end       as nr_vezes_zw,
--
                mp.nomepagador               as nomepagador,
--
                case
                    when ch.codigo is not null then ch.valor
                    when cc.codigo is not null then cc.valor
                    else mp.valortotal
                end as valor,
                case
                    when ch.codigo is not null then ch.valor
                    when cc.codigo is not null then cc.valor
                    else mp.valor
                end as valor_receita,
                coalesce(mp.movpagamentoorigemcredito, 0) > 0 as pagamento_cc,
                    (select count(distinct movparcela) from pagamentomovparcela where movpagamento = mp.codigo) as qtd_parcelas,
                    mp.datalancamento::date as data_faturamento,
                    mp.pessoa as cliente_pessoa,
                    pe.nome as cliente_nome,
                    rec.contrato as cliente_contrato,
                    case
                	when rec.contrato is null then 'Venda Avulsa'
                	else p2.descricao
                end as produto_contrato,
                    case
                    when ch.codigo is not null then ch.datacompesancao
                    when cc.codigo is not null then cc.datacompesancao
                    else mp.datapagamento end as data_compensacao,
			case
                    when ch.codigo is not null then ch.dataoriginal
                    when cc.codigo is not null then cc.dataoriginal
                    else mp.datapagamentooriginal end as data_compensacao_original,
                    mp.recibopagamento as recibo
         from movpagamento mp
             inner join empresa e on e.codigo = mp.empresa
             inner join formapagamento fp on fp.codigo = mp.formapagamento
             inner join pessoa pe on pe.codigo = mp.pessoa
             inner join recibopagamento rec on rec.codigo = mp.recibopagamento
             left join cheque ch on ch.movpagamento = mp.codigo and ch.situacao = 'EA'
             left join cartaocredito cc on cc.movpagamento = mp.codigo and cc.situacao = 'EA'
             left join adquirente ad on ad.codigo = mp.adquirente
             left join usuario usm on usm.codigo = mp.responsavelpagamento
             left join operadoracartao o on o.codigo = mp.operadoracartao
             left join pix px on px.recibopagamento = mp.recibopagamento
             left join conveniocobranca ccp on ccp.codigo = px.conveniocobranca
             left join usuario usp on usp.codigo = px.usuarioresponsavel
             left join boleto bl on bl.movpagamento = mp.codigo
             left join conveniocobranca ccb on ccb.codigo = bl.conveniocobranca
             left join usuario usb on usb.codigo = bl.usuario
             left join transacao tr on tr.movpagamento = mp.codigo
             left join conveniocobranca cct on cct.codigo = tr.conveniocobranca
             left join usuario ust on ust.codigo = tr.usuarioresponsavel
             left join remessaitem ri on ri.movpagamento = mp.codigo
             left join remessa re on re.codigo = ri.remessa
             left join conveniocobranca ccr on ccr.codigo = re.conveniocobranca
             left join banco bar on bar.codigo = ccr.banco
             left join usuario usr on usr.codigo = re.usuario
             left join contrato c on rec.contrato = c.codigo 
             left join plano p on c.plano = p.codigo 
             left join produto p2 on p.produtopadraogerarparcelascontrato = p2.codigo 
         -- where mp.datalancamento::date between '2022-12-20' and '2022-12-25'
--            where
            -- Todas venda feitas do 5 ano a traz para frente....
--                cast(mp.datalancamento as date) > ( date_trunc('year', current_date) - interval '5 year')
     ) as sql
     where coalesce(sql.valor,0) > 0
order by 1,2,3,4,5,6,7,8

