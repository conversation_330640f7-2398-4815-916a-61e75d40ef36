WITH primeiro_dia_mes AS (
    SELECT generate_series(
        '2024-01-01'::date, 
        CURRENT_DATE, 
        '1 month'::interval
    ) AS primeiro_dia
)

-- CONTRATOS ATESTADOS
SELECT plano.descricao, 
       count(o.contrato) AS quantidade, 
       contrato.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'ATESTADO' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM contratooperacao AS o
INNER JOIN contrato ON contrato.codigo = o.contrato
INNER JOIN plano ON plano.codigo = contrato.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = plano.codigo
INNER JOIN contratoduracao ON contratoduracao.contrato = contrato.codigo
INNER JOIN empresa e ON e.codigo = contrato.empresa
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN o.datainicioefetivacaooperacao  
AND o.datafimefetivacaooperacao
WHERE o.tipooperacao IN ('AT')
AND ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY plano.descricao, numeromeses, contrato.empresa, nomeempresa, pdm.primeiro_dia
UNION ALL
-- CONTRATOS TRANCADOS
SELECT plano.descricao, 
       count(o.contrato) AS quantidade, 
       contrato.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'TRANCADOS' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM contratooperacao AS o
INNER JOIN contrato ON contrato.codigo = o.contrato
INNER JOIN plano ON plano.codigo = contrato.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = plano.codigo
INNER JOIN contratoduracao ON contratoduracao.contrato = contrato.codigo
INNER JOIN empresa e ON e.codigo = contrato.empresa
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN o.datainicioefetivacaooperacao  
AND o.datafimefetivacaooperacao
WHERE o.tipooperacao IN ('TR', 'TV')
AND ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY plano.descricao, numeromeses, contrato.empresa, nomeempresa, pdm.primeiro_dia
UNION ALL
-- CONTRATOS CARENCIA - FERIAS
SELECT plano.descricao, 
       count(o.contrato) AS quantidade, 
       contrato.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'FERIAS' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM contratooperacao AS o
INNER JOIN contrato ON contrato.codigo = o.contrato
INNER JOIN plano ON plano.codigo = contrato.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = plano.codigo
INNER JOIN contratoduracao ON contratoduracao.contrato = contrato.codigo
INNER JOIN empresa e ON e.codigo = contrato.empresa
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN o.datainicioefetivacaooperacao  
AND o.datafimefetivacaooperacao
WHERE o.tipooperacao IN ('CR')
AND ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY plano.descricao, numeromeses, contrato.empresa, nomeempresa, pdm.primeiro_dia
UNION ALL
-- CONTRATOS ATIVO NORMAL
SELECT p.descricao, 
       count(c.codigo) AS quantidade, 
       c.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'ATIVO NORMAL' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM contrato c
INNER JOIN plano p ON p.codigo = c.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = p.codigo
INNER JOIN empresa e ON e.codigo = c.empresa
INNER JOIN contratoduracao ON contratoduracao.contrato = c.codigo
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN c.vigenciaDe AND c.vigenciaAteAjustada
WHERE NOT EXISTS (
    SELECT h.contrato
    FROM historicocontrato h
    WHERE h.contrato = c.codigo
    AND pdm.primeiro_dia BETWEEN h.datainiciosituacao AND h.datafinalsituacao
    AND h.tipohistorico IN ('AV', 'VE', 'CA', 'DE')
)
AND NOT EXISTS (
    SELECT o.contrato
    FROM contratooperacao o
    WHERE o.contrato = c.codigo
    AND o.tipooperacao IN ('AT', 'CR', 'TR', 'TV')
    AND pdm.primeiro_dia BETWEEN o.datainicioefetivacaooperacao AND o.datafimefetivacaooperacao
)
AND ((planorecorrencia.codigo IS NULL AND p.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY p.descricao, numeromeses, c.empresa, nomeempresa, pdm.primeiro_dia
UNION ALL
-- CONTRATOS A VENCER
SELECT plano.descricao, 
       count(h.contrato) AS quantidade, 
       contrato.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'A VENCER' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM historicocontrato AS h
INNER JOIN contrato ON contrato.codigo = h.contrato
INNER JOIN plano ON plano.codigo = contrato.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = plano.codigo
INNER JOIN contratoduracao ON contratoduracao.contrato = contrato.codigo
INNER JOIN empresa e ON e.codigo = contrato.empresa
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN h.datainiciosituacao  
AND h.datafinalsituacao
WHERE h.tipohistorico = 'AV'
AND ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY plano.descricao, numeromeses, contrato.empresa, nomeempresa, pdm.primeiro_dia
UNION ALL
-- CONTRATOS VENCIDOS
SELECT plano.descricao, 
       count(h.contrato) AS quantidade, 
       contrato.empresa, 
       contratoduracao.numeromeses AS numeromeses, 
       e.nome AS nomeempresa, 
       'VENCIDOS' AS situacaoContrato, 
       pdm.primeiro_dia AS data_hoje,
       TO_CHAR(pdm.primeiro_dia, 'Month') AS nome_mes,
       TO_CHAR(pdm.primeiro_dia, 'YYYY') AS ano
FROM historicocontrato AS h
INNER JOIN contrato ON contrato.codigo = h.contrato
INNER JOIN plano ON plano.codigo = contrato.plano
LEFT JOIN planorecorrencia ON planorecorrencia.plano = plano.codigo
INNER JOIN contratoduracao ON contratoduracao.contrato = contrato.codigo
INNER JOIN empresa e ON e.codigo = contrato.empresa
JOIN primeiro_dia_mes pdm ON pdm.primeiro_dia BETWEEN h.datainiciosituacao  
AND h.datafinalsituacao
WHERE h.tipohistorico = 'VE'
AND ((planorecorrencia.codigo IS NULL AND plano.renovavelautomaticamente IS FALSE) 
     OR (planorecorrencia.codigo IS NOT NULL AND planorecorrencia.renovavelautomaticamente IS FALSE))
GROUP BY plano.descricao, numeromeses, contrato.empresa, nomeempresa, pdm.primeiro_dia
ORDER BY numeromeses;
