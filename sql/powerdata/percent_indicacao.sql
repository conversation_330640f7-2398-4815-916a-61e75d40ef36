-- PORCENTAGEM DE CONVERSAO DE INDICACAO
WITH meta AS (
    -- Subconsulta para contar o total de metas por dia
    SELECT 
        e.nome as empresa,
        am.dia::date AS data_meta,
        COUNT(*) AS total_meta 
    FROM fecharmetadetalhado fmd
    INNER JOIN fecharmeta fm ON fm.codigo = fmd.fecharmeta
    INNER JOIN aberturameta am ON am.codigo = fm.aberturameta
    LEFT JOIN cliente cli ON cli.codigo = fmd.cliente
    LEFT JOIN pessoa pes ON pes.codigo = cli.pessoa
    LEFT JOIN passivo pas ON pas.codigo = fmd.passivo
    LEFT JOIN indicado ind ON ind.codigo = fmd.indicado
    LEFT JOIN situacaoclientesinteticodw scs ON scs.codigocliente = fmd.cliente
    LEFT JOIN agenda ag ON ag.codigo = fmd.codigoorigem AND fmd.origem = 'AGENDA'
    inner join empresa e on e.codigo = fmd.empresa
    WHERE am.dia::date > '2024-01-01'
    AND fm.identificadormeta IN ('IN') -- Meta para Indicados
    AND fmd.obtevesucesso = TRUE
    AND fmd.repescagem = FALSE
    GROUP BY am.dia::date
),
atingida AS (
    -- Subconsulta para contar o total de metas atingidas por dia
    SELECT 
        e.nome as empresa,
        am.dia::date AS data_meta,
        COUNT(*) AS total_atingida 
    FROM fecharmetadetalhado fmd
    INNER JOIN fecharmeta fm ON fm.codigo = fmd.fecharmeta
    INNER JOIN aberturameta am ON am.codigo = fm.aberturameta
    LEFT JOIN cliente cli ON cli.codigo = fmd.cliente
    LEFT JOIN pessoa pes ON pes.codigo = cli.pessoa
    LEFT JOIN passivo pas ON pas.codigo = fmd.passivo
    LEFT JOIN indicado ind ON ind.codigo = fmd.indicado
    LEFT JOIN situacaoclientesinteticodw scs ON scs.codigocliente = fmd.cliente
    LEFT JOIN agenda ag ON ag.codigo = fmd.codigoorigem AND fmd.origem = 'AGENDA'
    WHERE am.dia::date > '2024-01-01'
    AND fm.identificadormeta IN ('CI') -- Conversão de Indicados Atingida
    AND fmd.obtevesucesso = TRUE
    AND fmd.repescagem = FALSE
    GROUP BY am.dia::date
)
-- Consulta final para calcular a porcentagem por data
SELECT 
    COALESCE(m.data_meta, a.data_meta) AS data_meta,
    CASE 
        WHEN m.total_meta = 0 THEN 0 -- Se o total de metas for zero, retorna 0%
        ELSE (a.total_atingida::decimal / m.total_meta::decimal) * 100
    END AS porcentagem
FROM meta m
FULL JOIN atingida a ON m.data_meta = a.data_meta
ORDER BY data_meta;
