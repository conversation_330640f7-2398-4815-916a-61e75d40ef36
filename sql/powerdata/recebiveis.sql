select
	distinct movpagamento.codigo as cod_movpagamento,
	movpagamento.nomepagador,
	movpagamento.valor,
	movpagamento.valortotal,
	movpagamento.datalancamento::timestamp,
	movpagamento.datapagamento::timestamp,
	movpagamento.dataquitacao::timestamp,
	movpagamento.datapagamentooriginal::timestamp,
	movpagamento.pessoa as cod_pessoa,
	empresa.cod_empresafinanceiro as cod_empresafinanceiro,
	empresa.nome as empresaNome,
	cli.matricula,
	cli.cpf as cpfPagador,
	conta.descricao as descricaoconta,
	movc.dataquitacao as datamovconta,
	array_to_string(array
                                  (
	select
		mp.descricao
	from
		pagamentomovparcela p
	inner join movparcela mp on
		mp.codigo = p.movparcela
	where
		p.movpagamento = movpagamento.codigo),
	', ',
	'') as parcelasPagas,
	case
		when fp.tipoformapagamento = 'CA' then cartaocredito.valor
		when fp.tipoformapagamento = 'CH' then cartaocredito.valor
		else movpagamento.valor
	end as valor_recebiveis,
	fp.descricao as formapagamento_descricao,
	p.nomepai,
	p.nomemae,
	p.datanasc,
	-- Criação da coluna de data normalizada:
 case
		when fp.tipoformapagamento in ('AV',
                                    'CD',
                                    'CC',
                                    'BB',
                                    'CO',
                                    'TB',
                                    'PX') then movpagamento.datapagamentooriginal::timestamp
		when fp.tipoformapagamento = 'CH' then coalesce(cheque.dataoriginal::timestamp,
		cheque.datacompesancao::timestamp)
		when fp.tipoformapagamento = 'CA' then coalesce(cartaocredito.dataoriginal::timestamp,
		cartaocredito.datacompesancao::timestamp,
		ei.datapgtooriginalantesdaantecipacao::timestamp)
		else movpagamento.datapagamentooriginal::timestamp
	end as data_compensacao_original,
	case
		when fp.tipoformapagamento in ('AV',
                                    'CD',
                                    'CC',
                                    'BB',
                                    'CO',
                                    'TB',
                                    'PX') then movpagamento.datapagamento::timestamp
		when fp.tipoformapagamento = 'CH' then cheque.datacompesancao::timestamp
		when fp.tipoformapagamento = 'CA' then cartaocredito.datacompesancao::timestamp
		else movpagamento.datapagamento::timestamp
	end as data_compensacao
from
	movpagamento
left join empresa on
	empresa.codigo = movpagamento.empresa
left outer join situacaoclientesinteticodw as cli on
	cli.codigopessoa = movpagamento.pessoa
left outer join movconta as movc on
	movc.codigo = movpagamento.movconta
left outer join conta on
	conta.codigo = movc.conta
left join pessoa p on
	p.codigo = movpagamento.pessoa
left outer join cheque on
	movpagamento.codigo = cheque.movpagamento
	and cheque.situacao not like 'CA'
	and cheque.situacao not like 'DV'
left outer join cartaocredito on
	movpagamento.codigo = cartaocredito.movpagamento
	and cartaocredito.situacao not like 'CA'
inner join formapagamento fp on
	movpagamento.formapagamento = fp.codigo
inner join usuario u on
	u.codigo = movpagamento.responsavelpagamento
left join extratodiarioitem ei on
	ei.codigo =
  (
	select
		max(ei2.codigo)
	from
		extratodiarioitem ei2
	where
		ei2.codigocartaocredito is not null
		and ei2.codigocartaocredito = cartaocredito.codigo
		and ei2.datapgtooriginalantesdaantecipacao is not null
		and ei2.antecipacao is true
		and ei2.tipoconciliacao = 4)
where
	1 = 1
	and (movpagamento.recibopagamento is not null
		or movpagamento.credito = 't')
	and (movpagamento.valor > 0
		or fp.tipoformapagamento = 'CC')