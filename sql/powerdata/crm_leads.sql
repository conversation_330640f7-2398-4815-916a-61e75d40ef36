select
    m.empresa_codigo,
    m.empresa_nome,
    m.colaboradorresponsavel,
    m.colaboradorresponsavel_nome,
    m.meta,
    m.dia_meta as dia_meta,
    m.passivo as nome_lead,
    m.email,
    m.telefonecelular,
    m.obteves<PERSON>sso,
    m.codigo<PERSON><PERSON>ula,
    m.situacao_cliente
from (
         select e.nome as empresa_nome,
                e.codigo as empresa_codigo,
                e.cod_empresafinanceiro,
                a.dia as dia_meta,
                a.colaboradorresponsavel,
                pes_col.nome as colaboradorresponsavel_nome,
                f.identificadormeta,
                case f.identificadormeta
                    when 'LH' then 'Leads Hoje'
                    when 'LC' then 'Leads Acumuladas'
                    end as meta,
                pas.nome as passivo,
                pas.email,
                pas.telefonecelular,
                fd.obtevesucesso,
                cli.codigomatricula,
                case when cli.situacao = 'AT' then 'Ativo' 
                	 when cli.situacao = 'IN' then 'Inativo'
                	 when cli.situacao = 'TR' then 'Trancado'
                	 when cli.situacao = 'VI' then 'Visitante'
                	 else '' end as situacao_cliente
         from aberturameta a
                  inner join fecharmeta f on f.aberturameta = a.codigo
                  inner join fecharmetadetalhado fd on fd.fecharmeta = f.codigo
                  inner join empresa e on e.codigo = a.empresa
                  inner join usuario u on u.codigo = a.colaboradorresponsavel
                  inner join colaborador c on c.codigo = u.colaborador
                  inner join pessoa pes_col on pes_col.codigo = c.pessoa
                  inner join passivo pas on pas.codigo = fd.passivo
                  left join historicocontato h on h.codigo = fd.historicocontato
                  left join "lead" l on l.passivo = pas.codigo
                  left join cliente cli on cli.codigo = l.cliente) as m
where 1 = 1
  and m.identificadormeta in ('LH', 'LC')
  AND m.dia_meta BETWEEN current_date - interval '30 days' and current_date
order by m.dia_meta::timestamp,
    m.empresa_codigo,
    m.colaboradorresponsavel
        desc
