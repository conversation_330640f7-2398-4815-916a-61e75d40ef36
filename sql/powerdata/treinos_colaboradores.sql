SELECT 
    em.codigo AS codigo_empresa, 
    em.nome AS nome_empresa, 
    prs.nome AS nome_professor,  
    COUNT(CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM ProgramaTreino pt 
                WHERE pt.cliente_codigo = cli.codigo
            ) THEN 1 
            ELSE NULL 
          END) AS quantidade_com_treino,
    COUNT(CASE 
            WHEN NOT EXISTS (
                SELECT 1 
                FROM ProgramaTreino pt 
                WHERE pt.cliente_codigo = cli.codigo
            ) THEN 1 
            ELSE NULL 
          END) AS quantidade_sem_treino
FROM 
    clientesintetico cli
INNER JOIN 
    professorsintetico prs ON cli.professorsintetico_codigo = prs.codigo AND prs.ativo = 'true'
INNER JOIN 
    empresa em ON cli.empresa = em.codigo
--WHERE prs.nome = 'PERSONAL TRAINER' -- Filtro opcional
GROUP BY 
    em.codigo, em.nome, prs.nome
ORDER BY 
    quantidade_com_treino DESC;