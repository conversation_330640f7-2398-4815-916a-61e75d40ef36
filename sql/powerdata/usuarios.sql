--select 
--  sql.situacao,
--  count(distinct pessoa_codigo) as qtde_pessoas,
--  count(distinct usuario_codigo) as qtde_usuarios,
--  count(*) as qtde
--from (
select
    'USUARIOS' as usuario_tipo,
    emp.nome as empresa_nome,
    emp.cod_empresafinanceiro,
    pes.datacadastro as pessoa_data_cadastro,
    pes.codigo as pessoa_codigo,
    pes.nome as pessoa_nome,
    pes.datanasc,
    pes.sexo,
    uf.sigla as uf,
    cid.nome as cidade,
	array_to_string(
		array(
			select tel.numero
			from telefone tel
			where tel.pessoa = c.pessoa
		), ',', ''
	) as telefones,
	array_to_string(
		array(
			select email
			from email em
			where em.pessoa = c.pessoa
		), ',', ''
	) as emails,
    c.codigo as codigo,
    null as matricula,
    c.situacao as situacao,
    c.codacesso as codacesso,
    p.nome as perfil_nome,
    u.codigo as usuario_codigo,
    u.username as usuario_username,
    u.administrador as usuario_adm,    
    u.linguagem,
    u.usuariogeral,
    u.dataalteracaosenha,    
    pes.dataaceitetermospacto as atu_cad_data,
    pes.aceitetermospacto as atu_cad_concluida,
	CASE
        WHEN tp.descricao = 'PR' THEN 'Professor'
        WHEN tp.descricao = 'TW' THEN 'Professor (TreinoWeb)'
        WHEN tp.descricao = 'PT' THEN 'Personal Trainer'
        WHEN tp.descricao = 'OR' THEN 'Orientador'
        WHEN tp.descricao = 'CO' THEN 'Consultor'
        WHEN tp.descricao = 'PI' THEN 'Personal Interno'
        WHEN tp.descricao = 'PE' THEN 'Personal Externo'
        WHEN tp.descricao = 'TE' THEN 'Terceirizado'
        WHEN tp.descricao = 'ES' THEN 'Estúdio'
        WHEN tp.descricao = 'FO' THEN 'Fornecedor'
        WHEN tp.descricao = 'CR' THEN 'Coordenador'
        WHEN tp.descricao = 'MD' THEN 'Médico'
        WHEN tp.descricao = 'FC' THEN 'Funcionário'
        WHEN tp.descricao = 'AD' THEN 'Administrador'
        ELSE 'Outro'
    END AS tipo_colaborador,
    CASE 
	    WHEN p.tipo = 0 THEN 'Todos'
	    WHEN p.tipo = 1 THEN 'Administrador'
	    WHEN p.tipo = 2 THEN 'Consultor'
	    WHEN p.tipo = 3 THEN 'Gerente'
	    WHEN p.tipo = 4 THEN 'Professor'
	END AS perfil_tipo
from
    usuario u
    inner join colaborador c on u.colaborador = c.codigo
    inner join pessoa pes on pes.codigo = c.pessoa
    inner join empresa emp on emp.codigo = c.empresa and emp.ativa
    left join usuarioperfilacesso up on up.usuario = u.codigo
    left join perfilacesso p on p.codigo = up.perfilacesso
    left join estado uf on uf.codigo = pes.estado
    left join cidade cid on cid.codigo = pes.cidade
    left join tipocolaborador tp on tp.colaborador = c.codigo
-- where
--     c.situacao = 'AT'
--
union all
--
select
    'PROFESSORES APP' as usuario_tipo,
    emp.nome as empresa_nome,
    emp.cod_empresafinanceiro,    
    pes.datacadastro as pessoa_data_cadastro,
    pes.codigo as pessoa_codigo,
    pes.nome as pessoa_nome,
    pes.datanasc,
    pes.sexo,
    uf.sigla as uf,
    cid.nome as cidade,
	array_to_string(
		array(
			select tel.numero
			from telefone tel
			where tel.pessoa = col.pessoa
		), ',', ''
	) as telefones,
	array_to_string(
		array(
			select email
			from email em
			where em.pessoa = col.pessoa
		), ',', ''
	) as emails,
    col.codigo as codigo,
    null as matricula,
    col.situacao as situacao,
    col.codacesso as codacesso,
    null perfil_nome,
    u.codigo as usuario_codigo,
    um.nome as usuario_username,   
    false as usuario_adm,    
    null as linguagem,
    null as usuariogeral,
    null as dataalteracaosenha,
    null as atu_cad_data,
    null as atu_cad_concluida,
    CASE
        WHEN tp.descricao = 'PR' THEN 'Professor'
        WHEN tp.descricao = 'TW' THEN 'Professor (TreinoWeb)'
        WHEN tp.descricao = 'PT' THEN 'Personal Trainer'
        WHEN tp.descricao = 'OR' THEN 'Orientador'
        WHEN tp.descricao = 'CO' THEN 'Consultor'
        WHEN tp.descricao = 'PI' THEN 'Personal Interno'
        WHEN tp.descricao = 'PE' THEN 'Personal Externo'
        WHEN tp.descricao = 'TE' THEN 'Terceirizado'
        WHEN tp.descricao = 'ES' THEN 'Estúdio'
        WHEN tp.descricao = 'FO' THEN 'Fornecedor'
        WHEN tp.descricao = 'CR' THEN 'Coordenador'
        WHEN tp.descricao = 'MD' THEN 'Médico'
        WHEN tp.descricao = 'FC' THEN 'Funcionário'
        WHEN tp.descricao = 'AD' THEN 'Administrador'
        ELSE 'Outro'
    END AS tipo_colaborador,
	'Professor' AS perfil_tipo
from
    usuariomovel um
    inner join colaborador col on col.codigo = um.colaborador
    inner join pessoa pes on pes.codigo = col.pessoa
    inner join empresa emp on emp.codigo = col.empresa and emp.ativa
--    left join telefone tel on tel.pessoa = pes.codigo
--    left join email em on em.pessoa = pes.codigo
    left join usuario u on u.colaborador = col.codigo
    left join estado uf on uf.codigo = pes.estado
    left join cidade cid on cid.codigo = pes.cidade
	left join tipocolaborador tp on tp.colaborador = col.codigo
where
    um.ativo
    and u.codigo is null
--    ) sql
--where 1=1
--  and sql.cod_empresafinanceiro in (8183,3571)
--  and sql.usuario_tipo = 'USUARIOS'
--group by 1
 
 