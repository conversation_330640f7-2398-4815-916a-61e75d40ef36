SELECT 
    op.contrato AS codigo_contrato,
    con.empresa AS codigo_empresa_contrato,
    em.nome AS nome_empresa,
    con.pessoa,
    cl.matricula AS matricula_pessoa,
    pe.nome AS nome_pessoa,
    con.situacao,
    con.situacaocontrato as situacao_contrato,
    op.tipooperacao AS sigla_operacao,
    jus.descricao AS motivo_cancelamento,
    op.valor,
    con.valorbasecalculo,
    con.valorfinal,
    con.valorarredondamento,
    pe.datacadastro AS datacadastro_pessoa,
    op.dataoperacao AS data_operacao,
    con.datalancamento AS data_lancamento
FROM contrato con
INNER JOIN pessoa pe ON con.pessoa = pe.codigo
LEFT JOIN cliente cl ON con.pessoa = cl.pessoa
LEFT JOIN empresa em ON cl.empresa = em.codigo
LEFT JOIN contratooperacao op ON con.codigo = op.contrato
LEFT JOIN justificativaoperacao jus ON op.tipojustificativa = jus.codigo;

