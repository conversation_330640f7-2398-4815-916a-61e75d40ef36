select 
  sql.*
--  sql.tiporegistro,
--  sql.situacao_c,
--  sum(sql.valortotal) as soma ,
--  count(distinct sql.contrato) as qtdecontrato,
--  count(distinct  sql.vendaavulsa) qtdeVendaAvulsa,
--  count(distinct sql.codigopessoa) as  qtdePessoas,
--  count(*) as qtdeReg
from 
(
--
 SELECT 'BV'::character varying AS tiporegistro,
    emp.cod_empresafinanceiro,
--    emp.nome AS nomeunidade,
--    cid.nome AS nomecidade,
--    est.descricao AS nomeestado,
    quest.data,
    pessoa.codigo AS codigopessoa,
    pessoa.nome AS nomepessoa,
    ( SELECT contrato.situacao
           FROM contrato
          WHERE contrato.pessoa = cli.pessoa AND contrato.datalancamento::date = quest.data::date
         LIMIT 1) AS situacao_c,
    ( SELECT contrato.situacaocontrato
           FROM contrato
          WHERE contrato.pessoa = cli.pessoa AND contrato.datalancamento::date = quest.data::date
         LIMIT 1) AS situacaocontrato,
    ( SELECT contrato.codigo
           FROM contrato
          WHERE contrato.pessoa = cli.pessoa AND contrato.datalancamento::date = quest.data::date
         LIMIT 1) AS contrato,
        CASE
            WHEN quest.tipobv = 1 THEN 'Matricula'::text
            WHEN quest.tipobv = 2 THEN 'Retorno'::text
            WHEN quest.tipobv = 3 THEN 'Rematricula'::text
            ELSE 'NÃO DEFINIDO'::text
        END AS tipoquestionario,
        CASE
            WHEN quest.origemsistema = 1 THEN 'Academia'::text
            WHEN quest.origemsistema = 2 THEN 'Agenda Web'::text
            WHEN quest.origemsistema = 3 THEN 'Pacto Treino'::text
            WHEN quest.origemsistema = 4 THEN 'App Treino'::text
            WHEN quest.origemsistema = 5 THEN 'App Professor'::text
            WHEN quest.origemsistema = 6 THEN 'Autoatendimento'::text
            WHEN quest.origemsistema = 7 THEN 'Site Vendas'::text
            WHEN quest.origemsistema = 8 THEN 'Buzz Lead'::text
            WHEN quest.origemsistema = 9 THEN 'Vendas online'::text
            WHEN quest.origemsistema = 10 THEN 'App do consultor'::text
            WHEN quest.origemsistema = 11 THEN 'Booking Gympass'::text
            WHEN quest.origemsistema = 12 THEN 'Fila de espera'::text
            WHEN quest.origemsistema = 13 THEN 'Importação API'::text
            ELSE 'NÃO DEFINIDO'::text
        END AS origemsistema,
    eq.descricao AS nomeevento,
    eq.codigo AS codigoevento,
    NULL::character varying AS usuariovenda,
    NULL::character varying AS ipcliente,
    NULL::text AS errovendasonline,
    NULL::integer AS codigoplano,
    NULL::character varying AS nomeplano,
--    NULL::real AS valortotal,
    NULL::character varying AS produto,
    NULL::integer AS vendaavulsa,
    NULL::text AS bv_evento,
    NULL::date AS bv_data,
    NULL::text AS bv_tipo,
    NULL::text AS bv_origemsistema,
    NULL::text AS bv_consultor,
    NULL::text AS telavendasonline,
    NULL::integer AS totaltentativa,
    NULL::text AS link,
    NULL::real AS valortotal
   FROM questionariocliente quest
     JOIN questionario q2 ON quest.questionario = q2.codigo AND q2.tipoquestionario::text = 'PL'::text
     JOIN cliente cli ON cli.codigo = quest.cliente
     JOIN empresa emp ON emp.codigo = cli.empresa
     JOIN pessoa ON pessoa.codigo = cli.pessoa
     JOIN colaborador ON colaborador.codigo = quest.consultor
     JOIN pessoa pc ON pc.codigo = colaborador.pessoa
     LEFT JOIN evento eq ON eq.codigo = quest.evento
--     LEFT JOIN cidade cid ON cid.codigo = emp.cidade
--     LEFT JOIN estado est ON est.codigo = emp.estado
UNION ALL
 SELECT DISTINCT 'VENDAS_ONLINE_COMPRA_ERRO'::character varying AS tiporegistro,
    emp.cod_empresafinanceiro,
--    emp.nome AS nomeunidade,
--    cid.nome AS nomecidade,
--    est.descricao AS nomeestado,
    ( SELECT log.dataalteracao
           FROM log
          WHERE log.chaveprimaria::text = l.chaveprimaria::text AND log.operacao::text = 'ESTORNO_COBRANCA_NAO_AUTORIZADA'::text AND log.dataalteracao::date = l.dataalteracao::date
          ORDER BY log.dataalteracao DESC
         LIMIT 1) AS data,
    v.pessoa AS codigopessoa,
    pessoa.nome AS nomepessoa,
    NULL::text AS situacao_c,
    NULL::text AS situacaocontrato,
    v.contrato,
    NULL::text AS tipoquestionario,
    'Vendas online'::character varying AS origemsistema,
    e.descricao AS nomeevento,
    e.codigo AS codigoevento,
    u.nome AS usuariovenda,
    v.ip AS ipcliente,
    ( SELECT log.valorcampoalterado
           FROM log
          WHERE log.chaveprimaria::text = l.chaveprimaria::text AND log.operacao::text = 'ESTORNO_COBRANCA_NAO_AUTORIZADA'::text AND log.dataalteracao::date = l.dataalteracao::date
          ORDER BY log.dataalteracao DESC
         LIMIT 1) AS errovendasonline,
    NULL::integer AS codigoplano,
    plano.descricao AS nomeplano,
--    NULL::real AS valortotal,
    produto.descricao AS produto,
    NULL::integer AS vendaavulsa,
    NULL::text AS bv_evento,
    NULL::date AS bv_data,
    NULL::text AS bv_tipo,
    NULL::text AS bv_origemsistema,
    NULL::text AS bv_consultor,
    v.tela AS telavendasonline,
    NULL::integer AS totaltentativa,
    v.link,
    NULL::real AS valortotal
   FROM cliente cli
     JOIN log l ON l.chaveprimaria::integer = cli.codigo
     LEFT JOIN vendasonlinecampanhaicv v ON v.codigo = (( SELECT vendasonlinecampanhaicv.codigo
           FROM vendasonlinecampanhaicv
          WHERE vendasonlinecampanhaicv.pessoa = cli.pessoa AND vendasonlinecampanhaicv.datainiciooperacao::date = l.dataalteracao::date
          ORDER BY vendasonlinecampanhaicv.datainiciooperacao DESC
         LIMIT 1))
     LEFT JOIN pessoa ON pessoa.codigo = cli.pessoa
     LEFT JOIN empresa emp ON emp.codigo = cli.empresa
     LEFT JOIN evento e ON e.codigo = v.evento
     LEFT JOIN usuario u ON u.codigo = v.usuario
--     LEFT JOIN cidade cid ON cid.codigo = emp.cidade
--     LEFT JOIN estado est ON est.codigo = emp.estado
     LEFT JOIN contrato ON contrato.codigo = v.contrato
     LEFT JOIN vendaavulsa ON vendaavulsa.codigo = v.vendaavulsa
    --  -- NOVA ATERALÇ
    --  LEFT JOIN produto ON produto.codigo = (
    --     SELECT NULLIF(split_part(split_part(aux.link::text, 'pr=', 2), '&', 1)::integer, 0)
    --     FROM vendasonlinecampanhaicv aux
    --     WHERE aux.codigo = v.codigo
    --  )
    --  LEFT JOIN plano ON plano.codigo = (
    --     SELECT NULLIF(split_part(split_part(aux.link::text, 'pl=', 2), '&', 1)::integer, 0)
    --     FROM vendasonlinecampanhaicv aux
    --     WHERE aux.codigo = v.codigo
    --  )
    --
     LEFT JOIN produto ON produto.codigo = (( SELECT NULLIF(regexp_replace(
                CASE
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), 'pr='::text) = 0 THEN '0'::text
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), '&'::text) = 0 THEN "substring"(aux.link::text, "position"(aux.link::text, 'pr='::text) + 3)
                    ELSE "substring"(aux.link::text, "position"(aux.link::text, 'pr='::text) + 3, "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), '&'::text) - 4)
                END, '\D'::text, ''::text, 'g'::text), ''::text)::integer AS "nullif"
           FROM vendasonlinecampanhaicv aux
          WHERE aux.codigo = v.codigo))
     LEFT JOIN plano ON plano.codigo = (( SELECT NULLIF(regexp_replace(
                CASE
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), 'pl='::text) = 0 THEN '0'::text
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), '&'::text) = 0 THEN "substring"(aux.link::text, "position"(aux.link::text, 'pl='::text) + 3)
                    ELSE "substring"(aux.link::text, "position"(aux.link::text, 'pl='::text) + 3, "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), '&'::text) - 4)
                END, '\D'::text, ''::text, 'g'::text), ''::text)::integer AS "nullif"
           FROM vendasonlinecampanhaicv aux
          WHERE aux.codigo = v.codigo))
  WHERE l.operacao::text = 'ESTORNO_COBRANCA_NAO_AUTORIZADA'::text AND cli.situacao::text <> 'AT'::text
UNION ALL
 SELECT 'VENDAS_ONLINE_ACESSOU_PAGINA_SEM_INFO_CLIENTE'::character varying AS tiporegistro,
    emp.cod_empresafinanceiro,
--    emp.nome AS nomeunidade,
--    cid.nome AS nomecidade,
--    est.descricao AS nomeestado,
    v.datainiciooperacao AS data,
    v.pessoa AS codigopessoa,
    pessoa.nome AS nomepessoa,
    NULL::text AS situacao_c,
    NULL::text AS situacaocontrato,
    v.contrato,
    NULL::text AS tipoquestionario,
    'Vendas online'::character varying AS origemsistema,
    e.descricao AS nomeevento,
    e.codigo AS codigoevento,
    u.nome AS usuariovenda,
    v.ip AS ipcliente,
    NULL::text AS errovendasonline,
    plano.codigo AS codigoplano,
    plano.descricao AS nomeplano,
--    NULL::real AS valortotal,
    produto.descricao AS produto,
    v.vendaavulsa,
    NULL::text AS bv_evento,
    NULL::date AS bv_data,
    NULL::text AS bv_tipo,
    NULL::text AS bv_origemsistema,
    NULL::text AS bv_consultor,
    v.tela AS telavendasonline,
    NULL::integer AS totaltentativa,
    v.link,
    NULL::real AS valortotal
   FROM vendasonlinecampanhaicv v
     LEFT JOIN pessoa ON pessoa.codigo = v.pessoa
     LEFT JOIN empresa emp ON emp.codigo = v.empresa
     LEFT JOIN evento e ON e.codigo = v.evento
     LEFT JOIN usuario u ON u.codigo = v.usuario
--     LEFT JOIN cidade cid ON cid.codigo = emp.cidade
--     LEFT JOIN estado est ON est.codigo = emp.estado
     LEFT JOIN produto ON produto.codigo = (( SELECT NULLIF(regexp_replace(
                CASE
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), 'pr='::text) = 0 THEN '0'::text
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), '&'::text) = 0 THEN "substring"(aux.link::text, "position"(aux.link::text, 'pr='::text) + 3)
                    ELSE "substring"(aux.link::text, "position"(aux.link::text, 'pr='::text) + 3, "position"("substring"(aux.link::text, "position"(aux.link::text, 'pr='::text)), '&'::text) - 4)
                END, '\D'::text, ''::text, 'g'::text), ''::text)::integer AS "nullif"
           FROM vendasonlinecampanhaicv aux
          WHERE aux.codigo = v.codigo))
     LEFT JOIN plano ON plano.codigo = (( SELECT NULLIF(regexp_replace(
                CASE
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), 'pl='::text) = 0 THEN '0'::text
                    WHEN "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), '&'::text) = 0 THEN "substring"(aux.link::text, "position"(aux.link::text, 'pl='::text) + 3)
                    ELSE "substring"(aux.link::text, "position"(aux.link::text, 'pl='::text) + 3, "position"("substring"(aux.link::text, "position"(aux.link::text, 'pl='::text)), '&'::text) - 4)
                END, '\D'::text, ''::text, 'g'::text), ''::text)::integer AS "nullif"
           FROM vendasonlinecampanhaicv aux
          WHERE aux.codigo = v.codigo))
  WHERE v.contrato IS NULL AND v.vendaavulsa IS NULL AND v.pessoa IS null
--
UNION all
--
select 
  case 
    when mprod.contrato is not null then 'CONTRATO' 
    when mprod.vendaavulsa is not null then 'VENDA_AVULSA'
    else 'OUTRAS_VENDAS' 
  end as tiporegistro,	    
    emp.cod_empresafinanceiro,
--    emp.nome AS nomeunidade,
--    cid.nome AS nomecidade,
--    est.descricao AS nomeestado, 
    mprod.datalancamento as data,
    mprod.pessoa as codigopessoa,
    pessoa.nome AS nomepessoa,   -- RETIRAR
    c.situacao 	as situacao_c,
    c.situacaocontrato,
    mprod.contrato,
    NULL::text AS tipoquestionario,
    CASE
        WHEN c.origemsistema = 1 THEN 'Academia'::text
        WHEN c.origemsistema = 2 THEN 'Agenda Web'::text
        WHEN c.origemsistema = 3 THEN 'Pacto Treino'::text
        WHEN c.origemsistema = 4 THEN 'App Treino'::text
        WHEN c.origemsistema = 5 THEN 'App Professor'::text
        WHEN c.origemsistema = 6 THEN 'Autoatendimento'::text
        WHEN c.origemsistema = 7 THEN 'Site Vendas'::text
        WHEN c.origemsistema = 8 THEN 'Buzz Lead'::text
        WHEN c.origemsistema = 9 THEN 'Vendas online'::text
        WHEN c.origemsistema = 10 THEN 'App do consultor'::text
        WHEN c.origemsistema = 11 THEN 'Booking Gympass'::text
        WHEN c.origemsistema = 12 THEN 'Fila de espera'::text
        WHEN c.origemsistema = 13 THEN 'Importação API'::text
        ELSE 'NÃO DEFINIDO'::text
    END AS origemsistema,
    e.descricao AS nomeevento,
    e.codigo AS codigoevento,
    u.nome AS usuariovenda,
    vc.ip AS ipcliente,
    NULL::text AS errovendasonline,
    plano.codigo AS codigoplano,
    plano.descricao AS nomeplano,
--    mprod.valorfaturado as valortotal,  -- PRINCIPAL CAMPO.
    prod.descricao as produto,
    mprod.vendaavulsa,
  --  
    e2.descricao as bv_evento,
    qq."data" as bv_data,
     CASE
        WHEN qq.tipobv = 1 THEN 'Matricula'::text
        WHEN qq.tipobv = 2 THEN 'Retorno'::text
        WHEN qq.tipobv = 3 THEN 'Rematricula'::text
        ELSE 'NÃO DEFINIDO'::text
     END AS bv_tipo,
     CASE
        WHEN qq.origemsistema = 1 THEN 'Academia'::text
        WHEN qq.origemsistema = 2 THEN 'Agenda Web'::text
        WHEN qq.origemsistema = 3 THEN 'Pacto Treino'::text
        WHEN qq.origemsistema = 4 THEN 'App Treino'::text
        WHEN qq.origemsistema = 5 THEN 'App Professor'::text
        WHEN qq.origemsistema = 6 THEN 'Autoatendimento'::text
        WHEN qq.origemsistema = 7 THEN 'Site Vendas'::text
        WHEN qq.origemsistema = 8 THEN 'Buzz Lead'::text
        WHEN qq.origemsistema = 9 THEN 'Vendas online'::text
        WHEN qq.origemsistema = 10 THEN 'App do consultor'::text
        WHEN qq.origemsistema = 11 THEN 'Booking Gympass'::text
        WHEN qq.origemsistema = 12 THEN 'Fila de espera'::text
        WHEN qq.origemsistema = 13 THEN 'Importação API'::text
        ELSE 'NÃO DEFINIDO'::text
     END AS bv_origemsistema,
     pc.nome as bv_consultor,
     vc.tela AS telavendasonline,
     ( SELECT count(*) AS count
           FROM vendasonlinecampanhaicv vt
          WHERE vt.ip::text = vc.ip::text AND vt.tela::text = vc.tela::text AND vt.link::text = vc.link::text AND vt.contrato IS NULL AND vt.vendaavulsa IS NULL AND vt.datainiciooperacao >= COALESCE(( SELECT max(aux.datainiciooperacao) AS max
                   FROM vendasonlinecampanhaicv aux
                  WHERE aux.ip::text = vc.ip::text AND aux.tela::text = vc.tela::text AND aux.link::text = vc.link::text AND (aux.contrato IS NOT NULL OR aux.vendaavulsa IS NOT NULL) AND aux.datainiciooperacao < vc.datainiciooperacao), ( SELECT min(aux.datainiciooperacao) AS min
                   FROM vendasonlinecampanhaicv aux
                  WHERE aux.contrato IS NULL AND aux.vendaavulsa IS NULL AND aux.ip::text = vc.ip::text AND aux.tela::text = vc.tela::text AND aux.link::text = vc.link::text)) AND vt.datainiciooperacao <= vc.datainiciooperacao) AS totaltentativa,
     vc.link
     ,sum(mprod.valorfaturado) as valortotal
        from movproduto mprod
                  inner join empresa emp on mprod.empresa = emp.codigo
                  inner join produto prod on mprod.produto = prod.codigo
--                  left join vendaavulsa va on mprod.vendaavulsa = va.codigo
--                  left join movpagamento mp on mp.codigo = (select max(movpagamento) from pagamentomovparcela pmp
--                                                              inner join movprodutoparcela mpp on pmp.movparcela = mpp.movparcela
--                                                            where mpp.movproduto = mprod.codigo)
--                  left join formapagamento fp on fp.codigo = mp.formapagamento
--                  left join cliente cli ON cli.pessoa = mprod.pessoa
--                  left join pix px on px.codigo = (select max(pm.pix) from pixmovparcela pm
--                                                                inner join movprodutoparcela mpp on mpp.movparcela = pm.movparcela
--                                                   where mpp.movproduto = mprod.codigo)
--                  left join boleto bp on bp.codigo = (select max(bm.boleto) from boletomovparcela bm
--                                                                                     inner join boleto bol on bol.codigo = bm.boleto
--                                                                                     inner join movprodutoparcela mpp on mpp.movparcela = bm.movparcela
--                                                      where bol.situacao in (3,4,5) and mpp.movproduto = mprod.codigo)
--                  left join conveniocobranca ccb on ccb.codigo = bp.conveniocobranca
--                  left join autorizacaocobrancacliente auc on auc.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 1)
--                  left join conveniocobranca acc on acc.codigo = auc.conveniocobranca
--                  left join autorizacaocobrancacliente aub on aub.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 3)
--                  left join conveniocobranca abc on abc.codigo = aub.conveniocobranca
--                  left join movpagamento mpu on mpu.codigo = (select max(mp1.codigo) from movpagamento mp1 where mp1.pessoa = mprod.pessoa)
--                  left join formapagamento fpu on fpu.codigo = mpu.formapagamento
              --
                  left join contrato c on c.codigo = mprod.contrato
                  left join pessoa on pessoa.codigo = mprod.pessoa 
			      LEFT JOIN plano ON plano.codigo = c.plano
    			  LEFT JOIN evento e ON e.codigo = c.evento
			      LEFT JOIN usuario u ON u.codigo = c.responsavelcontrato
--			      LEFT JOIN cidade cid ON cid.codigo = emp.cidade
--                  LEFT JOIN estado est ON est.codigo = emp.estado
			      left join questionariocliente qq on qq.codigo = (select max(q.codigo) from questionariocliente q 
														             JOIN questionario q2 ON q.questionario = q2.codigo AND q2.tipoquestionario::text = 'PL'::text
														             JOIN cliente ON cliente.codigo = q.cliente
														              WHERE cliente.pessoa = c.pessoa AND q.data::date >= (c.datalancamento - '1 mon'::interval)::date AND q.data::date <= c.datalancamento)
			      left join colaborador c2 on c2.codigo = qq.consultor 
			      left join pessoa pc on pc.codigo = c2.pessoa 
			      left join evento e2 on e2.codigo = qq.evento 
				  LEFT JOIN vendasonlinecampanhaicv vc ON vc.codigo = ( SELECT max(codigo) FROM vendasonlinecampanhaicv WHERE contrato = c.codigo)
         WHERE prod.tipoproduto not in ('DE', 'DR', 'MC', 'CC','RD','DV','DC')
           and mprod.datalancamento::date  between '2023-01-01' and '2099-04-14'
         group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27
         ) sql
-- where sql.data::date  between '2023-06-01' and '2099-04-14'
WHERE sql.data::date BETWEEN (CURRENT_DATE - INTERVAL '13 months') AND CURRENT_DATE
--group by 1,2
 