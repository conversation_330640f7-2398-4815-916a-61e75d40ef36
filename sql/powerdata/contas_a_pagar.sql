WITH PlanoContasAgrupado AS (
    SELECT
        mcr.movconta AS movconta_codigo,
        /* concatena os códigos de plano de contas distintos num único campo */
        STRING_AGG(DISTINCT pc.codigoplanocontas::text, ', ') AS planocontas,
        /* concatena os nomes de plano de contas distintos num único campo */
        STRING_AGG(DISTINCT pc.nome, ', ') AS nome_planocontas
    FROM movcontarateio mcr
    LEFT JOIN planoconta pc
           ON pc.codigo = mcr.planoconta
    GROUP BY mcr.movconta
)
SELECT
    "movConta.codigo" AS codigo,
    "movConta.datavencimento" AS datavencimento,
    "movConta.dataquitacao" AS dataquitacao,
    "movConta.datacompetencia" AS datacompetencia,
    cod_empresafinanceiro,
    "movConta.pessoa" AS codigo_favorecido,
    pessoa_nome AS nome_favorecido,
    "movConta.tipooperacao" AS tipo,
    "movConta.lote" AS lote,
    "movConta.descricao" AS descricao,
    "movConta.conta" AS conta,
    "movConta.valor" AS valorprevisto,
    movconta_valor_previsto_auditado AS valor_previsto_auditado,
    movconta_valorpago_auditado AS valor_pago_auditado,
    COALESCE("movConta.valorpago", 0) AS valorpago,
    "movConta.valor" - COALESCE("movConta.valorpago", 0) AS valorsaldo,
    planocontas AS codigo_planoscontas,
    nome_planocontas,
    nome_planocontas_individual, -- Nova coluna adicionada
    1 AS total
FROM
    (
    SELECT
        DISTINCT (movConta.codigo),
        pessoa.nome AS pessoa_nome,
        empresa.cod_empresafinanceiro AS cod_empresafinanceiro, 
        pc.planocontas,
        pc.nome_planocontas,
        planoconta.nome AS nome_planocontas_individual, -- Nova coluna adicionada aqui
        movConta.codigo AS "movConta.codigo",
        movConta.descricao AS "movConta.descricao",
        movConta.valor AS "movConta.valor",
        movConta.dataUltimaAlteracao AS "movConta.dataUltimaAlteracao",
        movConta.tipooperacao AS "movConta.tipooperacao",
        movConta.pessoa AS "movConta.pessoa",
        movConta.app AS "movConta.app",
        movConta.codigobarras AS "movConta.codigobarras",
        movConta.usuario AS "movConta.usuario",
        movConta.empresa AS "movConta.empresa",
        movConta.conta AS "movConta.conta",
        movConta.observacoes AS "movConta.observacoes",
        movConta.dataquitacao AS "movConta.dataquitacao",
        movConta.datalancamento AS "movConta.datalancamento",
        movConta.datacompetencia AS "movConta.datacompetencia",
        movConta.datavencimento AS "movConta.datavencimento",
        movConta.agendamentoFinanceiro AS "movConta.agendamentoFinanceiro",
        movConta.nrParcela AS "movConta.nrParcela",
        movConta.lote AS "movConta.lote",
        movConta.contaorigem AS "movConta.contaorigem",
        movConta.apresentarnocaixa AS "movConta.apresentarnocaixa",
        movConta.nfseemitida AS "movConta.nfseemitida",
        movConta.lotePagouConta AS "movConta.lotePagouConta",
        movConta.conjuntopagamento AS "movConta.conjuntopagamento",
        movConta.numerodocumento AS "movConta.numerodocumento",
        movConta.valorpago AS "movConta.valorpago",
        movConta.valororiginalalterado AS "movConta.valororiginalalterado",
        movConta.retiradaAutomaticaRecebivelOrigemCancelamento AS "movConta.retiradaAutomaticaRecebivelOrigemCancelamento",
        ---- Valor Pago Auditado:
        CASE
            WHEN movconta.valorpago = 0 THEN movconta.valorpago
            WHEN (
                SELECT COUNT(cmr.movconta)
                FROM movcontarateio cmr
                WHERE cmr.movconta = movconta.codigo
            ) > 1 
            THEN colunasmovcontaRateio.valor
            ELSE movconta.valorpago
        END AS movconta_valorpago_auditado,
        ---- Valor Previsto Auditado
        CASE
            WHEN (
                SELECT COUNT(cmr.movconta)
                FROM movcontarateio cmr
                WHERE cmr.movconta = movconta.codigo
            ) > 1 
            THEN colunasmovcontaRateio.valor
            ELSE movconta.valororiginalalterado
        END AS movconta_valor_previsto_auditado
    FROM
        movconta movConta
    LEFT JOIN conta conta ON
        conta.codigo = movConta.conta
    INNER JOIN movcontarateio colunasMovContaRateio ON
        colunasMovContaRateio.movconta = movConta.codigo
    LEFT JOIN planoconta planoconta ON -- Nova junção adicionada
        planoconta.codigo = colunasMovContaRateio.planoconta
    LEFT JOIN PlanoContasAgrupado pc ON
        colunasMovContaRateio.movconta = pc.movconta_codigo
    INNER JOIN empresa empresa ON
        empresa.codigo = movConta.empresa
    LEFT JOIN pessoa pessoa ON
        pessoa.codigo = movConta.pessoa
    LEFT JOIN lote lote ON
        lote.codigo = movConta.lote
    LEFT JOIN (
        SELECT
            caixamovconta1.movconta,
            MIN(caixamovconta1.codigo) AS caixamovconta_codigo
        FROM
            caixamovconta caixamovconta1
        GROUP BY
            caixamovconta1.movconta 
    ) AS maisantigo_caixamovconta ON
        maisantigo_caixamovconta.movconta = movConta.codigo
    LEFT JOIN caixa caixa ON
        caixa.codigo = maisantigo_caixamovconta.caixamovconta_codigo
    INNER JOIN usuario usuario ON
        usuario.codigo = movConta.usuario
    LEFT JOIN colaborador colaborador ON
        colaborador.codigo = usuario.colaborador
    LEFT JOIN pessoa colaboradorPessoa ON
        colaboradorPessoa.codigo = colaborador.pessoa
    LEFT JOIN nfseemitida nfseemitida ON
        movconta.codigo = nfseemitida.movconta
    WHERE
        1 = 1
		AND movConta.tipooperacao = 1
		--and movconta.datavencimento BETWEEN '2025-05-06' AND '2025-05-06'
		--and movconta.dataquitacao IS NULL
        --AND movConta.tipooperacao = 1
    ) AS totalizacao