select *
from (select rep.datapres<PERSON><PERSON>,
            COALESCE(ac.diaaula, a.datacomparecimento) as datapresencaaulacoletiva,
            a.codigo,
            a.dataagendamento,
            a.hora,
            a.minuto,
            a.tipoa<PERSON>o,
            a.colaborador<PERSON>ponsavel,
            a.cliente,
            a.indicado,
            a.passivo,
            a.datacomparecimento,
            a.responsavelca<PERSON>tro,
            pe.nome                                    as clientenome,
            c.matricula,
            usur.nome                                  as responsavelcadastronome,
            pe.codigo                                  as codigopessoa,
            i.nomeindicado,
            p.nome                                     as nomepassivo,
            p.telefoneresidencial                      as telefoneresidencialpassivo,
            p.telefonecelular                          as telefonecelularpassivo,
            p.telefonetrabalho                         as telefonetrabalhopassivo,
            us.nome                                    as colaboradorresponsavelnome,
            con.codigo                                 as contrato,
            s.codigocontrato,
            s.situacao                                 as situacaoCliente,
            s.situacaocontrato                         as situacaoContrato,
            pescol.nome                                as consultor,
            resp.nome                                  as responsavel,
            a.data<PERSON>,
            a.gym<PERSON>,
            a.empresa,
            COALES<PERSON>(ht.professor, htrep.professor)    as codigo<PERSON><PERSON><PERSON><PERSON><PERSON>,
            a.tipoprofessor,
            cliente_passivo.codigo                     as codigoclientepassivo,
            cliente_indicado.codigo                    as codigoclienteindicado,
            pesprof.nome                               as nomeprofessor,
            (select codigo
             from contrato c
             where pessoa = pe.codigo
               and con.codigo = c.codigo
               and con.datalancamento::date >= a.dataagendamento::date
             limit 1)                                  as contratoConvertido,
            (select pes.nome
             from colaborador col
                      inner join pessoa pes on col.pessoa = pes.codigo
             where col.codigo = a.codigoprofessor
             limit 1)                                  as professorCRM
     from agenda a
              left join usuario us ON us.codigo = a.colaboradorresponsavel
              left join usuario usur ON usur.codigo = a.responsavelcadastro
              left join cliente c on c.codigo = a.cliente
              left join pessoa pe on pe.codigo = c.pessoa
              left join situacaoclientesinteticodw s on s.codigopessoa = c.pessoa
              left join contrato con ON con.codigo = s.codigocontrato
              left join passivo p on p.codigo = a.passivo
              left join cliente cliente_passivo on cliente_passivo.codigo = p.cliente
              left join indicado i on i.codigo = a.indicado
              left join cliente cliente_indicado on cliente_indicado.codigo = i.cliente
              left join colaborador col ON con.consultor = col.codigo
              left join pessoa pescol ON pescol.codigo = col.pessoa
              left join usuario resp ON con.responsavelcontrato = resp.codigo
              left join reposicao rep ON rep.codigo = a.reposicao
              left join alunohorarioturma aht on a.alunohorarioturma = aht.codigo
              left join aulaconfirmada ac
                        on aht.horarioturma = ac.horario and aht.cliente = ac.cliente and aht.dia::date = ac.diaaula::date
              left join horarioturma htrep ON htrep.codigo = rep.horarioturma
              left join horarioturma ht ON ht.codigo = aht.horarioturma
              left join colaborador prof ON prof.codigo = COALESCE(ht.professor, htrep.professor)
              left join pessoa pesprof ON pesprof.codigo = prof.pessoa
     where 1 = 1
       AND NOT EXISTS
         (SELECT 1 FROM periodoacessocliente pac WHERE pac.pessoa = pe.codigo AND pac.tipoTotalPass IS TRUE)
      --  AND a.empresa = 1
       and not gympass) as sql
where 1 = 1
ORDER BY dataagendamento, hora, codigo

