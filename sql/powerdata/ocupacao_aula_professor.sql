select date_trunc('month', CURRENT_DATE) as inicio_periodo_aulas, CURRENT_DATE::date::timestamp as fim_periodo_aulas, f.*, 
case 
 when f.vagas = 0 then 0.0
 when f.vagas > 0 then (f.ocupadas * 100.0/f.vagas) end as freq_media from (
select h.codigo as codigohora<PERSON>,  
t.identificador as turma, p.nome as professor,
a.desc<PERSON><PERSON> as ambiente, 
h.horainicial, 
h.horafinal ,
CASE
WHEN h.diasemana = 'DM' THEN 'Domingo'
WHEN h.diasemana = 'SG' THEN 'Segunda'
WHEN h.diasemana = 'TR' THEN 'Terca'
WHEN h.diasemana = 'QA' THEN 'Quarta'
WHEN h.diasemana = 'QI' THEN 'Quinta'
WHEN h.diasemana = 'SX' THEN 'Sexta'
ELSE 'Sabado' end as diasemana,
h.nrmaximoaluno as vagas,
( SELECT COUNT(ma.codigo) FROM matriculaalunohorarioturma ma 
 WHERE ma.horarioturma in (h.codigo)  AND ((datafim >= date_trunc('month', CURRENT_DATE) AND datafim <= CURRENT_DATE) 
 OR (datainicio >= date_trunc('month', CURRENT_DATE) AND datainicio <= CURRENT_DATE) 
 OR (datainicio < date_trunc('month', CURRENT_DATE) AND datafim > CURRENT_DATE))) as ocupadas,
 (select count(codigo) from reposicao r 
 where r.horarioturma = h.codigo and r.datareposicao between date_trunc('month', CURRENT_DATE) AND CURRENT_DATE) as presenc_Prev,
  (select count(codigo) from reposicao r 
 where r.horarioturma = h.codigo and r.datapresenca between date_trunc('month', CURRENT_DATE) AND CURRENT_DATE) as presencas
 from horarioturma h
inner join ambiente a on a.codigo = h.ambiente  
inner join turma t on t.codigo = h.turma
inner join colaborador c on c.codigo = h.professor 
inner join pessoa p on p.codigo = c.pessoa 
where h.situacao = 'AT' 
and t.datafinalvigencia >= CURRENT_DATE) as f