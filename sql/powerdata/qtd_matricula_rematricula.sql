SELECT 
    DATE_TRUNC('month', qtdMatriculaRematriculaMes.dataLancamento) AS mes,
    COUNT(*) AS qtdMatriculaRematriculaMes,
    qtdMatriculaRematriculaMes.empresa AS codigoEmpresa,
    qtdMatriculaRematriculaMes.nomeEmpresa AS nomeEmpresa
FROM (
    SELECT 
        cliente.codigo AS cliente, 
        MAX(contrato.codigo) AS contrato, 
        contrato.dataLancamento,
        cliente.empresa AS empresa,
        emp.nome AS nomeEmpresa
    FROM cliente
    INNER JOIN contrato ON contrato.pessoa = cliente.pessoa
    INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa
    INNER JOIN colaborador ON colaborador.codigo = contrato.consultor
    INNER JOIN pessoa AS pescol ON colaborador.pessoa = pescol.codigo
    INNER JOIN empresa emp ON emp.codigo = cliente.empresa
    LEFT JOIN questionariocliente ON questionariocliente.cliente = cliente.codigo 
        AND questionariocliente.data >= '2024-01-01 00:00:00'::TIMESTAMP
    WHERE contrato.dataLancamento >= '2024-01-01 00:00:00'::TIMESTAMP
      AND (contrato.situacaoContrato = 'RE' OR contrato.situacaoContrato = 'MA')
      AND contrato.origemcontrato IN (1, 2)
      AND (questionariocliente.codigo IS NULL OR questionariocliente.tipobv IN (1, 2, 3))
      AND questionariocliente.origemSistema IN(1)
    GROUP BY cliente.codigo, contrato.dataLancamento, cliente.empresa, emp.nome
) AS qtdMatriculaRematriculaMes
GROUP BY mes, codigoEmpresa, nomeEmpresa
ORDER BY mes;
