-- --------------- KPIS ----------------
select
	date_trunc('day',
	parcela_datavencimento) as datavencimento,
	cod_empresafinanceiro,
	tipo_registro,
	cliente_matricula,
	-- count(distinct cliente_matricula) as qtd_clientes,
	sum(parcela_valorparcela) as valor_total,
	sum(total) as total_parcelas
from
	(
	select
		*,
		'INADIMPLENCIA' as tipo_registro
	from
		(
		select
			distinct on
			(movparcela.codigo) movparcela.codigo as movparcela_codigo,
			oo.dataoperacao::timestamp as datacancelamento,
			movparcela.pessoa as codigopessoa,
			cliente.codigo as codigoCliente,
			emp.cod_empresafinanceiro,
			case
				when necp.contrato is not null then cliev.matricula
				when ((vendaavulsa.colaborador is not null
					or controletaxapersonal.codigo is not null)
				and colaborador.codigo is not null) then 'cl-' || colaborador.codigo
				when (cliente.matricula is null
					or cliente.matricula = '') then vendaavulsa.nomecomprador || ' sem matrícula'
				else cliente.matricula
			end as cliente_matricula,
			case
				when (movparcela.vendaavulsa > 0
					and (pessoa.nome is null
						or pessoa.nome = '')) then vendaavulsa.nomecomprador
				when necp.contrato is not null then pesev.nome
				else pessoa.nome
			end as pessoa_nome 
	,
			movparcela.descricao as parcela_descricao,
			movparcela.dataregistro::timestamp as parcela_dataregistro,
			movparcela.datavencimento::timestamp as parcela_datavencimento,
			movparcela.situacao as parcela_situacao,
			movparcela.regimerecorrencia as regime_recorrencia,
			movparcela.contrato as parcela_contrato,
			movparcela.valorparcela as parcela_valorparcela,
			1 as total,
			rp.data::timestamp as datapagamento,
			array_to_string(array(
			select
				email
			from
				email
			where
				email.pessoa = movparcela.pessoa),
			',') as email,
			array_to_string(array_agg(distinct fp.descricao),
			',') as formaspagamento,
			array_to_string(array_agg(distinct mod.nome),
			',') as modalidades,
			array_to_string(array(
			select
				numero
			from
				telefone
			where
				telefone.pessoa = movparcela.pessoa),
			',') as telefone,
			emp.nome as nomeEmpresa,
			(
			select
				nrtentativas
			from
				movparcelaresultadocobranca mr
			where
				mr.movparcela = movparcela.codigo
			order by
				mr.codigo desc
			limit 1) as nrtentativas,
			(
			select
				motivoretorno
			from
				movparcelaresultadocobranca mr
			where
				mr.movparcela = movparcela.codigo
			order by
				mr.codigo desc
			limit 1) as motivoretorno,
			array_to_string((
			select
				array_agg(descricao)
			from
				conveniocobranca cc
			inner join autorizacaocobrancacliente acc on
				acc.conveniocobranca = cc.codigo
				and acc.ativa
			where
				acc.cliente = cliente.codigo),
			',') as convenios  
	,
			pessoa.cfp as cpf
	,
			(
			select
				replace((endereco || ', ' || numero || ', ' || complemento || ', ' || bairro || ', ' || bairro || ', ' || cep || ', ' || coalesce(cid.nome,
				'')|| ', ' || coalesce(estado.sigla,
				'')),
				', ,',
				',') as endereco
			from
				endereco
			inner join pessoa pes on
				pes.codigo = endereco.pessoa
			left join cidade cid on
				cid.codigo = pes.cidade
			left join estado on
				estado.codigo = cid.estado
			where
				pes.codigo = movparcela.pessoa
			limit 1) as endereco
	,
			plano.descricao as nomeplano
		from
			movparcela
		left join observacaooperacao oo on
			oo.movparcela = movparcela.codigo
			and oo.tipooperacao = 'PC'
		left join empresa emp on
			emp.codigo = movparcela.empresa
		left join pessoa on
			pessoa.codigo = movparcela.pessoa
		left join cliente on
			cliente.pessoa = pessoa.codigo
		left join colaborador on
			colaborador.pessoa = pessoa.codigo
		left join contrato on
			movparcela.contrato = contrato.codigo
		left join contratocondicaopagamento ccp on
			ccp.contrato = contrato.codigo
		left join condicaopagamento cp on
			cp.codigo = ccp.condicaopagamento
		left join contratomodalidade cmod on
			cmod.contrato = contrato.codigo
		left join modalidade mod on
			cmod.modalidade = mod.codigo
		left join aulaavulsadiaria on
			aulaavulsadiaria.codigo = movparcela.aulaavulsadiaria
		left join vendaavulsa on
			vendaavulsa.codigo = movparcela.vendaavulsa
		left join controletaxapersonal on
			controletaxapersonal.codigo = movparcela.personal
		left join usuario on
			usuario.codigo = movparcela.responsavel
		left join negociacaoeventocontratoparcelas necp on
			necp.parcela = movparcela.codigo
		left join pessoa pesev on
			pesev.codigo = movparcela.pessoa
		left join cliente cliev on
			cliev.pessoa = pesev.codigo
		left join movprodutoparcela mpp on
			mpp.movparcela = movparcela.codigo
		left join recibopagamento rp on
			mpp.recibopagamento = rp.codigo
		left join pagamentomovparcela pmp on
			movparcela.codigo = pmp.movparcela
		left join movpagamento mpag on
			pmp.movpagamento = mpag.codigo
		left join formapagamento fp on
			mpag.formapagamento = fp.codigo
		left join contratohorario chor on
			chor.contrato = movparcela.contrato
		left join plano on
			plano.codigo = contrato.plano
		where
			1 = 1
			and movparcela.empresa = 1
			and (oo.dataoperacao >= movparcela.datavencimento
				or movparcela.situacao != 'CA')
			and (movparcela.situacao in ('EA', 'CA'))
			and movparcela.situacao != 'RG'
		group by
			pessoa.cfp,
			rp.data,
			nrtentativas,
			motivoretorno,
			oo.dataoperacao,
			movparcela_codigo,
			cliente.codigo,
			emp.cod_empresafinanceiro,
			movparcela.pessoa,
			cliente_matricula,
			pessoa_nome,
			parcela_descricao,
			parcela_dataregistro,
			parcela_datavencimento,
			parcela_situacao,
			parcela_contrato,
			parcela_valorparcela,
			regime_recorrencia,
			datapagamento,
			emp.nome,
			plano.descricao
		order by
			movparcela.codigo,
			pessoa_nome) as inad
union all
	select
		*,
		'PREVISAO' as tipo_registro
	from
		(
		select
			distinct on
			(movparcela.codigo) movparcela.codigo as movparcela_codigo,
			oo.dataoperacao::timestamp as datacancelamento,
			movparcela.pessoa as codigopessoa,
			cliente.codigo as codigoCliente,
			emp.cod_empresafinanceiro,
			case
				when necp.contrato is not null then cliev.matricula
				when ((vendaavulsa.colaborador is not null
					or controletaxapersonal.codigo is not null)
				and colaborador.codigo is not null) then 'cl-' || colaborador.codigo
				when (cliente.matricula is null
					or cliente.matricula = '') then vendaavulsa.nomecomprador || ' sem matrícula'
				else cliente.matricula
			end as cliente_matricula,
			case
				when (movparcela.vendaavulsa > 0
					and (pessoa.nome is null
						or pessoa.nome = '')) then vendaavulsa.nomecomprador
				when necp.contrato is not null then pesev.nome
				else pessoa.nome
			end as pessoa_nome 
	,
			movparcela.descricao as parcela_descricao,
			movparcela.dataregistro::timestamp as parcela_dataregistro,
			movparcela.datavencimento::timestamp as parcela_datavencimento,
			movparcela.situacao as parcela_situacao,
			movparcela.regimerecorrencia as regime_recorrencia,
			movparcela.contrato as parcela_contrato,
			movparcela.valorparcela as parcela_valorparcela,
			1 as total,
			rp.data::timestamp as datapagamento,
			array_to_string(array(
			select
				email
			from
				email
			where
				email.pessoa = movparcela.pessoa),
			',') as email,
			array_to_string(array_agg(distinct fp.descricao),
			',') as formaspagamento,
			array_to_string(array_agg(distinct mod.nome),
			',') as modalidades,
			array_to_string(array(
			select
				numero
			from
				telefone
			where
				telefone.pessoa = movparcela.pessoa),
			',') as telefone,
			emp.nome as nomeEmpresa,
			(
			select
				nrtentativas
			from
				movparcelaresultadocobranca mr
			where
				mr.movparcela = movparcela.codigo
			order by
				mr.codigo desc
			limit 1) as nrtentativas,
			(
			select
				motivoretorno
			from
				movparcelaresultadocobranca mr
			where
				mr.movparcela = movparcela.codigo
			order by
				mr.codigo desc
			limit 1) as motivoretorno,
			array_to_string((
			select
				array_agg(descricao)
			from
				conveniocobranca cc
			inner join autorizacaocobrancacliente acc on
				acc.conveniocobranca = cc.codigo
				and acc.ativa
			where
				acc.cliente = cliente.codigo),
			',') as convenios  
	,
			pessoa.cfp as cpf
	,
			(
			select
				replace((endereco || ', ' || numero || ', ' || complemento || ', ' || bairro || ', ' || bairro || ', ' || cep || ', ' || coalesce(cid.nome,
				'')|| ', ' || coalesce(estado.sigla,
				'')),
				', ,',
				',') as endereco
			from
				endereco
			inner join pessoa pes on
				pes.codigo = endereco.pessoa
			left join cidade cid on
				cid.codigo = pes.cidade
			left join estado on
				estado.codigo = cid.estado
			where
				pes.codigo = movparcela.pessoa
			limit 1) as endereco
	,
			plano.descricao as nomeplano
		from
			movparcela
		left join observacaooperacao oo on
			oo.movparcela = movparcela.codigo
			and oo.tipooperacao = 'PC'
		left join empresa emp on
			emp.codigo = movparcela.empresa
		left join pessoa on
			pessoa.codigo = movparcela.pessoa
		left join cliente on
			cliente.pessoa = pessoa.codigo
		left join colaborador on
			colaborador.pessoa = pessoa.codigo
		left join contrato on
			movparcela.contrato = contrato.codigo
		left join contratocondicaopagamento ccp on
			ccp.contrato = contrato.codigo
		left join condicaopagamento cp on
			cp.codigo = ccp.condicaopagamento
		left join contratomodalidade cmod on
			cmod.contrato = contrato.codigo
		left join modalidade mod on
			cmod.modalidade = mod.codigo
		left join aulaavulsadiaria on
			aulaavulsadiaria.codigo = movparcela.aulaavulsadiaria
		left join vendaavulsa on
			vendaavulsa.codigo = movparcela.vendaavulsa
		left join controletaxapersonal on
			controletaxapersonal.codigo = movparcela.personal
		left join usuario on
			usuario.codigo = movparcela.responsavel
		left join negociacaoeventocontratoparcelas necp on
			necp.parcela = movparcela.codigo
		left join pessoa pesev on
			pesev.codigo = movparcela.pessoa
		left join cliente cliev on
			cliev.pessoa = pesev.codigo
		left join movprodutoparcela mpp on
			mpp.movparcela = movparcela.codigo
		left join recibopagamento rp on
			mpp.recibopagamento = rp.codigo
		left join pagamentomovparcela pmp on
			movparcela.codigo = pmp.movparcela
		left join movpagamento mpag on
			pmp.movpagamento = mpag.codigo
		left join formapagamento fp on
			mpag.formapagamento = fp.codigo
		left join contratohorario chor on
			chor.contrato = movparcela.contrato
		left join plano on
			plano.codigo = contrato.plano
		where
			1 = 1
			and movparcela.empresa = 1
			and (oo.dataoperacao >= movparcela.datavencimento
				or movparcela.situacao != 'CA')
			and (movparcela.situacao in ('EA', 'PG', 'CA'))
				and movparcela.situacao != 'RG'
			group by
					pessoa.cfp,
					rp.data,
					nrtentativas,
					motivoretorno,
					oo.dataoperacao,
					movparcela_codigo,
					cliente.codigo,
					emp.cod_empresafinanceiro,
					movparcela.pessoa,
					cliente_matricula,
					pessoa_nome,
					parcela_descricao,
					parcela_dataregistro,
					parcela_datavencimento,
					parcela_situacao,
					parcela_contrato,
					parcela_valorparcela,
					regime_recorrencia,
					datapagamento,
					emp.nome,
					plano.descricao
			order by
					movparcela.codigo,
					pessoa_nome
) as prev) as sql
group by
	1,
	2,
	3,
	4