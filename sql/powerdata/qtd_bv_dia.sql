SELECT
    QuestionarioCliente.codigo AS codigo_questionario,
    DATE_TRUNC('day', questionariocliente.data)::TIMESTAMP AS dia,
    cliente.empresa AS codigoEmpresa,
    emp.cod_empresafinanceiro,
    emp.nome AS nomeEmpresa,
    categoria.nome AS nome_categoria
FROM QuestionarioCliente
    INNER JOIN cliente ON cliente.codigo = questionariocliente.cliente
    INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa
    INNER JOIN questionario ON questionario.codigo = questionariocliente.questionario
    INNER JOIN colaborador ON colaborador.codigo = questionariocliente.consultor
    INNER JOIN pessoa AS pescol ON colaborador.pessoa = pescol.codigo
    INNER JOIN empresa emp ON emp.codigo = cliente.empresa
    LEFT JOIN categoria ON cliente.categoria = categoria.codigo
WHERE questionariocliente.tipobv IN (1, 2, 3)
  AND questionariocliente.origemSistema IN (1, 17)
ORDER BY dia desc;
