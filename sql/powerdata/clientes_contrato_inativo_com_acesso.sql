SELECT distinct(cliente.*)
FROM historicocontrato AS ht
INNER JOIN contrato ON contrato.codigo = ht.contrato
INNER JOIN pessoa ON pessoa.codigo = contrato.pessoa
INNER JOIN cliente ON cliente.pessoa = pessoa.codigo
INNER JOIN periodoacessocliente AS per ON per.contrato = ht.contrato
WHERE (ht.tipohistorico = 'VE' OR ht.tipohistorico = 'DE' OR ht.tipohistorico =
'CA')
AND ht.datainiciosituacao <= '2024-03-14'
AND (per.datainicioacesso <= '2024-03-14'
AND per.datafinalacesso>='2024-03-14')
AND contrato.empresa = 1