select
	p.planotipo ,
    e.nome as NomeUnidade,
    e.codigo as empresa<PERSON>od<PERSON>,
    e.cod_empres<PERSON><PERSON><PERSON><PERSON>,
    e.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
	p.codigo as codigo_plano,
	p.descricao as descricao_plano,
	ptp.descricao as modelo_contrato,
	p.vigenciade::timestamp as vigenciaInicial_plano, 
	p.vigenciaate::timestamp as vigenciaFinal_plano,
	case when (p.vigenciade::date > current_date) and (p.vigenciaate::date > current_date) then 'Futuro' 
		 when (p.ingressoate::date <= current_date) and (p.vigenciaate::date >= current_date) then 'So Renovação'
         when (p.vigenciade::date < current_date) and (p.vigenciaate::date < current_date) then 'Inativo' 
	     else 'Vigente' 
	end situacao_plano,
	p.ingressoate::timestamp as ingressoAte_plano,
--
	p2.descricao  as produto_plano,
	pac_e.descricao  as pacote_excecao_plano,
	mod_e.nome as modalidade_excecao_plano,
	pe.vezessemana as vezessemana_excecao_plano,
	pe.duracao as duracao_excecao_plano,
	pe.horario as horario_excecao_plano,
	pe.valor as valor_excecao_plano,
	mo.nome as modalidade_plano,
	pm.listavezessemana as vezes_semana_modalidade_plano,
	comp.descricao as pacote_plano,
	pd.numeromeses as numero_meses_duracao_plano,
	pd.valordesejadomensal  as valor_desejado_duracao_plano,
	pd.carencia as ferias_duracao_plano,
	hor.descricao as horario_plano,	
	pr.taxaadesao as taxa_adesao_plano_recorrencia,
	pr.valoranuidade as anuidade_plano_recorrencia,
	pr.valormensal as valor_mensal_plano_recorrencia,
	pr.diaanuidade as dia_anuidade_plano_recorrencia,
	pr.mesanuidade as mes_anuidade_plano_recorrencia,
	pr.duracaoplano as fidelidade_plano_recorrencia,
	pr.renovavelautomaticamente	as renovavel_automaticamente_plano_recorrencia
from plano p
left join planoexcecao pe on p.codigo = pe.plano
left join modalidade mod_e on pe.modalidade = mod_e.codigo 
left join composicao pac_e on pe.pacote = pac_e.codigo
left join horario hor_e on pe.horario = hor_e.codigo 
left join planomodalidade pm on p.codigo = pm.codigo
left join planomodalidadevezessemana pmvs on pm.codigo = pmvs.planomodalidade
left join modalidade mo on pm.modalidade = mo.codigo 
left join planocomposicao pc on p.codigo = pc.plano
left join composicao comp on pc.composicao = comp.codigo
left join planotextopadrao ptp on p.planotextopadrao = ptp.codigo
left join planoduracao pd on p.codigo = pd.plano
left join planohorario ph on p.codigo = ph.plano
left join horario hor on ph.horario = hor.codigo 
left join planorecorrencia pr on p.codigo = pr.plano
left join empresa e on p.empresa = e.codigo
left join produto p2 on p2.codigo = p.produtopadraogerarparcelascontrato 
--where p.descricao like '%111%'