-- Historico de produtos do Estoque --------------
select
    hpe.codigo as id_primario,
    e.cod_empresafinance<PERSON>, 
	p.codigo as produto_codigo,
	p.descricao as produto_descricao,
	pe.estoque as estoque_atual,
	pe.estoque<PERSON><PERSON> as estoque_minimo_atual,
	p.valorfinal as valor_atual,
	hpe.dataalteracao as dia,
	case when hpe.entidade = 'MOVPRODUTO' then 'VENDA_PRODUTO' else hpe.entidade end as operacao_dia,
	hpe.saldoanterior as saldo_anterior_dia,
	hpe.saldoatual  as saldo_atual_dia,
	(select precounitario from movproduto where produto = p.codigo and datalancamento::date = hpe.dataalteracao::date order by datalancamento desc limit 1) as valor_dia
from produto p
	inner join produtoestoque pe on pe.produto = p.codigo
	left join empresa e on e.codigo = pe.empresa 
	left join historicoprodutoestoque hpe on hpe.produtoestoque = pe.codigo 	
order by hpe.codigo 
--order by p.codigo,hpe.dataalteracao
--limit 20;