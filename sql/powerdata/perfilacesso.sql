-- O que cada usuario pode fazer. Nota: não está vincula a nenhuma empresa então eles são da chave.
select 
pa.codigo, pa.nome, p.tituloapresentacao, p.permissoes, 
pa.replicarredeempresa, 
pa.porcetagemdescontocontrato as desconto,
pa.unificado ,
pa.co<PERSON>per<PERSON>lt<PERSON>ino,
CASE
   WHEN p.permissoes = '(0)(9)(1)' THEN 'Incluir e Consultar'
   WHEN p.permissoes = '(0)' THEN 'Consultar'
   WHEN p.permissoes = '(12)' THEN 'Relatorio'
   WHEN p.permissoes = '(2)' THEN 'Alterar'
   WHEN p.permissoes = '(0)(1)(2)(9)(12)' THEN 'Total (Sem Excluir)'
   WHEN p.permissoes = '(0)(1)(2)(3)(9)(12)' THEN 'Total'
   WHEN p.permissoes = '(3)' THEN 'Excluir'
   WHEN p.permissoes = '(1)(9)' THEN 'Incluir'
   ELSE p.permissoes
END AS descricao_permissao
from perfilacesso pa
  inner join permissao p on p.codperfilacesso = pa.codigo;