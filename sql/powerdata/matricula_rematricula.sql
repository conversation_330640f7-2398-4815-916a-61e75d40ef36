WITH 
  min_data AS (
    SELECT DATE_TRUNC('month', MIN(data)) AS start_month
    FROM questionariocliente
  ),

meses AS (
    SELECT generate_series(
      (SELECT start_month FROM min_data),
      DATE_TRUNC('month', CURRENT_DATE),
      INTERVAL '1 month'
    ) AS inicio_mes
  ),
  
matriculas as (
select
	emp.nome as nomeEmpresa,
	meses.inicio_mes as mes,
	cliente.codigo cliente,
	cliente.empresa as codigoempresa,
	pessoa.sexo as sexo,
	contrato.situacaoContrato as contrato_situacao,
	max(contrato.codigo) contrato
from meses
left join questionariocliente on
	date_trunc('month', questionariocliente.data) = date_trunc('month', meses.inicio_mes)
inner join	cliente on
	questionariocliente.cliente = cliente.codigo
inner join contrato on
	contrato.pessoa = cliente.pessoa
	and date_trunc('month', contrato.datalancamento) = date_trunc('month', meses.inicio_mes)
inner join pessoa on
	pessoa.codigo = cliente.pessoa
inner join colaborador on
	colaborador.codigo = contrato.consultor
inner join pessoa as pescol on
	colaborador.pessoa = pescol.codigo
left join empresa emp on
	emp.codigo = contrato.empresa

where 1 = 1
	and contrato.origemcontrato in (1, 2)
	and (questionariocliente.codigo = null
		or questionariocliente.tipobv in (1, 2, 3))
	and questionariocliente.origemSistema in(1, 17)
group by
	1, 2, 3, 4, 5, 6)	
select codigoempresa, nomeEmpresa, mes, contrato_situacao, sexo, count(cliente) as quantidade from matriculas
group by 1, 2, 3, 4, 5
order by mes desc