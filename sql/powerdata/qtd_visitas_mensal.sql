SELECT 
    DATE_TRUNC('month', questionariocliente.data) AS mes,
    COUNT(*) AS qtdBvMes,
    cliente.empresa AS codigoEmpresa,
    emp.nome AS nomeEmpresa
FROM QuestionarioCliente
INNER JOIN cliente ON cliente.codigo = questionariocliente.cliente
INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa
INNER JOIN questionario ON questionario.codigo = questionariocliente.questionario
INNER JOIN colaborador ON colaborador.codigo = questionariocliente.consultor
INNER JOIN pessoa AS pescol ON colaborador.pessoa = pescol.codigo
INNER JOIN empresa emp ON emp.codigo = cliente.empresa
WHERE questionariocliente.data >= '2023-01-01 00:00:00'::TIMESTAMP
  AND questionariocliente.tipobv IN (1)
  AND cliente.gympassuniquetoken IS NULL
  AND questionariocliente.origemSistema IN(1)
GROUP BY mes, cliente.empresa, emp.nome
ORDER BY mes;
