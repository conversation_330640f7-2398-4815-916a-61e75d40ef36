select
    sql.*,
    case
        when (sql.usuario ilike 'ADMINISTRADOR' and sql.justificativa ilike '%- vendas online%') then 'AUTOMATICO_VENDAS_ONLINE'
        when (sql.usuario ilike 'ADMINISTRADOR' or sql.usuario ilike 'RECOR%') then 'AUTOMATICO'
        else 'MANUAL' end as operacao_forma
from (SELECT
          'ESTORNO_CONTRATO' as operacao_tipo,
          emp.cod_empresafinanceiro as empresa_financeiro,
          l.pessoa,
          e.contrato,
          cast(replace(replace(split_part(split_part(regexp_replace(l.valorcampoalterado, E'[\\n\\r]+', ' ', 'g' ), 'Valor Contrato = R$' ,2),' ',1), '.',''), ',', '.') as real) as contrato_valor,
          split_part(split_part(regexp_replace(l.valorcampoalterado, E'[\\n\\r]+', ' ', 'g' ), 'Plano = ' ,2),'Respon',1) as contrato_plano,
          l.dataalteracao as contrato_dt,
          e.dataestorno as operacao_dt,
          e.usuarioresponsavel as usuario,
          e.justificativa as justificativa
      FROM estornoobservacao e
               inner join empresa emp on emp.codigo = e.empresa
               LEFT JOIN log l ON l.chaveprimaria = e.contrato::text AND l.operacao ilike 'INCLUSÃO%' AND l.nomeentidade = 'CONTRATO'
      UNION all
      SELECT
          'CANCELAMENTO_CONTRATO' as operacao_tipo,
          emp.cod_empresafinanceiro as empresa_financeiro,
          c.pessoa,
          co.contrato,
          c.valorfinal as contrato_valor,
          pl.descricao as contrato_plano,
          c.datalancamento as contrato_dt,
          co.dataoperacao as operacao_dt,
          u.nome as usuario,
          co.observacao as justificativa
      FROM contratooperacao co
          INNER JOIN contrato c ON c.codigo = co.contrato
          INNER JOIN empresa emp ON emp.codigo = c.empresa
          INNER JOIN usuario u ON u.codigo = co.responsavel
          INNER JOIN plano pl on pl.codigo = c.plano
      WHERE co.tipooperacao LIKE 'CA'
     ) as sql