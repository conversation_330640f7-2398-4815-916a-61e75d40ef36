 --(Consulta do Relatório de Indicação - Somente Totalizadores)
SELECT
SUM(qtdindicacao) AS qtdindicacao,
SUM(qtdconvertido) as qtdconvertido FROM(
SELECT
0 AS qtdIndicacao,
COUNT(i.codigo) AS qtdConvertido
FROM indicacao i
INNER JOIN indicado ind ON ind.indicacao = i.codigo
LEFT JOIN cliente ci ON ci.codigo = ind.cliente
LEFT JOIN pessoa pi ON pi.codigo = ci.pessoa
LEFT JOIN contrato con ON con.pessoa = pi.codigo
WHERE 1 = 1
AND con.datalancamento::date >= i.dia::date
AND con.datalancamento::date < i.dia::date + 30
AND con.situacaocontrato IN ('MA', 'RE')
AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = con.codigo)
AND i.dia >= '2024-01-01 00:00:00.0'
UNION
SELECT
COUNT(i.codigo) AS qtdIndicacao,
0 AS qtdConvertido
FROM indicacao i
INNER JOIN indicado ind ON ind.indicacao = i.codigo
WHERE 1 = 1
AND i.dia >= '2024-01-01 00:00:00.0'
) as foo