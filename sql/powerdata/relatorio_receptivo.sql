 --(Consulta do Relatório de RECEPTIVO detalhada)
 SELECT
 pas.codigo,  pas.nome,  pas.telefoneresidencial,  pas.telefonecelular,  pas.telefonetrabalho,  usu.nome AS colaboradorRespCadastro,  pas.dia,  pas.lead,
 emp.nome as empresa,  coalesce(evt.descricao, '') as descricaoEvento,  coalesce(hist.observacao, '') as observacao  
 FROM passivo pas  
 INNER JOIN usuario usu ON usu.codigo = pas.responsavelcadastro  
 INNER JOIN empresa emp ON emp.codigo = pas.empresa  
 LEFT JOIN evento evt ON evt.codigo = pas.evento  
 LEFT JOIN historicocontato hist ON hist.passivo = pas.codigo  WHERE 1=1
 AND emp.codigo = 1
  AND pas.dia >= '2024-09-01 00:00:00.0'
  AND pas.dia <= '2024-09-30 23:59:59.999'
  ORDER BY 1 desc