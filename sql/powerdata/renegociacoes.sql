-- Active: 1741625085864@@localhost@5432@bdzillyonengenhariadocorpomatriz@public
WITH parcelas_base AS (
  SELECT  
    mp.codigo AS codigo_movparcela,
    mp.contrato, 
    mp.pessoa AS codigo_pessoa, 
    pe.nome, 
    mp.valorparcela, 
    mp.percentualjuro,
    mp.percentualmulta,
    mp.situacao,
    mp.responsavel,
    log.responsavel as nome_responsavel,
    mp.empresa,
    e.cod_empresafinanceiro::integer,
    mp.movimentocc,
    mp.reagendada,
    mp.naorealizarcobrancaautomatica,
    mp.parcelasrenegociadas,
    mp.nrtentativas,
    mp.descricao,
    mp.datavencimento,
    mp.dataregistro,
    mp.datacobranca,
    mp.dataalteracaosituacao
  FROM movparcela mp
  LEFT JOIN pessoa pe ON mp.pessoa = pe.codigo
  INNER JOIN contrato co ON mp.contrato = co.codigo
  INNER JOIN empresa e ON mp.empresa = e.codigo
  LEFT JOIN (SELECT
  log.dataalteracao,
  log.operacao,
  pessoaCliente.nome AS nome_cliente,
  log.valorcampoalterado,
  -- Código da nova parcela
  (REGEXP_MATCHES(log.valorcampoalterado, 'Código nova parcela: (\d+)', 'g'))[1] AS codigo_nova_parcela,
  -- Valor da nova parcela
  (REGEXP_MATCHES(log.valorcampoalterado, 'Parcela no valor: ([\d,.]+)', 'g'))[1] AS valor_nova_parcela,
  -- Vencimento
  (REGEXP_MATCHES(log.valorcampoalterado, 'vencimento em: (\d{2}/\d{2}/\d{4})', 'g'))[1] AS vencimento,
  -- Responsável
  (REGEXP_MATCHES(log.valorcampoalterado, 'Responsável pelo Lançamento = ([^\n\r]+)', 'g'))[1] AS responsavel,
  -- Empresa
  (REGEXP_MATCHES(log.valorcampoalterado, 'Empresa = ([^\n\r]+)', 'g'))[1] AS empresa
FROM log
LEFT JOIN cliente 
  ON cliente.codigo = log.cliente
LEFT JOIN pessoa AS pessoaCliente 
  ON pessoaCliente.codigo = cliente.pessoa
WHERE log.nomeentidade = 'PARCELA'
  AND log.operacao = 'RENEGOCIAÇÃO - PARCELA') log ON mp.codigo = log.codigo_nova_parcela:: integer
  WHERE mp.dataregistro >= DATE '2024-01-01'
),
codigos_renegociados AS (
  -- Extrai todos os códigos de parcelas renegociadas a partir do JSON bruto
  SELECT 
    pb.contrato,
    (regexp_matches(pb.parcelasrenegociadas, '"codigo"\s*:\s*(\d+)', 'g'))[1]::integer AS codigo_movparcela_renegociada
  FROM parcelas_base pb
  WHERE pb.parcelasrenegociadas IS NOT NULL AND pb.parcelasrenegociadas != ''
),
parcelas_com_tipo AS (
  SELECT 
    pb.*,
    CASE 
      WHEN EXISTS (
        SELECT 1 
        FROM codigos_renegociados cr 
        WHERE cr.codigo_movparcela_renegociada = pb.codigo_movparcela
      ) THEN 'RENEGOCIADA'
      WHEN pb.parcelasrenegociadas IS NOT NULL AND pb.parcelasrenegociadas != '' THEN 'ATUAL'
      ELSE 'OUTROS'
    END AS tipo_parcela
  FROM parcelas_base pb
)
SELECT 
  CONCAT(contrato, '_', TO_CHAR(dataregistro, 'YYYYMMDD')) AS id_renegociacao,
  *
FROM parcelas_com_tipo
WHERE tipo_parcela != 'OUTROS'
ORDER BY id_renegociacao
