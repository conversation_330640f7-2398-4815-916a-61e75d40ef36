SELECT 
    a.codigo AS codigo,
    a.cliente_codigo,
    a.massama<PERSON>,
    a.data<PERSON>lia<PERSON>,
    a.protocol<PERSON>,
    a.categoriaavaliacaoimc,
    a.agendamentoreavaliacao_codigo,
    a.fcastrand5,
    a.cintura,
    a.idademetabolica,
    a.fc<PERSON><PERSON>,
    a.resistencia,
    a.vo2max<PERSON><PERSON>,
    a.responsavellancamento_codigo,
    a.fcastrand,
    a.peso,
    a.fcastrand4,
    a.vo2max2400,
    a.imc,
    a.percentualmassamagra,
    a.metapercentualgordura,
    a.flexibilidade,
    a.circunferenciaabdominal,
    a.vomaxaerobico,
    a.movproduto,
    a.tempo2400,
    a.necessidadefisica,
    a.categoriapercentualgordura,
    a.tmb,
    a.necessidadecalorica,
    a.aumentopercentualgordura,
    a.dataproxima,
    a.altura,
    a.gorduraideal,
    a.massago<PERSON>,
    a.protocolo,
    a.percentualgordura,
    a.pesomuscular,
    a.gord<PERSON><PERSON>,
    a.metapercentual<PERSON>raanterior,
    a.recomenda<PERSON><PERSON>,
    a.fc<PERSON><PERSON>,
    c.nome AS nome_cliente,
    c.codigo AS codigo_empresa
FROM 
    "avaliacaofisica" a
INNER JOIN 
    clientesintetico c 
ON 
    c.codigo = a.cliente_codigo;
