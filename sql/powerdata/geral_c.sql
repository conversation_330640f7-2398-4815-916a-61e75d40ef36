--select s_nomecliente,count(*) from ( 
select
  concat(e.cod_empresafinanceiro,'-',s.codigo<PERSON><PERSON><PERSON>)  	as <PERSON><PERSON>_<PERSON><PERSON><PERSON>,
  concat(e.cod_empresafinance<PERSON>,'-',c.codigo)  		as I<PERSON>_<PERSON><PERSON><PERSON>,  
  e.nome                       as e_nome,
  e.cod_empresafinanceiro      as e_empresafinanceiro,
  e.codigo                     as e_codigo,
  s.codigo                     as s_codigo,
  s.codigocliente              as s_codigocliente,
  s.matricula 				   as s_matricula,
  s.nomecliente 			   as s_nomecliente,
  s.codigo<PERSON>tra<PERSON>             as s_codigocontrato,
  s.codigo<PERSON><PERSON>a               as s_codigopessoa,
  s.sexocliente                as s_sexocliente,
  s.datanascimento             as s_datanascimento,
  s.valorfaturadocontrato      as s_valorfaturadocontrato,
  s.valorpagocontrato          as s_valorpagocontrato,
  s.valo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>    as s_valorpar<PERSON><PERSON><PERSON><PERSON><PERSON>,
  s.<PERSON><PERSON><PERSON><PERSON> 			   as s_dataca<PERSON><PERSON>,
  s.data<PERSON><PERSON><PERSON><PERSON>           as s_data<PERSON><PERSON><PERSON><PERSON>,
  c.codigo                     as c_codigo,
  c.datalancamento             as c_datalancamento,
  c.datamatric<PERSON>              as c_datamatricula,
  c.situacao                   as c_situacao,
  c.situacaocontrato           as c_situacaocontrato,
  c.situacaorenovacao          as c_situacaorenovacao,
  c.situacaorematricula        as c_situacaorematricula,
  c.valorfinal                 as c_valorfinal,
  c.valorbasecalculo           as c_valorbasecalculo,
  c.valorbasecalculomanutencaomodalidade as c_valorbasemanutencaomodalidade,
  c.vigenciade                 as c_vigenciade,
  c.vigenciaateajustada        as c_vigenciaateajustada,
  ((DATE_PART('year', C.vigenciaateajustada) - DATE_PART('year',C.vigenciade)) * 12 + 
    (DATE_PART('month', C.vigenciaateajustada) - DATE_PART('month',C.vigenciade))) AS c_meses_reais,
  cd.codigo                    as cd_codigo,
  cd.numeromeses               as cd_numeromeses,
  cd.valordesejadomensal       as cd_valordesejadomensal,
  cd.quantidadediasextra       as cd_quantidadediasextra,
  co.codigo                    as co_codigo,
  co.tipooperacao              as co_tipooperacao,
  co.operacaopaga              as co_operacaopaga,
  co.tipojustificativa         as co_tipojustificativa,
  co.origemsistema             as co_origemsistema,
  co.dataoperacao              as co_dataoperacao,
  co.datainicioefetivacaooperacao as co_datainicioefetivacaooperacao,
  co.datafimefetivacaooperacao as co_datafimefetivacaooperacao,
  ac.codigo                    as ac_acessocliente_codigo,
  ac.cliente                   as ac_cliente,
  ac.dthrentrada               as ac_dthrentrada,
  ac.sentido                   as ac_sentido,
  ac.meioidentificacaoentrada  as ac_identificacao,
  ac.tipoacesso                as ac_tipoacesso,
  cm.codigo                    as cm_codigo,
  cm.modalidade                as cm_modalidade,
  cm.vezessemana               as cm_vezessemana,
  cm.valorfinalmodalidade      as cm_valorfinalmodalidade,
  cm.valormodalidade           as cm_valormodalidade,
  m.codigo                     as m_modalidade_codigo,
  m.nome                       as m_nome,
  m.nrvezes                    as m_nrvezes,
  m.usatreino                  as m_usatreino,
  m.utilizarproduto            as m_utilizarproduto,
  m.utilizarturma              as m_utilizarturma,
  m.modalidadedefault          as m_modalidadedefault,
  tc.codigo                    as tc_trancamentocontrato_codigo,
  tc.datafimtrancamento        as tc_datafimtrancamento,
  tc.dataretorno               as tc_dataretorno,
  tc.datatrancamento           as tc_datatrancamento,
  tc.nrdiascongelado           as tc_nrdiascongelado,
  tc.valorcongelado            as tc_valorcongelado,
  tc.valortrancamento          as tc_valortrancamento
from
  situacaoclientesinteticodw s
  inner join empresa e on e.codigo = s.empresacliente 
  inner join contrato c on c.pessoa = s.codigopessoa
  left join contratoduracao cd on c.codigo = cd.contrato
  left join contratooperacao co on c.codigo = co.contrato
  left join acessocliente ac on ac.cliente = s.codigocliente
    and ac.dthrentrada >= ( s.dataultimoacesso  - interval '13 month')
    and ac.dthrentrada < date_trunc('month', current_date)
  left join contratomodalidade cm on c.codigo = cm.contrato
  left join modalidade m on cm.modalidade = m.codigo
  left join trancamentocontrato tc on c.codigo = tc.contrato
-- where 1=1
----  and s.situacao = 'AT'
--   and s.nomecliente like '%JOSE%'
-- order by 
--     s.codigo,  c.codigo, cd.codigo , co.codigo , ac.codigo , cm.codigo , tc.codigo
--    ) as sql
--    group by 1
--limit 1000;