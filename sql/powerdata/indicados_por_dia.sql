SELECT
    foo.dia,
    foo.empresa,
    SUM(foo.qtdIndicacao)  AS qtdIndicacao,
    SUM(foo.qtdConvertido) AS qtdConvertido
FROM (
    -- PARTE 1: Contar quantas indicações viraram contrato
    SELECT
        i.dia::date AS dia,              -- agrupa pelo dia (apenas a parte de data)
        ind.empresa AS empresa,          -- agrupa pela empresa
        0 AS qtdIndicacao,               -- aqui zeramos a coluna de indicações
        COUNT(i.codigo) AS qtdConvertido -- aqui contamos as "convertidas"
    FROM indicacao i
         INNER JOIN indicado ind ON ind.indicacao = i.codigo
         LEFT JOIN cliente  ci ON ci.codigo = ind.cliente
         LEFT JOIN pessoa   pi ON pi.codigo = ci.pessoa
         LEFT JOIN contrato con ON con.pessoa = pi.codigo
    WHERE
          -- Regras para considerar "convertido"
          con.datalancamento::date >= i.dia::date
      AND con.datalancamento::date < i.dia::date + 20
      AND con.situacaocontrato IN ('MA', 'RE')
      AND NOT EXISTS (
          SELECT codigo
          FROM contratooperacao
          WHERE tipooperacao = 'CA'
            AND contrato = con.codigo
      )
    GROUP BY i.dia::date, ind.empresa

    UNION
    -- PARTE 2: Contar quantas indicações foram feitas
    SELECT
        i.dia::date AS dia,
        ind.empresa AS empresa,
        COUNT(i.codigo) AS qtdIndicacao,
        0 AS qtdConvertido
    FROM indicacao i
         INNER JOIN indicado ind ON ind.indicacao = i.codigo
    GROUP BY i.dia::date, ind.empresa
) AS foo
GROUP BY foo.dia, foo.empresa;