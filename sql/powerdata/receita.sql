select
		distinct             
		empresa.cod_empresafinanceiro,
		movpagamento.pessoa,
		movpagamento.nomepagador,
		case 
			when fp.tipoformapagamento = 'CA' then cartaocredito.valor
			else movpagamento.valor
		end as valor,
		case 
			when fp.tipoformapagamento = 'CA' then cartaocredito.valortotal
			else movpagamento.valortotal
		end as valortotal,
				case
			when fp.tipoformapagamento = 'CA' then 'Cartão de Crédito'
		when fp.tipoformapagamento = 'CD' then 'Cartão de Débito'
		when fp.tipoformapagamento = 'CH' then 'Cheque'
		when fp.tipoformapagamento = 'AV' then 'Dinheiro'
		when fp.tipoformapagamento = 'CC' then 'Conta Corrente'
		when fp.tipoformapagamento = 'BB' then 'Boleto Bancário'
		when fp.tipoformapagamento = 'CO' then 'Convênio'
		when fp.tipoformapagamento = 'PD' then 'Pagamento Digital'
		when fp.tipoformapagamento = 'LO' then 'Lote'
		when fp.tipoformapagamento = 'PX' then 'PIX'
		else 'Outro'
	end as descricao_tipo_forma_pagamento,
		case
			when fp.tipoformapagamento in ('AV', 'CD', 'CC', 'BB', 'CO', 'TB', 'PX')
            then movpagamento.datapagamento::timestamp
		when fp.tipoformapagamento = 'CH'
            then cheque.datacompesancao::timestamp
		when fp.tipoformapagamento = 'CA'
            then cartaocredito.datacompesancao::timestamp
	end as receita_datacompensacao,
		empresa.nome as empresaNome,
		cli.matricula,
		cli.cpf as cpfPagador,
		conta.descricao as descricaoconta,
		movc.dataquitacao::timestamp as datamovconta,
		array_to_string(array(
	select
			mp.descricao
	from
			pagamentomovparcela p
	inner join movparcela mp on
			mp.codigo = p.movparcela
	where
			p.movpagamento = movpagamento.codigo),
		', ',
		'') as parcelasPagas
from
		movpagamento
left join empresa on
		empresa.codigo = movpagamento.empresa
left outer join situacaoclientesinteticodw as cli on
		cli.codigopessoa = movpagamento.pessoa
left outer join movconta as movc on
		movc.codigo = movpagamento.movconta
left outer join conta on
		conta.codigo = movc.conta
left join pessoa p on
		p.codigo = movpagamento.pessoa
left outer join cheque on
		movpagamento.codigo = cheque.movpagamento
	and cheque.situacao not like 'CA'
	and cheque.situacao not like 'DV'
left outer join cartaocredito on
		movpagamento.codigo = cartaocredito.movpagamento
	and cartaocredito.situacao not like 'CA'
inner join formapagamento fp on
		movpagamento.formapagamento = fp.codigo
	and fp.tipoformapagamento in ('CA', 'CD', 'CH', 'AV', 'CC', 'BB', 'CO', 'PD', 'TB', 'PX')
inner join usuario u on
		u.codigo = movpagamento.responsavelpagamento
where
		1 = 1
	and (movpagamento.recibopagamento is not null
		or movpagamento.credito = 't')
	and (movpagamento.valor > 0
		or fp.tipoformapagamento = 'CC')
