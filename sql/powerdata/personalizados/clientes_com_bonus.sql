SELECT distinct (cli.*), 
  co.tipojustificativa AS tipojustificativa,
  co.responsavelliberacao AS responsavelliberacao,
  co.clientetransferedias AS clientetransferedias,
  co.clienterecebedias AS clienterecebedias,
  CAST(co.descricaocalculo AS TEXT) AS descricaocalculo,
  co.observacao AS observacao,
  co.responsavel AS responsavel,
  co.datafimefetivacaooperacao AS datafimefetivacaooperacao,
  co.datainicioefetivacaooperacao AS datainicioefetivacaooperacao,
  co.dataoperacao AS dataoperacao,
  co.operacaopaga AS operacaopaga,
  co.tipooperacao AS tipooperacao,
  co.contrato AS contrato,
  co.codigo AS codigo_contrato,
  co.valor AS valor,
  co.nrdiasoperacao AS nrdiasoperacao,
  co.informacoes AS informacoes,
  co.chavearquivo AS chavearquivo,
  co.origemsistema AS origemsistema,
  co.informacoesdesfazer AS informacoesdesfazer,
  co.formatoarquivo AS formatoarquivo,
  co.nomearquivo AS nomearquivo,
  jo.descricao 
FROM contratooperacao co
left join usuario as us on us.codigo = co.responsavel
inner join contrato as cont on cont.codigo = co.contrato
inner join cliente as cli on cli.pessoa = cont.pessoa
inner join justificativaoperacao jo on co.tipojustificativa = jo.codigo
where (co.tipooperacao LIKE 'BA' or co.tipooperacao like 'BR')
and (('2024-03-18' >= co.datainicioefetivacaooperacao and '2024-03-18' <=
co.datafimefetivacaooperacao )
or co.datainicioefetivacaooperacao >= '2025-03-18')
AND us.colaborador is not null