SELECT 
  co.bolsa AS bolsa,
  CAST(co.naopermitirrenovacaorematriculadecontratoanteriores as boolean) AS naopermitirrenovacaorematriculadecontratoanteriores,
  co.valordescontoespecifico AS valordescontoespecifico,
  co.valordescontoporcentagem AS valordescontoporcentagem,
  co.nomemodalidades AS nomemodalidades,
  co.somaproduto AS somaproduto,
  co.contratoresponsavelrenovacaomatricula AS contratoresponsavelrenovacaomatricula,
  co.contratoresponsavelrematriculamatricula AS contratoresponsavelrematriculamatricula,
  co.datalancamento AS datalancamento,
  co.datamatricula AS datamatricula,
  co.dataprevistarenovar AS dataprevistarenovar,
  co.datarenovarrealizada AS datarenovarrealizada,
  co.dataprevistarematricula AS dataprevistarematricula,
  co.datarematricularealizada AS datarematricularealizada,
  co.situacaocontrato AS situacaocontrato,
  co.situacaorenovacao AS situacaorenovacao,
  co.situacaorematricula AS situacaorematricula,
  co.contratobaseadorenovacao AS contratobaseadorenovacao,
  co.contratobaseadorematricula AS contratobaseadorematricula,
  co.pagarcomboleto AS pagarcomboleto,
  co.responsavelcontrato AS responsavelcontrato,
  co.observacao AS observacao,
  co.responsavelliberacaocondicaopagamento AS responsavelliberacaocondicaopagamento,
  co.valorfinal AS valorfinal,
  co.valorbasecalculo AS valorbasecalculo,
  co.vigenciaate AS vigenciaate,
  co.vigenciaateajustada AS vigenciaateajustada,
  co.vigenciade AS vigenciade,
  co.estendecoberturafamiliares AS estendecoberturafamiliares,
  co.situacao AS situacao,
  co.plano AS plano,
  co.pessoa AS pessoa,
  co.consultor AS consultor,
  co.empresa AS empresa,
  co.codigo AS codigo,
  co.conveniodesconto AS conveniodesconto,
  co.dividirprodutosnasparcelas AS dividirprodutosnasparcelas,
  co.desconto AS desconto,
  co.tipodesconto AS tipodesconto,
  co.valordesconto AS valordesconto,
  co.diavencimentoprorata AS diavencimentoprorata,
  co.dataalteracaomanual AS dataalteracaomanual,
  co.responsaveldatabase AS responsaveldatabase,
  co.regimerecorrencia AS regimerecorrencia,
  co.origemcontrato AS origemcontrato,
  co.importacao AS importacao,
  co.valorarredondamento AS valorarredondamento,
  co.renovavelautomaticamente AS renovavelautomaticamente,
  co.quantidademaximafrequencia AS quantidademaximafrequencia,
  co.vendacreditotreino AS vendacreditotreino,
  co.numerocupomdesconto AS numerocupomdesconto,
  co.crossfit AS crossfit,
  co.permiterenovacaoautomatica AS permiterenovacaoautomatica,
  co.agendadoconviteaulaexperimental AS agendadoconviteaulaexperimental,
  co.origemsistema AS origemsistema,
  co.grupo AS grupo,
  co.nomeconveniodesconto AS nomeconveniodesconto,
  co.valorconveniodesconto AS valorconveniodesconto,
  co.percentualconveniodesconto AS percentualconveniodesconto,
  co.idexterno AS idexterno,
  co.vendacreditosessao AS vendacreditosessao,
  co.cancelamentointegrado AS cancelamentointegrado,
  co.contratoorigemtransferencia AS contratoorigemtransferencia,
  co.valorbasecalculomanutencaomodalidade AS valorbasecalculomanutencaomodalidade,
  co.dataenviocontrato AS dataenviocontrato,
  co.emailrecebimento AS emailrecebimento,
  co.ipassinaturacontrato AS ipassinaturacontrato,
  co.dataassinaturacontrato AS dataassinaturacontrato,
  co.vendavitio AS vendavitio,
  co.evento AS evento,
  co.pessoaoriginal AS pessoaoriginal,
  co.primeirocontratobaseadorenovacao AS primeirocontratobaseadorenovacao,
  co.dataassinaturacancelamento AS dataassinaturacancelamento,
  co.xnumpro AS xnumpro,
  co.valorbasenegociado AS valorbasenegociado
  FROM contrato co
LEFT JOIN usuario usu ON co.responsavelcontrato = usu.codigo
WHERE dataalteracaomanual BETWEEN '2023-01-01 00:00:00' AND '2025-01-31 23:59:59'
