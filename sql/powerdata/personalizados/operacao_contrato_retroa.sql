SELECT DISTINCT
    cliente.*,
    con.tipojustificativa,
    con.dataoperacao, 
    con.datainicioefetivacaooperacao,
    jo.descricao
FROM Cliente
INNER JOIN contrato ON contrato.pessoa = cliente.pessoa
INNER JOIN contratooperacao con ON con.contrato = contrato.codigo
LEFT JOIN justificativaoperacao jo ON con.tipojustificativa = jo.codigo
WHERE con.dataoperacao BETWEEN '2024-01-01' AND '2025-03-14'
  AND con.dataoperacao > con.datainicioefetivacaooperacao
  AND cliente.empresa = 1
ORDER BY con.dataoperacao;