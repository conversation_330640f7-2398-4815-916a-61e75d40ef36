select * from (select
distinct(cl.codigo) as cli,
c.codigo as codContrato,
p.nome,
pl.descricao as nomePlano,
c.pessoa as codPessoa,
cl.matricula as matriculacli,
dw.situacao as situacaoCliente,
c.vigenciade as dataInicio,
c.vigenciaateajustada as dataFim,
cdu.numeroMeses as duracaoContrato,
c.nomemodalidades,
array_to_string(array(SELECT email FROM email WHERE email.pessoa =
dw.codigopessoa), ';', '') as emailsCliente,
array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa =
dw.codigopessoa), ' || ', '') as telefonescliente,
CASE WHEN exists(select codigo from autorizacaocobrancacliente where
tipoautorizacao = 1 and cliente = cl.codigo and ativa = true)
THEN true
ELSE false END AS temAutorizacaoCobranca,
CASE WHEN pl.recorrencia THEN ((plrec.renovavelautomaticamente OR
crec.renovavelautomaticamente) AND c.permiterenovacaoautomatica)
ELSE ((pl.renovavelautomaticamente or c.renovavelautomaticamente) AND
c.permiterenovacaoautomatica) END AS contratoRenovavel,
e.nome as empresa,
e.codigo as codEmpresa
FROM contrato c
INNER JOIN pessoa p on p.codigo = c.pessoa
INNER JOIN cliente cl on cl.pessoa = p.codigo
INNER JOIN situacaoclientesinteticodw dw on dw.codigocliente = cl.codigo
INNER JOIN empresa e on e.codigo = c.empresa
INNER JOIN plano pl on pl.codigo = c.plano
LEFT JOIN planorecorrencia plrec on plrec.plano = pl.codigo
LEFT JOIN contratorecorrencia crec on crec.contrato = c.codigo
INNER JOIN contratoduracao cdu on cdu.contrato = c.codigo
LEFT JOIN vinculo v ON cl.codigo = v.cliente
WHERE 1 = 1
AND c.situacao in('AT','IN')
AND c.vigenciaateajustada::date between '01/03/2024' and '31/03/2025'
) as foo
WHERE 1 = 1
AND temAutorizacaoCobranca = true
AND contratoRenovavel = false