SELECT
distinct (cli.*),coalesce(pa.dataLancamento,pa.datainicioacesso) as
dataLancamento, us.nome as responsavel, pa.tokengympass, (EXTRACT(DAY
FROM pa.datafinalacesso - pa.datainicioacesso) + 1) AS qtd
FROM cliente cli
INNER JOIN periodoacessocliente pa ON pa.pessoa = cli.pessoa
LEFT JOIN usuario us ON pa.responsavel = us.codigo
WHERE 1 = 1
AND coalesce(pa.tokengympass, '') <> ''
AND pa.tipoacesso = 'PL'
AND '2025-03-18' BETWEEN pa.datainicioacesso AND pa.datafinalacesso