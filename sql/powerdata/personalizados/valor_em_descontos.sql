SELECT descricao, usuario, matricula, cliente, contrato, datalancamento,
SUM(desconto) AS valor, convenio, moeda FROM (
SELECT
emp.moeda,p.descricao,u.nome AS usuario,cli.matricula,
pes.nome AS cliente,mp.contrato,mp.datalancamento::date,
CASE
WHEN p.tipoproduto = 'DE' THEN mp.totalfinal
ELSE mp.valordesconto
END AS desconto,
c2.descricao AS convenio
FROM
movproduto mp
INNER JOIN produto p ON mp.produto = p.codigo
INNER JOIN usuario u ON mp.responsavellancamento = u.codigo
INNER JOIN cliente cli ON mp.pessoa = cli.pessoa
INNER JOIN pessoa pes ON pes.codigo = mp.pessoa
INNER JOIN empresa emp ON emp.codigo = mp.empresa
LEFT JOIN contrato con ON mp.contrato = con.codigo
LEFT JOIN conveniodesconto c2 ON con.conveniodesconto = c2.codigo
LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato
LEFT JOIN contratoduracaocreditotreino cdct ON cd.codigo =
cdct.contratoduracao
WHERE
mp.datalancamento BETWEEN ? AND ?
AND pes.codigo IS NOT NULL
AND (
(mp.valordesconto > 0 AND (c2.codigo IS NOT NULL OR mp.contrato IS
NULL))
OR (p.tipoproduto = 'DE' AND c2.codigo IS NULL)
)
AND mp.empresa = ?
UNION ALL
SELECT
'' AS moeda,p.descricao,u.nome AS usuario,
'' AS matricula,'CONSUMIDOR' AS cliente,
mp.contrato,mp.datalancamento::date,
mp.valordesconto,'' AS convenio
FROM
movproduto mp
INNER JOIN produto p ON mp.produto = p.codigo
INNER JOIN usuario u ON mp.responsavellancamento = u.codigo
WHERE
mp.datalancamento BETWEEN data1 AND data2
AND mp.valordesconto > 0
AND mp.pessoa IS NULL
AND mp.empresa = ?
) AS query
GROUP BY
1, 2, 3, 4, 5, 6, 8, 9
ORDER BY datalancamento