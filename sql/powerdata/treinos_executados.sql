select t.executa<PERSON><PERSON><PERSON><PERSON>,
       c.co<PERSON><PERSON><PERSON>,
       e.cod_empresaf<PERSON><PERSON><PERSON>,
       e.nome as nome_unidade,
       pac.nome as nome_professor_a<PERSON><PERSON>hou,
       c.nome as nome_aluno,
       t.come<PERSON><PERSON>,
       p.co<PERSON><PERSON><PERSON><PERSON>   as professor,
       pac.co<PERSON><PERSON><PERSON><PERSON> as professor_a<PERSON><PERSON><PERSON>,
       t.nota,
       t.<PERSON>,
       t.professor<PERSON><PERSON><PERSON><PERSON><PERSON>_codigo,
       c.co<PERSON>,
       t.data<PERSON><PERSON>          as inicio_execucao,
       t.datafim             as fim_execucao,
       CASE t.origem
           WHEN 0 THEN 'Ficha Impressa'
           WHEN 1 THEN 'Acomp. Professor'
           WHEN 2 THEN 'Smartphone'
           WHEN 3 THEN 'Ficha concluída pelo RetiraFicha'
           end               as origem_execucao,
       ptf.ficha_codigo,
       ptf.programa_codigo
from treinorealizado t
         inner join clientesintetico c on t.cliente_codigo = c.codigo
         inner join empresa e on e.codzw = c.empresa
         inner join programatreinoficha ptf on t.programatreinoficha_codigo = ptf.codigo
         left join professorsintetico p on p.codigo = t.professor_codigo
         left join professorsintetico pac on p.codigo = t.professor<PERSON><PERSON><PERSON><PERSON><PERSON>_codigo
order by t.datainicio
