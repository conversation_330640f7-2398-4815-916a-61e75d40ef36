select c.codigo<PERSON><PERSON><PERSON>,
       e.cod_emp<PERSON><PERSON><PERSON><PERSON><PERSON>,
       e.nome as nome_unidade,
       prof.nome as nome_professor_monto<PERSON>,
       c.nome as nome_aluno,
    p.codigo as codigoprograma,
    f.codigo as codigoficha,
    c.co<PERSON>,
    p.<PERSON><PERSON>,
    p.<PERSON><PERSON><PERSON><PERSON>,
    p.data<PERSON>,
    p.nrtreinosrealizados,    
    datalancamento,
    diasporsemana,
    genero,
    p.nome as nomeprograma,
    totalaulasprevistas,
    prof.codigocolaborador,
    f.nome as nomeficha,
    f.sexo as sexoficha,
    regexp_replace(f.mensage<PERSON><PERSON>, E'[\\n\\r]+', ' ', 'g' ) as mensage<PERSON>uno,
    case
        ptf.tipoexecucao
        when 0 then 'ALTERNADA'
        when 1 then 'DIAS DA SEMANA'
    end as diasficha,
    ARRAY_TO_STRING(
        ARRAY(
            select
                diasemana
            from
                programatreinoficha_diasemana
            where
                programatreinoficha_codigo = ptf.codigo
        ),
        ';'
    ) as di<PERSON><PERSON><PERSON>,
    CASE
        af.metodoexecucao
        WHEN 0 THEN 'Pirâmide Decrescente'
        WHEN 1 THEN 'Pirâmide Crescente'
        WHEN 2 THEN 'Circuito'
        WHEN 3 THEN 'Isométrico'
        WHEN 4 THEN 'Super-Série'
        WHEN 5 THEN 'BI-Set'
        WHEN 6 THEN 'TRI-Set'
        WHEN 7 THEN 'Drop-Set'
        WHEN 8 THEN 'Ondulatório'
        WHEN 9 THEN 'Progressão Dupla'
        WHEN 11 THEN 'De Lorme'
        WHEN 12 THEN 'Erpad'
        WHEN 13 THEN 'Parcelado'
        WHEN 14 THEN 'Duplamente Parcelado'
        WHEN 15 THEN 'Triplamente Parcelado'
        WHEN 16 THEN 'Puxe-Empurre'
        WHEN 17 THEN 'Repetição Roubada'
        WHEN 18 THEN 'Repetição Forçada'
        WHEN 19 THEN 'D.T.A.'
        WHEN 20 THEN 'Repetição Parcial'
        WHEN 21 THEN 'Pique de Contração'
        WHEN 22 THEN 'Tensão Lenta e Contínua'
        WHEN 23 THEN 'Set Descendente'
        WHEN 24 THEN 'Isolamento'
        WHEN 25 THEN 'Super-Set'
        WHEN 26 THEN 'Série Composta'
        WHEN 27 THEN 'Super-Set Múltiplo'
        WHEN 28 THEN 'Pré-Exaustão'
        WHEN 29 THEN 'Série Gigante'
        WHEN 30 THEN 'P.H.A.'
        WHEN 31 THEN 'Super-Circuito'
        WHEN 32 THEN 'Musculação Intervalada'
        WHEN 33 THEN 'Pliométrico'
        WHEN 34 THEN 'Repetição Negativa'
        WHEN 35 THEN 'Nautilus'
        WHEN 36 THEN 'Heavy-Duty'
        WHEN 37 THEN 'Rest Pause'
        WHEN 38 THEN 'Pós-Exaustão'
        WHEN 39 THEN 'Exaustão'
        WHEN 40 THEN 'Strip Set'
        WHEN 41 THEN 'Set 21'
        WHEN 42 THEN 'Super Drop Set'
        WHEN 43 THEN 'Flushing'
        WHEN 44 THEN 'Contração Isométrica'
        WHEN 45 THEN 'Contínuo'
        WHEN 46 THEN 'Combinado'
        WHEN 47 THEN 'Alternado'
        WHEN 48 THEN 'Alternado + Simultâneo'
        WHEN 49 THEN 'FST-7'
        WHEN 50 THEN 'SST'
        WHEN 51 THEN 'HIIT'
        WHEN 52 THEN 'Tabata'
        WHEN 53 THEN '6/20'
        WHEN 54 THEN '2 tempos'
        WHEN 55 THEN '3 tempos'
        WHEN 56 THEN 'Nonstop'
        WHEN 57 THEN 'Cluster'
        WHEN 58 THEN 'Ponto zero'
        WHEN 999 THEN 'Não atribuído'
    end as metodo_execucao_atividade,    
    regexp_replace(af.complementonomeatividade, E'[\\n\\r]+', ' ', 'g' ) as complementonomeatividade,
    af.ordem as ordemficha,
    af.descanso as atv_descanso,
    a.nome as nomeatividade,
    case
        a.tipo
        when 0 then 'Neuromuscular'
        when 1 then 'Cardiovascular'
    end as tipoatividade,
    s.carga,
    s.cargaapp,    
    regexp_replace(s.complemento, E'[\\n\\r]+', ' ', 'g' ) as complemento,
    s.descanso as serie_descanso,
    s.ordem as serie,
    s.distancia,
    s.duracao,
    s.velocidade,
    s.repeticao,
    g.nome as grupomuscular,
    apar.nome as aparelho
from
    programatreino p
    inner join clientesintetico c on c.codigo = p.cliente_codigo
    inner join empresa e on e.codzw = c.empresa
    inner join professorsintetico prof on prof.codigo = p.professormontou_codigo
    inner join programatreinoficha ptf on ptf.programa_codigo = p.codigo
    inner join ficha f on f.codigo = ptf.ficha_codigo
    inner join atividadeficha af on af.ficha_codigo = f.codigo
    inner join atividade a on af.atividade_codigo = a.codigo
    left join atividadegrupomuscular agm on agm.atividade_codigo = a.codigo
    left join grupomuscular g on g.codigo = agm.grupomuscular_codigo
    left join atividadeaparelho aa on aa.atividade_codigo = a.codigo
    left join aparelho apar on apar.codigo = aa.aparelho_codigo
    inner join serie s on s.atividadeficha_codigo = af.codigo
order by
    p.datainicio desc,
    f.codigo,
    af.ordem
