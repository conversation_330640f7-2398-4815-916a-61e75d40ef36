select 
clsou.matricula as convidou_matricula, 
psou.nome as convidou_nome, 
empou.nome as convidou_empresa,
obj.dia as dia_convite,
clado.matricula as convidado_matricula, 
pado.nome as convidado_nome,  
clado.situacao as convidado_situacao, 
pado.dataNasc as convidado_dtnasc, 
ac.dtHrEntrada  as convidado_dtentrada, 
tel.numero as convidado_telefone, 
em.email as convidado_email, 
pado.dataCadastro as convidado_data_cadastro,
empado.nome as convidado_empresa
 from Convite obj 
 inner join cliente clsou on clsou.codigo = obj.convidou  
 inner join cliente clado on clado.codigo = obj.convidado  
 inner join pessoa psou on psou.codigo = clsou.pessoa
 inner join pessoa pado on pado.codigo = clado.pessoa
 inner join empresa empado on empado.codigo = clado.empresa
 inner join empresa empou on empou.codigo = clsou.empresa
 left join AcessoCliente ac on ac.codigo = (select max(ac2.codigo) from AcessoCliente ac2 where ac2.cliente = obj.convidado and ac2.sentido = 'E') 
 left join Telefone tel on tel.codigo = (select max(tel2.codigo) from Telefone tel2 where tel2.pessoa = pado.codigo) 
 left join Email em on em.codigo = (select max(em2.codigo) from Email em2 where em2.pessoa = pado.codigo) 
order by obj.dia desc