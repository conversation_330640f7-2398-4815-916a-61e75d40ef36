/*

https://pacto.atlassian.net/browse/IA-1001
Cópia da consulta acessos.sql com duas modificações para se adequar às consultas utilizados no Relatório Lista de Acessos / ZW.
As mudanças são apenas duas:
  - Acesso de alunos: filtro de situação
  - Acesso de colaboradores: troca no join de "colaborador > empresa" para "localacesso > empresa". 
    O join em acessos.sql considera a empresa onde o colaborador está cadastrado. O relatório considera a empresa
    onde foi feito o acesso. Essa diferença está causando divergência nos indicadores da Fly Now.

*/
select *
from (
  --
  select
    'aluno'                      as ac_de,
    e.nome                       as e_nome,
    e.cod_empresafinanceiro      as e_empresafinanceiro,
    e.codigo                     as e_codigo,
    pes.nome                     as ac_nomepessoa,
    ac.cliente                   as s_codigocliente,
    ac.codigo                    as ac_codigo,
    ac.dthrentrada               as ac_dthrentrada,
    ac.dthrsaida                 as ac_dthrsaida,
    ac.meioidentificacaoentrada  as ac_identificacao,
    ac.liberacaoacesso           as ac_liberacaoacesso,
    ac.localacesso 	           as ac_localacesso,
    loc.descricao                as ac_localdescricao  
  from acessocliente ac
    inner join cliente cli on cli.codigo  = ac.cliente 
    inner join pessoa pes on pes.codigo = cli.pessoa
    inner join empresa e on e.codigo = cli.empresa 
    left join localacesso loc on loc.codigo = ac.localacesso
  where
    ac.situacao not like 'RV_BLOQ%'
  --
    union
  --  
  select
    'colaborador'                as ac_de,
    e.nome                       as e_nome,
    e.cod_empresafinanceiro      as e_empresafinanceiro,
    e.codigo                     as e_codigo,
    pes.nome                     as ac_nomepessoa,
    null                         as s_codigocliente,
    ac.codigo 				   as ac_codigo, 
    ac.dthrentrada 			   as ac_dthrentrada, 
    ac.dthrsaida                 as ac_dthrsaida,
    ac.meioidentificacaoentrada  as ac_identificacao, 
    ac.liberacaoacesso           as ac_liberacaoacesso,
    ac.localacesso 	           as ac_localacesso,
    loc.descricao                as ac_localdescricao
  from acessocolaborador ac 
  inner join colaborador co on ac.colaborador = co.codigo 
  inner join pessoa pes on pes.codigo = co.pessoa 
  left join localacesso loc on loc.codigo = ac.localacesso
  left join empresa e on e.codigo = loc.empresa
  --
) as sql 
