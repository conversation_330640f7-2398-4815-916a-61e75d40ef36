----------------------------- TABELA CRIADA PARA VISUALIZAÇÃO DO LTV ----------------------------
SELECT
        pessoa.codigo AS pessoa_codigo,
        contrato.plano AS plano_codigo,
        contrato.codigo as codigo_contrato,
        pessoa.sexo as sexo,
        plano.descricao as descricao,
        pessoa.nome as nome,
        st.idade as idade,
        contratooperacao.tipooperacao as tipo_operacao,
        justificativaoperacao.descricao as descricao_operacao,
        st.situacaocontrato as situacao_contrato,
        historicocontrato.tipohistorico as tipo_historico,
        historicocontrato.datainiciosituacao,
        contrato.vigenciaateajustada as vigencia_ajustada,
        contrato.datamatricula,
        contrato.datalancamento,
        contrato.situacao,
        empresa.codigo as empresa_codigo,
        contrato.situacaocontrato
    FROM contrato
    LEFT JOIN pessoa ON contrato.pessoa = pessoa.codigo
    LEFT JOIN empresa ON contrato.empresa = empresa.codigo
    LEFT JOIN cliente ON contrato.pessoa = cliente.pessoa
    LEFT JOIN situacaoclientesinteticodw st ON st.codigocliente = cliente.codigo
    LEFT JOIN contratooperacao ON contratooperacao.contrato = contrato.codigo
    LEFT JOIN justificativaoperacao ON justificativaoperacao.codigo = contratooperacao.tipojustificativa
    LEFT JOIN historicocontrato ON historicocontrato.contrato = contrato.codigo
    LEFT JOIN plano ON contrato.plano = plano.codigo