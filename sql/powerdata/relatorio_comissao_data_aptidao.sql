-- Active: 1741625085864@@localhost@5432@bdzillyonengenhariadocorpomatriz@public
-- https://pacto.atlassian.net/browse/IA-978
SELECT
  base.unidade,
  base.contrato_codigo,
  base.contrato_situacao,
  base.aluno_matricula,
  base.aluno_nome,
  base.aluno_cpf,
  base.dt_assinatura,
  base.dt_lancamento_contrato,
  base.dt_ultimo_pgto,
  base.dt_inclusao_convenio,
  base.contrato_quitado,
  base.nome_colaborador, 
    -- DATA DE APTIDÃO: maior data entre lancamento, assinatura, pgto, inclusão convênio
  CASE
    WHEN base.dt_assinatura IS NOT NULL AND base.contrato_quitado
    THEN GREATEST(
      base.dt_lancamento_contrato,
      base.dt_assinatura,
      base.dt_ultimo_pgto,
      base.dt_inclusao_convenio
    )
    ELSE NULL
  END AS data_aptidao,
  -- APTIDÃO PARA COMISSÃO: contrato quitado e possui assinatura 
  CASE
    WHEN base.dt_assinatura IS NOT NULL AND base.contrato_quitado
    THEN TRUE
    ELSE FALSE
  END AS apto_comissao
FROM (
  SELECT
    contrato.codigo AS contrato_codigo,
    contrato.situacaocontrato AS contrato_situacao,
    contrato.datalancamento AS dt_lancamento_contrato,
    cliente.matricula AS aluno_matricula,
    pessoa.nome AS aluno_nome,
    pessoa.cfp AS aluno_cpf,
    empresa.nome AS unidade,
    contratoassinaturadigital.lancamento AS dt_assinatura,
    -- DATA DO PGTO DA ÚLTIMA PARCELA
    (
      SELECT MAX(recibopagamento.data)
      FROM pagamentomovparcela pmp
      JOIN recibopagamento ON recibopagamento.codigo = pmp.recibopagamento
      JOIN movparcela mp ON mp.codigo = pmp.movparcela
      WHERE mp.contrato = contrato.codigo
        AND mp.empresa = contrato.empresa
    ) AS dt_ultimo_pgto,
    -- DATA DE INCLUSÃO EM CONVÊNIO DE COBRANÇA
    (
      SELECT MAX(a.dataregistro)
      FROM autorizacaocobrancacliente a
      WHERE a.cliente = cliente.codigo
    ) AS dt_inclusao_convenio,
    -- CONTRATO QUITADO: não existe parcela em 'EA' e todas possuem recibo de pagamento
    CASE
      WHEN NOT EXISTS (
             SELECT 1
             FROM movparcela mp
             WHERE mp.contrato = contrato.codigo
               AND mp.empresa = contrato.empresa
               AND mp.situacao = 'EA'
           )
           AND NOT EXISTS (
             SELECT 1
             FROM movparcela mp
             LEFT JOIN pagamentomovparcela pmp ON pmp.movparcela = mp.codigo
             WHERE mp.contrato = contrato.codigo
               AND mp.empresa = contrato.empresa
               AND pmp.recibopagamento IS NULL
           )
      THEN TRUE
      ELSE FALSE
    END AS contrato_quitado,
    colaborador.nome as nome_colaborador
  FROM contrato
  JOIN cliente ON cliente.pessoa = contrato.pessoa AND cliente.empresa = contrato.empresa
  JOIN pessoa ON pessoa.codigo = cliente.pessoa
  JOIN plano ON plano.codigo = contrato.plano AND plano.empresa = contrato.empresa
  JOIN empresa ON empresa.codigo = contrato.empresa
  LEFT JOIN contratoassinaturadigital ON contratoassinaturadigital.contrato = contrato.codigo
  LEFT JOIN (select pe.nome, co.codigo from colaborador co inner join pessoa pe on co.pessoa = pe.codigo) colaborador ON contrato.consultor = colaborador.codigo
  WHERE cliente.situacao = 'AT'
  GROUP BY
    contrato.codigo,
    contrato.situacaocontrato,
    contrato.datalancamento,
    cliente.matricula,
    pessoa.nome,
    pessoa.cfp,
    colaborador.nome,
    empresa.nome,
    contratoassinaturadigital.lancamento,
    cliente.codigo
) AS base
ORDER BY
  apto_comissao DESC,
  data_aptidao DESC

