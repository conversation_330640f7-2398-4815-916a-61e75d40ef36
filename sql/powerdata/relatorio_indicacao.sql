select * from (
 SELECT
-- e.cod_emp<PERSON><PERSON><PERSON><PERSON><PERSON>,
i.codigo,
ind.nomeindica<PERSON>,
p.nome AS clientequeindicou,
pc.nome AS colaboradorqueindicou,
i.dia,
(SELECT con.datalancamento 
FROM contrato con 
WHERE con.pessoa = ci.pessoa 
AND con.datalancamento::date >= i.dia::date 
AND con.datalancamento::date < i.dia::date + 30
AND con.situacaocontrato IN ('MA', 'RE') 
AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = con.codigo) 
ORDER BY codigo ASC LIMIT 1) AS datalancamento,
e.descricao AS evento,
us.nome AS responsavelcadastro,
ind.telefone,
ind.telefoneindicado,
ind.email
FROM indicacao i
INNER JOIN usuario us ON us.codigo = i.responsavelcadastro
INNER JOIN indicado ind ON ind.indicacao = i.codigo
LEFT JOIN evento e ON e.codigo = i.evento
LEFT JOIN cliente c ON c.codigo = i.clientequeindicou
LEFT JOIN pessoa p ON c.pessoa = p.codigo
LEFT JOIN colaborador col ON col.codigo = i.colaboradorqueindicou
LEFT JOIN pessoa pc ON pc.codigo = col.pessoa
LEFT JOIN cliente ci on ci.codigo = ind.cliente
LEFT JOIN pessoa pi on pi.codigo = ci.pessoa
-- LEFT JOIN empresa emp ON emp.codigo = c.empresa
WHERE 1=1
AND i.dia::date >='2024-01-01'
) as t