--(Indicador do BI) Conversão de Indicados (Atingida). 
 select 
fmd.codigo as codigodetalhado, 
fmd.obtevesucesso, 
fm.codigo as fecharmeta, 
fm.dataregistro as datameta, 
fm.identificadormeta as identificadormeta, 
am.meta<PERSON> as metaaberta, 
fmd.configuracaodiasposvenda, 
fmd.codigoorigem,
fmd.origem,
fmd.teveContato, 
fmd.repescagem, 
fmd.cliente, 
pes.codigo as codigopessoacliente, 
cli.matricula as matricula, 
pes.nome as nomecliente, 
pes.datanasc, 
pes.estadocivil, 
fmd.passivo, 
pas.nome as nomepassivo, 
fmd.indicado, 
ind.nomeindicado as nomeindicado, 
ag.codigo as codigoagenda,
ag.dataagendamento,
ag.tipoagendamento,
ag.hora,
ag.minuto,
scs.situacao, 
scs.situacaocontrato 
from fecharmetadetalhado  fmd 
inner join fecharmeta fm on fm.codigo = fmd.fecharmeta 
inner join aberturameta am on am.codigo = fm.aberturameta 
left join cliente cli on cli.codigo = fmd.cliente 
left join pessoa pes on pes.codigo = cli.pessoa 
left join passivo pas on pas.codigo = fmd.passivo 
left join indicado ind on ind.codigo = fmd.indicado 
left join situacaoclientesinteticodw scs on scs.codigocliente = fmd.cliente 
left join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' 
where am.dia::date > '01/01/2024'
and fm.identificadormeta in ('CI') 
and fmd.obtevesucesso = true 
and fmd.repescagem = false 