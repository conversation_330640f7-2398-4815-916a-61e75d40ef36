select
	c.codigo as codigo_cliente,
	c.codigoMatricula as matricula,
	c.pessoa as codigo_pessoa,
	p.nome as nome_pessoa,
	p.dataNasc as datanascimento,
	em.email as email,
	tel.numero as telefone,
	c.empresa as empresa,
	e.cod_empresafinanceiro as cod_empresafinanceiro, 
	e.nome as nome_empresa,
	pa.dataInicioAcesso as data_inicioacesso,
	pa.tipototalpass as tipototalpass
from
	Cliente c
inner join Empresa e on
	c.empresa = e.codigo
inner join Pessoa p on
	c.pessoa = p.codigo
inner join PeriodoAcessoCliente pa on
	(pa.pessoa = c.pessoa)
left outer join Email em on
	(em.codigo =(
	select
		min(em.codigo)
	from
		Email em
	where
		em.pessoa = c.pessoa))
left outer join Telefone tel on
	(tel.codigo =(
	select
		min(tel.codigo)
	from
		Telefone tel
	where
		tel.pessoa = c.pessoa))
where
	pa.tipototalpass is true
order by
	pa.dataInicioAcesso asc
