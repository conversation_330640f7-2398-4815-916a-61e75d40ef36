-- Active: 1739203793980@@localhost@5432@bdzillyongreenlifefatima@public
SELECT
    movconta.datavencimento::TIMESTAMP AS movconta_datavencimento,
    tdoc.descricao AS movconta_tipodocumento,
    fpg.descricao AS movconta_ds_formapg,
    pessoa.nome AS pessoa_nome,
    fornecedor.cnpj AS pessoa_cnpj,
    CASE 
        WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN colunasmovcontaRateio.valor * -1
        ELSE colunasmovcontaRateio.valor
    END AS movconta_valor,
    CASE
        WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN movconta.valorpago * -1
        ELSE movconta.valorpago
    END AS movconta_valorpago,
    CASE
        WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN movconta.valororiginalalterado * -1
        ELSE movconta.valororiginalalterado 
    END AS movconta_valororiginal,
    CASE
        WHEN movconta.valorpago = 0 THEN movconta.valorpago
        WHEN (
            SELECT COUNT(*) FROM movcontarateio cmr WHERE cmr.movconta = movconta.codigo
        ) > 1 THEN
            CASE WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN colunasmovcontaRateio.valor * -1
            ELSE colunasmovcontaRateio.valor END
        ELSE 
            CASE WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN movconta.valorpago * -1
            ELSE movconta.valorpago END
    END AS movconta_valorpago_auditado,
    CASE
        WHEN (
            SELECT COUNT(*) FROM movcontarateio cmr WHERE cmr.movconta = movconta.codigo
        ) > 1 THEN
            CASE WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN colunasmovcontaRateio.valor * -1
            ELSE colunasmovcontaRateio.valor END
        ELSE 
            CASE WHEN colunasmovcontaRateio.tipoes IN (2, 3) THEN movconta.valororiginalalterado * -1
            ELSE movconta.valororiginalalterado END
    END AS movconta_valor_previsto_auditado,
    colunasmovcontaRateio.descricao AS movconta_descricao,
    movconta.dataquitacao::TIMESTAMP AS movconta_dataquitacao,
    conta.descricao AS conta_descricao,
    b.codigobanco AS conta_codigobanco,
    CASE colunasmovcontaRateio.tipoes
        WHEN 1 THEN 'Entrada'
        WHEN 2 THEN 'Saída'
        WHEN 3 THEN 'Investimento'
        ELSE colunasmovcontaRateio.tipoes::VARCHAR
    END AS tipoes,
    pc.codigoplanocontas AS planocontas,
    pc.nome AS nome_planocontas,
    cc.codigocentrocustos AS centrocustos,
    cc.nome AS nome_centrocustos,
    movconta.datalancamento::TIMESTAMP AS movconta_datalancamento,
    movconta.datacompetencia::TIMESTAMP AS movconta_datacompetencia,
    lote.dataLancamento::TIMESTAMP AS lote_datalancamento,
    lote.dataDeposito::TIMESTAMP AS lote_datadeposito,
    movconta.tipooperacao AS movconta_tipooperacao,
    CASE movconta.tipooperacao
        WHEN 1 THEN 'Pagamento'
        WHEN 2 THEN 'Recebimento'
        WHEN 3 THEN 'Depósito'
        WHEN 4 THEN 'Transferência'
        WHEN 5 THEN 'Estorno'
        WHEN 6 THEN 'Troca de Forma de Pagamento'
        WHEN 7 THEN 'Ajuste de Saldo'
        WHEN 8 THEN 'Custódia'
        WHEN 9 THEN 'Recebível Avulso'
        WHEN 10 THEN 'Retirada de recebível de lote'
        WHEN 11 THEN 'Fluxo de Caixa'
        WHEN 12 THEN 'Devolução de cheques'
        ELSE 'Outro'
    END AS movconta_descricao_tipooperacao,
    usuario.nome AS usuario_nome,
    usuario.tipoUsuario AS usuario_tipoUsuario
FROM movconta
LEFT JOIN conta ON conta.codigo = movconta.conta
LEFT JOIN banco b ON b.codigo = conta.banco 
INNER JOIN movcontarateio colunasmovcontaRateio ON colunasmovcontaRateio.movconta = movconta.codigo
LEFT JOIN planoconta pc ON colunasmovcontaRateio.planoconta = pc.codigo 
LEFT JOIN centrocusto cc ON colunasmovcontaRateio.centrocusto = cc.codigo 
INNER JOIN empresa ON empresa.codigo = movconta.empresa
LEFT JOIN pessoa ON pessoa.codigo = movconta.pessoa
LEFT JOIN fornecedor ON pessoa.codigo = fornecedor.pessoa 
LEFT JOIN lote ON lote.codigo = movconta.lote
LEFT JOIN caixa ON caixa.codigo = (
    SELECT MAX(x1.caixa) FROM caixamovconta x1 WHERE x1.movconta = movconta.codigo
)
INNER JOIN usuario ON usuario.codigo = movconta.usuario
LEFT JOIN nfseemitida ON movconta.codigo = nfseemitida.movconta
LEFT JOIN tipodocumento tdoc ON tdoc.codigo = colunasmovcontaRateio.tipodocumento
LEFT JOIN formapagamento fpg ON fpg.codigo = colunasmovcontaRateio.formapagamento;