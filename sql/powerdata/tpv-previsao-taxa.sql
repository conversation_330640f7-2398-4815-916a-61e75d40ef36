select
--  sql.empresa_financeiro,
-- sum(sql.valor) as total
--  /*
sql.*,
--
e.nome                  as   Unidade,
e.cod_empresafinanceiro as   Unidade_IdPacto,
e.co<PERSON><PERSON>e            as   Unidade_IdRede,
uf.sigla                as   Unidade_uf,
cid.nome                as   Unidade_cidade,
case
    when sql.contrato is not null then p2.descricao
    when sql.venda_avulsa is not null then 'VENDA AVULSA'
    else 'OUTROS' end as Produto_Tipo,
--
us.nome as usuario_nome,
pc.nome as Contrato_Consultor,
--
CASE
    WHEN con.situacaocontrato = 'RN' THEN 'Renovação'
    WHEN con.situacaocontrato = 'MA' THEN 'Matricula'
    WHEN con.situacaocontrato = 'RE' THEN 'Rematricula'
    WHEN con.situacaocontrato is null THEN '_Outros'
    else con.situacaocontrato
    END as Contrato_Tipo,
--
con.situacao          as <PERSON><PERSON><PERSON>_<PERSON>ua<PERSON>,
CASE
    WHEN con.origemsistema = 1 THEN 'Academia'::text
    WHEN con.origemsistema = 2 THEN 'Agenda Web'::text
    WHEN con.origemsistema = 3 THEN 'Pacto Treino'::text
    WHEN con.origemsistema = 4 THEN 'App Treino'::text
    WHEN con.origemsistema = 5 THEN 'App Professor'::text
    WHEN con.origemsistema = 6 THEN 'Autoatendimento'::text
    WHEN con.origemsistema = 7 THEN 'Site Vendas'::text
    WHEN con.origemsistema = 8 THEN 'Buzz Lead'::text
    WHEN con.origemsistema = 9 THEN 'Vendas online'::text
    WHEN con.origemsistema = 10 THEN 'App do consultor'::text
    WHEN con.origemsistema = 11 THEN 'Booking Gympass'::text
    WHEN con.origemsistema = 12 THEN 'Fila de espera'::text
    WHEN con.origemsistema = 13 THEN 'Importação API'::text
    ELSE 'NÃO DEFINIDO'::text
    END AS Contrato_Origem,
con.vigenciade as contrato_validode,
con.vigenciaateajustada  as contrato_validoate,
con.valorfinal           as contrato_valorfinal,  -- para descobrir os contratos de valor zerado...
con.bolsa                as contrato_bolsa,
--
case
    when ( select max(1) from cliente cl where cl.pessoa = sql.pessoa ) = 1 then 'CLIENTE'
    when ( select max(1) from colaborador cl where cl.pessoa = sql.pessoa) = 1 then 'COLABORADOR'
    else 'CONSUMIDOR'
    end as tipo_pessoa,
case when (pescliente.nome is null) and (sql.consumidor is not null) then sql.consumidor
     else pescliente.nome
    end  as cliente_Nome,
pescliente.cfp   as cliente_cpf,
pescliente.sexo  as cliente_Sexo,
pescliente.datacadastro  as cliente_DataCadastro
--    */
from (
---
---INICIO Faturamento
         select
             'FATURAMENTO' as tipo_registro,
             mprod.empresa,
             mprod.pessoa as pessoa,
             mprod.contrato as contrato,
             mprod.vendaavulsa as venda_avulsa,
             mprod.valorfaturado::numeric                      as valor,
                 mprod.datalancamento                              as dt_faturamento,
             to_timestamp(mprod.mesreferencia::text, 'MM/YYYY'::text) as dt_competencia,
             null as dt_recebimento,
             null as dt_receita,
             null as dt_receita_prev,
             null as dt_receita_original,
             null::timestamp as dt_vencimento,
--
                 case
                     when fp.codigo is not null then	fp.tipoformapagamento
                     when bp.codigo is not null then 'BB'
                     when px.codigo is not null then 'PX'
                     when acc.codigo is not null then 'CA'
                     when abc.codigo is not null then 'BB'
                     when fpu.codigo is not null then fpu.tipoformapagamento
                     else null end AS tipo_forma_pagamento,
--
             case
                 when fp.codigo is not null then	fp.descricao
                 when bp.codigo is not null then 'BOLETO BANCÁRIO'
                 when px.codigo is not null then 'PIX'
                 when acc.codigo is not null then 'CARTÃO RECORRENTE'
                 when abc.codigo is not null then 'BOLETO BANCÁRIO'
                 when fpu.codigo is not null then fpu.descricao
                 else 'SEM COBRANÇA CADASTRADA' end AS forma_pagamento_descricao,
--
             case
                 when mp.conveniocobranca is not null then mp.conveniocobranca
                 when ccb.codigo is not null then ccb.codigo
                 when px.codigo is not null then px.conveniocobranca
                 when acc.codigo is not null then acc.codigo
                 when abc.codigo is not null then abc.codigo
                 else null end AS convenio,
--
             mprod.situacao as parcela_status,
             mprod.responsavellancamento as usuario,
             mprod.descricao  as descricao,
             case when (mp.pessoa is null and va.codigo is not null) then va.nomecomprador else '' end as consumidor,
             null as  nota_numero,
             null::timestamp as nota_dataemissao,

                 null as extrato_taxa_sistema,
             null as extrato_taxa_adquirente,
             null as extrato_antecipacao,
             null as extrato_dias_antecipacao,
             null as extrato_taxa_antecipacao


         from movproduto mprod
                  inner join produto prod on mprod.produto = prod.codigo
                  left join vendaavulsa va on mprod.vendaavulsa = va.codigo
                  left join movpagamento mp on mp.codigo = (select max(movpagamento) from pagamentomovparcela pmp
                                                                                              inner join movprodutoparcela mpp on pmp.movparcela = mpp.movparcela
                                                            where mpp.movproduto = mprod.codigo)
                  left join formapagamento fp on fp.codigo = mp.formapagamento
                  left join cliente cli ON cli.pessoa = mprod.pessoa
                  left join pix px on px.codigo = (select max(pm.pix) from pixmovparcela pm
                                                                               inner join movprodutoparcela mpp on mpp.movparcela = pm.movparcela
                                                   where mpp.movproduto = mprod.codigo)
                  left join boleto bp on bp.codigo = (select max(bm.boleto) from boletomovparcela bm
                                                                                     inner join boleto bol on bol.codigo = bm.boleto
                                                                                     inner join movprodutoparcela mpp on mpp.movparcela = bm.movparcela
                                                      where bol.situacao in (3,4,5) and mpp.movproduto = mprod.codigo)
                  left join conveniocobranca ccb on ccb.codigo = bp.conveniocobranca
                  left join autorizacaocobrancacliente auc on auc.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 1)
                  left join conveniocobranca acc on acc.codigo = auc.conveniocobranca
                  left join autorizacaocobrancacliente aub on aub.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 3)
                  left join conveniocobranca abc on abc.codigo = aub.conveniocobranca
                  left join movpagamento mpu on mpu.codigo = (select max(mp1.codigo) from movpagamento mp1 where mp1.pessoa = mprod.pessoa)
                  left join formapagamento fpu on fpu.codigo = mpu.formapagamento
              --
         WHERE prod.tipoproduto not in ('DE', 'DR', 'MC', 'CC','RD','DV','DC')
---FIM Faturamento
--
--
         union all
         --
--
--
---INICIO Recebido
         select
             'RECEBIDO'                AS tipo_registro,
             mp.empresa,
             mp.pessoa as pessoa,
             case
                 when rp.contrato is not null then rp.contrato
                 else (select
                           max(m1.contrato)
                       from movparcela m1
                                inner join pagamentomovparcela p1 on p1.movparcela = m1.codigo
                       where p1.movpagamento = mp.codigo) end as contrato,

             (select
                  max(m1.vendaavulsa)
              from movparcela m1
                       inner join pagamentomovparcela p1 on p1.movparcela = m1.codigo
              where p1.movpagamento = mp.codigo) as venda_avulsa,
             - -
                 case
                     when fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX') then mp.valortotal::numeric
			 when cc.codigo is not null then cc.valor::numeric
			 when ch.codigo is not null then ch.valor::numeric
			 else null end as valor,
-- 
            (select min(mpro.datalancamento)
             from movproduto mpro
             inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo
             inner join pagamentomovparcela pmp on pmp.movparcela = mpp.movparcela
             where pmp.movpagamento = mp.codigo) AS dt_faturamento,
    --
             null as dt_competencia,
    --
             mp.datalancamento AS dt_recebimento,
    --
            case
            when fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX') then mp.datapagamento::timestamp
            when cc.codigo is not null then cc.datacompesancao::timestamp
            when ch.codigo is not null then ch.datacompesancao::timestamp
            else null::timestamp end as dt_receita,
            case
            when fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX') then mp.datapagamento::timestamp
            when cc.codigo is not null then cc.datacompesancao::timestamp
            when ch.codigo is not null then ch.datacompesancao::timestamp
            else null::timestamp end as dt_receita_prev,
    -- 
            case
            when fp.tipoformapagamento in ('AV','CD','CC','BB','CO','TB','PX') then mp.datapagamentooriginal::timestamp
            when (fp.tipoformapagamento = 'CA' and ei.codigo is not null and ei.datapgtooriginalantesdaantecipacao is not null) then ei.datapgtooriginalantesdaantecipacao::timestamp
	        when (cc.dataoriginal IS NULL and cc.codigo is not null) then cc.datacompesancao::timestamp
            when cc.codigo is not null then cc.dataoriginal::timestamp
	        when (ch.dataoriginal is null and ch.codigo is not null) then ch.datacompesancao::timestamp
            when ch.codigo is not null then ch.dataoriginal::timestamp
            else null::timestamp end as dt_receita_original,
    -- 
            NULL as dt_vencimento,
            fp.tipoformapagamento as tipo_forma_pagamento,
            fp.descricao as forma_pagamento_descricao,
            mp.conveniocobranca as convenio,
            'PG' as parcela_status,
            mp.responsavelpagamento as usuario,
            '' as descricao,
            case when mp.pessoa is null then mp.nomepagador else '' end as consumidor,
            nf.numeronota as  nota_numero,
            nf.dataemissao as nota_dataemissao,
            (select
	        max (tc.taxa)
	        from taxacartao tc
	        left join operadoracartao opt on opt.codigo = tc.bandeira
	        left join adquirente adt on adt.codigo = tc.adquirente
	        where (current_date between tc.vigenciainicial::date and tc.vigenciafinal::date or tc.vigenciainicial::date >= current_date)
	        and tc.formapagamento = fp.codigo
	        and (mp.operadoracartao = tc.bandeira or mp.adquirente = tc.adquirente)) as extrato_taxa_sistema,
	        ei.taxa as extrato_taxa_adquirente,
            ei.antecipacao as extrato_antecipacao,
            extract(day from ei.datapgtooriginalantesdaantecipacao - ei.dataprevistapagamento) as extrato_dias_antecipacao,
            ei.taxacalculadaantecipacao as extrato_taxa_antecipacao
         from movpagamento mp
             inner join formapagamento fp on fp.codigo = mp.formapagamento
             left join recibopagamento rp on rp.codigo = mp.recibopagamento
             LEFT JOIN movpagamento mpa1 ON mpa1.movpagamentoorigemcredito = mp.codigo AND mpa1.valor > 0 AND mpa1.credito AND mpa1.movpagamentoorigemcredito IS NOT NULL and fp.tipoformapagamento  = 'CA'
             left join cartaocredito cc on (cc.movpagamento = mp.codigo or cc.movpagamento = mpa1.codigo) AND cc.situacao = 'EA'
             left join cheque ch on ch.movpagamento = mp.codigo and ch.situacao = 'EA'
             left join notafiscal nf on nf.nfseemitida = (select max(nfe.codigo)
             from nfseemitida nfe
             where (nfe.cartaocredito = cc.codigo or  nfe.cheque = ch.codigo or
             nfe.movpagamento = mp.codigo))
             left join extratodiarioitem ei on ei.codigo = (select max(ei2.codigo)
             from extratodiarioitem ei2
             where ei2.antecipacao is true
             and ((ei2.codigocartaocredito IS NOT NULL and ei2.codigocartaocredito = cc.codigo) or
             (cc.codigo is null and ei2.codigomovpagamento IS NOT NULL and ei2.codigomovpagamento = mp.codigo))
             and ei2.datapgtooriginalantesdaantecipacao is not null
             and ei2.tipoconciliacao = 4)
         where mp.credito = false
---FIM Recebido
--
--
         union all
         --
--
---INICIO Previsao
         SELECT
             'PREVISAO'                AS tipo_registro,
             mp.empresa,
             mp.pessoa AS pessoa,
             mp.contrato as contrato,
             mp.vendaavulsa as venda_avulsa,
             mp.valorparcela::numeric          AS valor,
--
             (select min(mpro.datalancamento)
             from movproduto mpro
             inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo
             where mpp.movparcela = mp.codigo) AS dt_faturamento,
--
--
             null as dt_competencia,
             null as dt_recebimento,
             null as dt_receita,
             case
             when bp.codigo is not null then (mp.datavencimento + interval '2 days')::timestamp    -- Boleto
             when px.codigo is not null then mp.datavencimento::timestamp    -- Pix
             when acc.codigo is not null then (mp.datavencimento + interval '30 days')::timestamp
             when abc.codigo is not null then (mp.datavencimento + interval '2 days')::timestamp
             when (fpu.codigo is not null and fpu.tipoformapagamento = 'CA') then (mp.datavencimento + interval '30 days')::timestamp
             when (fpu.codigo is not null and fpu.tipoformapagamento = 'BB') then (mp.datavencimento + interval '2 days')::timestamp
             else mp.datavencimento::timestamp end AS dt_receita_prev,
--
             null::timestamp as dt_receita_original,
--
             mp.datavencimento::timestamp as dt_vencimento,
--
             case
             when bp.codigo is not null then 'BB'
             when px.codigo is not null then 'PX'
             when acc.codigo is not null then 'CA'
             when abc.codigo is not null then 'BB'
             when fpu.codigo is not null then fpu.tipoformapagamento
             else null end AS tipo_forma_pagamento,
--
             case
             when bp.codigo is not null then 'BOLETO BANCÁRIO'
             when px.codigo is not null then 'PIX'
             when acc.codigo is not null then 'CARTÃO RECORRENTE'
             when abc.codigo is not null then 'BOLETO BANCÁRIO'
             when fpu.codigo is not null then fpu.descricao
             else 'SEM COBRANÇA CADASTRADA' end AS forma_pagamento_descricao,
--
             case
             when ccb.codigo is not null then ccb.codigo
             when px.codigo is not null then px.conveniocobranca
             when acc.codigo is not null then acc.codigo
             when abc.codigo is not null then abc.codigo
             else null end AS convenio,
             mp.situacao as parcela_status,
             mp.responsavel as usuario,
             mp.descricao as descricao,
             case when (mp.pessoa is null and va.codigo is not null) then va.nomecomprador else '' end as consumidor,
             null as  nota_numero,
             null::timestamp as nota_dataemissao,

             null as extrato_taxa_sistema,
             null as extrato_taxa_adquirente,
             null as extrato_antecipacao,
             null as extrato_dias_antecipacao,
             null as extrato_taxa_antecipacao
         FROM   movparcela mp
             left join cliente cli ON cli.pessoa = mp.pessoa
             left join pix px on px.codigo = (select max(pm.pix) from pixmovparcela pm where pm.movparcela = mp.codigo)
             left join boleto bp on bp.codigo = (select max(bm.boleto) from boletomovparcela bm inner join boleto bol on bol.codigo = bm.boleto where bol.situacao in (3,4,5) and bm.movparcela = mp.codigo)
             left join conveniocobranca ccb on ccb.codigo = bp.conveniocobranca
             left join autorizacaocobrancacliente auc on auc.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 1)
             left join conveniocobranca acc on acc.codigo = auc.conveniocobranca
             left join autorizacaocobrancacliente aub on aub.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 3)
             left join conveniocobranca abc on abc.codigo = aub.conveniocobranca
             left join movpagamento mpu on mpu.codigo = (select max(mp1.codigo) from movpagamento mp1 where mp1.pessoa = mp.pessoa)
             left join formapagamento fpu on fpu.codigo = mpu.formapagamento
             left join vendaavulsa va on mp.vendaavulsa = va.codigo
         WHERE  mp.situacao IN ('EA','CA')
           AND mp.valorparcela > 0
--
---FIM Previsao
     ) as sql
--  Para pegar dados da empresa
    inner join empresa e on e.codigo = sql.empresa
    left join estado uf on uf.codigo = e.estado
    left join cidade cid on cid.codigo = e.cidade
--  Para pegar dados do contrato e do Produto do plano -------------------------------------------------------------
    left join contrato con on con.codigo = sql.contrato
    left join plano p on p.codigo  = con.plano
    left join produto p2 on p2.codigo = p.produtopadraogerarparcelascontrato
    left join usuario us on us.codigo = sql.usuario
    left join colaborador c2  on c2.codigo = con.consultor
    left join pessoa pc on pc.codigo = c2.pessoa
--  Para pegar dados pessoa que fez a compra...
    left join pessoa pesCliente on sql.pessoa = pescliente.codigo
    --
--MAX
	where 1=1
	 and( sql.dt_faturamento::date between '01/01/2022' and '01/06/2050'
		or sql.dt_recebimento::date between '01/01/2022' and '10/04/2050'
		or sql.dt_vencimento::date between  '01/01/2022' and '10/04/2050'
		)
--and sql.tipo_registro = 'RECEBIDO'
-- --   and sql.dt_recebimento::date between '10/04/2023' and '10/04/2023'
----    and sql.tipo_registro = 'PREVISAO'
----   and sql.parcela_status in ('CA')
--   and sql.tipo_registro = 'FATURAMENTO'
--   and sql.consumidor <> ''
--   and sql.empresa_financeiro in (6465)
--   and sql.pessoa in (105053)
--   and sql.pessoa = 1564
--    and sql.nota_numero is not null
--   order by sql.nota_numero 
--   group by sql.empresa_financeiro

--faturamento
--where sql.dt_faturamento::date between '01/03/2023' and '31/03/2023'
--and sql.tipo_registro = 'FATURAMENTO'

--receita
--where sql.dt_receita::date between '01/03/2023' and '31/03/2023'
--where sql.dt_receita_prev::date between '01/03/2023' and '31/03/2023'
--and sql.tipo_registro = 'PREVISAO'

--faturamento recebido
--where sql.dt_recebimento::date between '01/02/2023' and '28/02/2023'

--and not exists(select codigo from autorizacaocobrancacliente  where ativa and cliente in (select codigo from cliente where pessoa = sql.pessoa))



/*

select count(*) as qtd, current_database() as banco from taxacartao where current_date between vigenciainicial::date and vigenciafinal::date or vigenciainicial::date >= current_date


select 
tc.taxa ,
tc.formapagamento ,
opt.descricao as operadora,
adt.nome as adquirente 
from taxacartao tc
left join operadoracartao opt on opt.codigo = tc.bandeira
left join adquirente adt on adt.codigo = tc.adquirente
where (current_date between tc.vigenciainicial::date and tc.vigenciafinal::date or tc.vigenciainicial::date >= current_date)
and 

*/