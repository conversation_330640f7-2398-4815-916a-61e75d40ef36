-- https://pacto.atlassian.net/browse/IA-1001
-- Cópia da mov_contrato_saida.sql com a categoria
WITH
-- 1) Data mínima
min_data AS (
    SELECT DATE_TRUNC('day', MIN(c.vigenciade)) AS data_inicial
    FROM contrato c
),
-- 2) Dias desde min_data até hoje
dias AS (
    SELECT GENERATE_SERIES(
        (SELECT data_inicial FROM min_data), 
        DATE_TRUNC('day', CURRENT_DATE),
        INTERVAL '1 day'
    ) AS dia
),
-- 3) Lista de empresas com cod_empresafinanceiro
lista_empfinanceiro AS (
    SELECT 
        e.codigo AS codigo_empresa,
        e.cod_empresafinanceiro AS cod_emp_fin
    FROM empresa e
    WHERE e.cod_empresafinanceiro IS NOT NULL
),
-- 4) Geração dos dados por dia, empresa e categoria
dados AS (
    SELECT
        d.dia,
        le.cod_emp_fin AS cod_empresafinanceiro,
        ca.nome AS nome_categoria,
        -- DESISTENTES no dia
        COUNT(DISTINCT CASE 
            WHEN h.tipohistorico = 'DE' THEN c.codigo 
        END) AS desistentes_dia,
        -- CANCELADOS no dia
        COUNT(DISTINCT CASE 
            WHEN h.tipohistorico = 'CA' THEN c.codigo 
        END) AS cancelados_dia,
        -- TRANCADOS no dia
        COUNT(DISTINCT CASE 
            WHEN h.tipohistorico = 'TR' THEN c.codigo 
        END) AS trancados_dia,
        -- DESISTENTES sem bolsa
        COUNT(DISTINCT CASE 
            WHEN h.tipohistorico = 'DE' AND c.bolsa <> TRUE THEN c.codigo 
        END) AS desistentes_sem_bolsa_dia,
        -- CANCELADOS sem bolsa
        COUNT(DISTINCT CASE 
            WHEN h.tipohistorico = 'CA' AND c.bolsa <> TRUE THEN c.codigo 
        END) AS cancelados_sem_bolsa_dia
    FROM dias d
    CROSS JOIN lista_empfinanceiro le
    LEFT JOIN contrato c ON c.empresa = le.codigo_empresa
    LEFT JOIN historicocontrato h ON h.contrato = c.codigo
        AND h.datainiciosituacao >= d.dia
        AND h.datainiciosituacao < d.dia + INTERVAL '1 day'
    LEFT JOIN pessoa pe ON c.pessoa = pe.codigo
    LEFT JOIN cliente cl ON pe.codigo = cl.pessoa
    LEFT JOIN categoria ca ON cl.categoria = ca.codigo
    GROUP BY d.dia, le.cod_emp_fin, ca.nome
)
-- 5) Resultado final
SELECT 
    dia,
    cod_empresafinanceiro,
    nome_categoria,
    desistentes_dia,
    cancelados_dia,
    trancados_dia,
    desistentes_sem_bolsa_dia,
    cancelados_sem_bolsa_dia
FROM dados
ORDER BY dia DESC