-- PORCENTAGEM DE CONVERSAO DE LEADS
WITH meta AS (
    -- Subconsulta para contar o total de metas por data
    SELECT 
        e.nome as empresa,
        COALESCE(p.dia::date, i.datalancamento::date) AS data_meta,
        COUNT(*) AS total_meta 
    FROM lead l 
    LEFT JOIN passivo p ON p.codigo = l.passivo 
    LEFT JOIN indicado i ON i.codigo = l.indicado 
    LEFT JOIN indicacao ii ON ii.codigo = i.indicacao  
    inner join empresa e on e.codigo = l.empresa
    WHERE ((p.dia >= '2024-01-01 00:00:00') OR (i.datalancamento >= '2024-01-01 00:00:00')) 
    AND (p.lead = true OR i.lead = true) 
    AND (p.metaextra IS NULL OR p.metaextra = 'f')
    GROUP BY e.nome, COALESCE(p.dia::date, i.datalancamento::date)
), 
atingida AS (
    -- Subconsulta para contar o total de metas atingidas por data
    SELECT 
        e.nome as empresa,
        COALESCE(p.dia::date, i.datalancamento::date) AS data_meta,
        COUNT(*) AS total_atingida 
    FROM lead l 
    LEFT JOIN passivo p ON p.codigo = l.passivo 
    LEFT JOIN indicado i ON i.codigo = l.indicado 
    LEFT JOIN indicacao ii ON ii.codigo = i.indicacao 
    INNER JOIN situacaoclientesinteticodw scs ON (scs.codigocliente = l.cliente OR scs.codigocliente = i.cliente) 
    INNER JOIN pessoa p2 ON p2.codigo = scs.codigopessoa 
    INNER JOIN contrato c ON c.pessoa = p2.codigo  
    inner join empresa e on e.codigo = l.empresa
    WHERE ((p.dia >= '2024-01-01 00:00:00') OR (i.datalancamento >= '2024-01-01 00:00:00'))
    AND (p.lead = true OR i.lead = true) 
    AND (p.metaextra IS NULL OR p.metaextra = 'f')  
    AND (l.cliente IS NOT NULL OR i.cliente IS NOT NULL) 
    AND scs.codigocontrato IS NOT NULL
    GROUP BY e.nome, COALESCE(p.dia::date, i.datalancamento::date)
)
-- Consulta final para calcular a porcentagem por data
SELECT 
m.empresa,
    COALESCE(m.data_meta, a.data_meta) AS data_meta,
    CASE 
        WHEN COALESCE(m.total_meta, 0) = 0 THEN 0 -- Se o total de metas for zero, retorna 0%
        ELSE (COALESCE(a.total_atingida, 0)::decimal / m.total_meta::decimal) * 100
    END AS porcentagem
FROM meta m
FULL JOIN atingida a ON m.data_meta = a.data_meta
ORDER BY data_meta;