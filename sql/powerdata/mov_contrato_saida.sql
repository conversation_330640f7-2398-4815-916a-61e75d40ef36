WITH 
----------------------------------------------------------------------
-- 1) Pega a data inicial diária (menor vigenciaDe para empresa = 1)
----------------------------------------------------------------------
min_data AS (
    SELECT 
        DATE_TRUNC('day', MIN(c.vigenciade)) AS data_inicial
    FROM contrato c
),
----------------------------------------------------------------------
-- 2) Gera todos os dias desde min_data até hoje
----------------------------------------------------------------------
dias AS (
    SELECT 
        GENERATE_SERIES(
            (SELECT data_inicial FROM min_data), 
            DATE_TRUNC('day', CURRENT_DATE),
            INTERVAL '1 day'
        ) AS dia
),
----------------------------------------------------------------------
-- 3) Lista de cod_empresafinanceiro (ou seja, de empresas)
--    Ajuste o WHERE se quiser só algumas empresas específicas.
----------------------------------------------------------------------
lista_empfinanceiro AS (
    SELECT 
        e.codigo                 AS codigo_empresa,
        e.cod_empresafinanceiro AS cod_emp_fin
    FROM empresa e
    -- Se quiser mostrar só empresas com c.empresa=1, não faria muito sentido,
    -- pois c.empresa=1 indica que "contrato.empresa = 1", mas aqui e.codigo
    -- é a PK da tabela empresa. Ajuste conforme necessidade.
    WHERE e.cod_empresafinanceiro IS NOT NULL
),
----------------------------------------------------------------------
-- 4) Cross Join + Lateral Joins para desistentes e cancelados
----------------------------------------------------------------------
dados AS (
    SELECT
        d.dia,
        le.cod_emp_fin AS cod_empresafinanceiro,
        -- DESISTENTES no dia
        COALESCE((
            SELECT COUNT(DISTINCT c.codigo)
            FROM contrato c
            JOIN historicocontrato h ON h.contrato = c.codigo
            WHERE c.empresa      = le.codigo_empresa
              AND h.tipohistorico = 'DE'
              -- "No dia" = datainiciosituacao >= d.dia e < d.dia + 1 dia
              AND h.datainiciosituacao >= d.dia
              AND h.datainiciosituacao <  d.dia + INTERVAL '1 day'
        ), 0) AS desistentes_dia,
        -- CANCELADOS no dia
        COALESCE((
            SELECT COUNT(DISTINCT c.codigo)
            FROM contrato c
            JOIN historicocontrato h ON h.contrato = c.codigo
            WHERE c.empresa      = le.codigo_empresa
              AND h.tipohistorico = 'CA'
              AND h.datainiciosituacao >= d.dia
              AND h.datainiciosituacao <  d.dia + INTERVAL '1 day'
        ), 0) AS cancelados_dia,
        -- TRANCADOS no dia 
        COALESCE((
            SELECT COUNT(DISTINCT c.codigo)
            FROM contrato c
            JOIN historicocontrato h ON h.contrato = c.codigo
            WHERE c.empresa      = le.codigo_empresa
              AND h.tipohistorico = 'TR'
              AND h.datainiciosituacao >= d.dia
              AND h.datainiciosituacao <  d.dia + INTERVAL '1 day'
        ), 0) AS trancados_dia,
        -- DESISTENTES sem bolsa no dia
        COALESCE((
            SELECT COUNT(DISTINCT c.codigo)
            FROM contrato c
            JOIN historicocontrato h ON h.contrato = c.codigo
            WHERE c.empresa      = le.codigo_empresa
              AND h.tipohistorico = 'DE'
              -- "No dia" = datainiciosituacao >= d.dia e < d.dia + 1 dia
              AND h.datainiciosituacao >= d.dia
              AND h.datainiciosituacao <  d.dia + INTERVAL '1 day'
              AND c.bolsa <> TRUE
        ), 0) AS desistentes_sem_bolsa_dia,
        -- CANCELADOS sem bolsa no dia
        COALESCE((
            SELECT COUNT(DISTINCT c.codigo)
            FROM contrato c
            JOIN historicocontrato h ON h.contrato = c.codigo
            WHERE c.empresa      = le.codigo_empresa
              AND h.tipohistorico = 'CA'
              AND h.datainiciosituacao >= d.dia
              AND h.datainiciosituacao <  d.dia + INTERVAL '1 day'
              AND c.bolsa <> TRUE
        ), 0) AS cancelados_sem_bolsa_dia
    FROM dias d
    CROSS JOIN lista_empfinanceiro le
)
----------------------------------------------------------------------
-- 5) Seleciona e ordena resultado final
----------------------------------------------------------------------
SELECT 
    dia,
    cod_empresafinanceiro,
    desistentes_dia,
    cancelados_dia,
    trancados_dia,
    desistentes_sem_bolsa_dia,
    cancelados_sem_bolsa_dia
FROM dados
ORDER BY dia desc

