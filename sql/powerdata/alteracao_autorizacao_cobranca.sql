with pessoas_com_exclusao as(
select
		distinct(pessoa)
from
		Log
where
		1 = 1
	and nomeEntidade ilike '%AUTORIZACAOCOBRANCACLIENTE%'
	and operacao ilike '%EXCLUSÃO%'
)

select
	distinct(date_trunc('min',
	l.dataalteracao))::timestamp dataalteracao,
	e.cod_empresafinanceiro,
	'cliente' as tipo_pessoa,
	l.nomeentidade,
	l.nomeentidadedescricao,
	l.chaveprimaria,
	l.nomecam<PERSON>,
	l.valorcampoanterior,
	l.valorcampoalterado,
	l.responsavelalteracao,
	l.operacao,
	l.codigo,
	l.pessoa
from
	Log l
inner join cliente cli 
		on
	cli.pessoa = l.pessoa
inner join empresa e
		on
	cli.empresa = e.codigo
where
	1 = 1
	and nomeEntidade ilike '%AUTORIZACAOCOBRANCACLIENTE%'
	and (operacao ilike '%EXCLUSÃO%'
		or operacao ilike '%INCLUSÃO%')
--	and nomecampo ilike '%Autorização%'
	--	and dataalteracao between '2024-05-01 00:00:00' and '2025-01-28 23:59:59'
	and l.pessoa in (
	select
		pessoa
	from
		pessoas_com_exclusao)
union all 
select
	distinct(date_trunc('min',
	l.dataalteracao))::timestamp dataalteracao,
	e.cod_empresafinanceiro,
	'colaborador' as tipo_pessoa,
	l.nomeentidade,
	l.nomeentidadedescricao,
	l.chaveprimaria,
	l.nomecampo,
	l.valorcampoanterior,
	l.valorcampoalterado,
	l.responsavelalteracao,
	l.operacao,
	l.codigo,
	l.pessoa
from
	Log l
inner join colaborador col
		on
	col.pessoa = l.pessoa
inner join empresa e
		on
	col.empresa = e.codigo
where
	1 = 1
	and nomeEntidade ilike '%AUTORIZACAOCOBRANCACLIENTE%'
	and (operacao ilike '%EXCLUSÃO%'
		or operacao ilike '%INCLUSÃO%')
--	and nomecampo ilike '%Autorização%'
	--	and dataalteracao between '2024-05-01 00:00:00' and '2025-01-28 23:59:59'
	and
 l.pessoa in (
	select
		pessoa
	from
		pessoas_com_exclusao)
order by
	dataalteracao asc,
	pessoa