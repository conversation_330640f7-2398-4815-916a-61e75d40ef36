SELECT
  obs.codigo as codigo_observacao,
  obs.observacao as observacao,
  obs.datacadastro as cadastro_observacao,
  obs.cliente as codigo_cliente,
  cli.empresacliente,
  e.nome                  as   Unidade,
  e.cod_empresafinance<PERSON> as   Unidade_IdPacto,
  e.codigo<PERSON>e            as   Unidade_IdRede
FROM
  clienteobservacao obs
  left join situacaoclientesinteticodw cli on obs.cliente = cli.codigocliente
  inner join empresa e on e.codigo = cli.empresacliente