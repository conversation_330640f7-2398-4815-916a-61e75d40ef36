SELECT 
    data,
    SUM(qtdindicacao)  AS qtdindicacao,
    SUM(qtdconvertido) AS qtdconvertido
FROM (
    SELECT 
        i.dia::DATE AS data, -- Convertendo i.dia para apenas a data
        0           AS qtdIndicacao,
        COUNT(i.codigo) AS qtdConvertido
    FROM indicacao i
    INNER JOIN indicado ind ON ind.indicacao = i.codigo
    LEFT JOIN cliente ci ON ci.codigo = ind.cliente
    LEFT JOIN pessoa pi ON pi.codigo = ci.pessoa
    LEFT JOIN contrato con ON con.pessoa = pi.codigo
    WHERE 1 = 1
        AND con.datalancamento::DATE >= i.dia::DATE
        AND con.datalancamento::DATE < i.dia::DATE + 20
        AND con.situacaocontrato IN ('MA', 'RE')
        AND NOT EXISTS (
            SELECT codigo 
            FROM contratooperacao 
            WHERE tipooperacao = 'CA' AND contrato = con.codigo
        )
        AND ind.empresa = 1
    GROUP BY i.dia::DATE -- Agrupando por dia
    UNION
    SELECT 
        i.dia::DATE AS data,
        COUNT(i.codigo) AS qtdIndicacao,
        0 AS qtdConvertido
    FROM indicacao i
    INNER JOIN indicado ind ON ind.indicacao = i.codigo
    WHERE 1 = 1
    GROUP BY i.dia::DATE -- Agrupando por dia
) AS foo
GROUP BY data;