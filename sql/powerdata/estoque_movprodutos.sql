-- Venda de produtos do estoque ----------------------
select
    mp.codigo as id_primario,
    e.cod_empresafinanceiro, 
	p.codigo as produto_codigo,
	p.descricao as produto_descricao,
	pe.estoque as estoque_atual,
	pe.estoqueminimo as estoque_minimo_atual,
	p.valorfinal as valor_atual,
	mp.datalancamento as venda_data,
	mp.precounitario as venda_precounitario,
	mp.quantidade as venda_quantidade,
	mp.totalfinal as venda_valor_total,
	hpe.saldoatual  as saldo_dia
from movproduto mp 
	inner join produto p on p.codigo = mp.produto 
	inner join produtoestoque pe on pe.produto = p.codigo
	left join empresa e on e.codigo = mp.empresa 
	left join historicoprodutoestoque hpe on hpe.codigo = (select max(codigo) from historicoprodutoestoque where produtoestoque = pe.codigo and dataalteracao::date = mp.datalancamento::date)
where mp.datalancamento::date >= '01/01/2024' 
order by mp.codigo 
--order by mp.datalancamento desc
--limit 20;