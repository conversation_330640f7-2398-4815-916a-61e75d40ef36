WITH 
-- CANCELADOS = CANCELADOS + DEPENDENTES CANCELADOS - CANCELADOS POR MUDANÇA DE plano
-- DESISTENTES = DESISTENTES + DEPENDENTES DESISTENTES
  ----------------------------------------------------------------------------
  -- 1) Pega a data de vigência mais antiga, truncada para o início do mês
  ----------------------------------------------------------------------------
  min_data AS (
    SELECT DATE_TRUNC('month', MIN(vigenciade)) AS start_month
    FROM contrato
    -- Sem filtro de empresa, pega o menor vigenciade de todas as empresas
  ),
  ----------------------------------------------------------------------------
  -- 2) Gera meses (início do mês) até o mês atual
  ----------------------------------------------------------------------------
  meses AS (
    SELECT generate_series(
      (SELECT start_month FROM min_data),
      DATE_TRUNC('month', CURRENT_DATE),
      INTERVAL '1 month'
    ) AS inicio_mes
  ),
  ----------------------------------------------------------------------------
  -- 3) Contagem de ativos e inativos por cod_empresafinanceiro + mês
  ----------------------------------------------------------------------------
  base AS (
    SELECT 
      e.cod_empresafinanceiro,
      m.inicio_mes AS mes,
      -- -------------------------
      -- Ativos
      -- -------------------------
      COUNT(DISTINCT c.codigo) AS ativos,
      -- -------------------------
      -- Alunos ativos 
      -- -------------------------
      COUNT(DISTINCT st.matricula) as alunos_ativos,
      -- -------------------------
      -- Dependentes dos ativos
      COUNT(DISTINCT cd.codigo) AS dependentes_ativos,
      -- -------------------------
      -- Alunos ativos (Matrícula)
      -- -------------------------
      COUNT(DISTINCT 
        CASE 
          WHEN (
            c.situacaocontrato = 'MA'
            AND c.vigenciade BETWEEN m.inicio_mes
                              AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
            ) THEN st.matricula
          ELSE NULL
        END
      ) AS alunos_ativos_matricula,
      -- -------------------------
      -- Alunos ativos (Rematrícula)
      -- -------------------------
      COUNT(DISTINCT 
        CASE 
          WHEN (
            c.situacaocontrato = 'RE'
            AND c.vigenciade BETWEEN m.inicio_mes 
                    AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
            ) THEN st.matricula
          ELSE NULL
        END
      ) AS alunos_ativos_rematricula,
      -- -------------------------
      -- Dependentes dos ativos (Matrícula)
      -- -------------------------
        COUNT(DISTINCT 
            CASE 
            WHEN (
                c.situacaocontrato = 'MA'
                AND c.vigenciade BETWEEN m.inicio_mes 
                        AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
                ) THEN cd.codigo
            ELSE NULL
            END
        ) AS dependentes_ativos_matricula,
      -- -------------------------
      -- Dependentes dos ativos (Rematrícula)
      -- -------------------------
        COUNT(DISTINCT 
            CASE 
            WHEN (
                c.situacaocontrato = 'RE'
                AND c.vigenciade BETWEEN m.inicio_mes 
                        AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
                ) THEN cd.codigo
            ELSE NULL
            END
        ) AS dependentes_ativos_rematricula,
      -- -------------------------
      -- -------------------------
      -- Inativos (subselect)
      -- -------------------------
      (
        SELECT COUNT(x.codigo)
        FROM contrato x
        JOIN empresa e2 ON x.empresa = e2.codigo
        WHERE e2.cod_empresafinanceiro = e.cod_empresafinanceiro
          AND x.vigenciaateajustada 
                BETWEEN m.inicio_mes 
                    AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
          AND x.contratoresponsavelrenovacaomatricula = 0
          AND x.situacao IN ('IN','CA')
      ) AS inativos,
      -- Cancelados (subselect)
      -- -------------------------
      (
        SELECT COUNT(DISTINCT cli.matricula) -- + COUNT(DISTINCT cd.cliente)
        -- *
        FROM cliente cli
            LEFT JOIN situacaoclientesinteticodw st ON st.codigocliente = cli.codigo
        LEFT JOIN (
          select c.* 
          from contratodependente c
          left join cliente cli on cli.codigo = c.cliente
        --   where cli.situacao not in ('AT')
                ) cd on cd.titular = st.codigocliente
            and cd.dataFinalAjustada
            -- BETWEEN '2025-02-01 00:00:00' AND '2025-02-28 23:59:59'
                BETWEEN m.inicio_mes
                    AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
            LEFT JOIN contrato con ON con.codigo = st.codigocontrato
            LEFT JOIN contratooperacao co ON co.contrato = con.codigo
            AND co.tipooperacao = 'CA'
            LEFT JOIN justificativaoperacao jo ON co.tipojustificativa = jo.codigo
            JOIN empresa e2 ON con.empresa = e2.codigo
        WHERE 1 = 1
            -- AND con.vigenciaateajustada BETWEEN '2025-02-01 00:00:00' AND '2025-02-28 23:59:59'
            AND con.vigenciaateajustada
                BETWEEN m.inicio_mes
                    AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
            AND st.situacaocontrato ILIKE 'CA'
            AND jo.descricao NOT LIKE '%TROCA DE PLANO%'
            AND jo.descricao NOT LIKE '%MUDANÇA DE PLANO%'
            -- AND cd.codigo IS NOT NULL
      ) as cancelados,
      -- -------------------------
      -- Desistentes (subselect)
      -- -------------------------
      (
        SELECT 
        COUNT(DISTINCT st.matricula) --+ COUNT(DISTINCT cd.cliente)
        -- st.matricula, cd.codigo
        FROM contrato c
        JOIN historicocontrato h ON h.contrato = c.codigo
        JOIN empresa e2 ON c.empresa = e2.codigo
        JOIN situacaoclientesinteticodw st on c.codigo = st.codigocontrato
          AND h.tipohistorico = 'DE'
          AND h.datainiciosituacao 
            -- BETWEEN '2025-02-01 00:00:00' AND '2025-02-28 23:59:59'
            BETWEEN m.inicio_mes 
                AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
        LEFT JOIN (
          select c.* 
          from contratodependente c
          left join cliente cli on cli.codigo = c.cliente
        --   where cli.situacao not in ('AT')
                ) cd on cd.titular = st.codigocliente
            and cd.dataFinalAjustada
            -- BETWEEN '2025-02-01 00:00:00' AND '2025-02-28 23:59:59'
                BETWEEN m.inicio_mes
                    AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
        -- where cd.codigo is not null
        ) AS desistentes
    FROM meses m
    JOIN contrato c 
      ON (
        -- Regra para "ativos": 
        --  A data de início do mês está entre vigenciaDe e vigenciaAteAjustada
        m.inicio_mes BETWEEN c.vigenciade AND c.vigenciaateajustada
        OR
        --  OU (situação em 'MA','RE','TF' e vigenciaDe dentro do mês)
        (
          c.situacaocontrato IN ('MA','RE','TF')
          AND c.vigenciade BETWEEN m.inicio_mes 
                             AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
        )
      )
    JOIN situacaoclientesinteticodw st ON st.codigocontrato = c.codigo
    -- Puxa cod_empresafinanceiro da tabela empresa
    JOIN empresa e ON e.codigo = c.empresa
    LEFT join contratodependente cd ON cd.titular = st.codigocliente
        AND cd.datafinalajustada
        BETWEEN m.inicio_mes
            AND (m.inicio_mes + INTERVAL '1 month' - INTERVAL '1 day')
    GROUP BY
      e.cod_empresafinanceiro,
      m.inicio_mes
  )
  ----------------------------------------------------------------------------
  -- 4) Passo final: soma e calcula churn
  ----------------------------------------------------------------------------
SELECT
  base.cod_empresafinanceiro,
  base.mes,
  SUM(base.inativos) AS inativos,
  SUM(base.ativos)   AS ativos,
  SUM(base.alunos_ativos) - SUM(base.alunos_ativos_matricula) - SUM(base.alunos_ativos_rematricula) AS alunos_ativos,
  SUM(base.dependentes_ativos) AS dependentes_ativos,
  SUM(base.alunos_ativos_matricula) AS alunos_ativos_matricula,
  SUM(base.alunos_ativos_rematricula) AS alunos_ativos_rematricula,
  SUM(base.dependentes_ativos_matricula) AS dependentes_ativos_matricula,
  SUM(base.dependentes_ativos_rematricula) AS dependentes_ativos_rematricula,
  SUM(base.cancelados) AS alunos_cancelados,
  SUM(base.desistentes) AS alunos_desistentes,
  CASE 
    WHEN SUM(base.ativos) = 0 THEN 0
    ELSE (SUM(base.inativos)::numeric / SUM(base.ativos)) 
  END AS churn,
  CASE
    WHEN SUM(base.alunos_ativos) = 0 THEN 0  
    ELSE  (SUM(base.cancelados)::numeric + SUM(base.desistentes)) / (SUM(base.alunos_ativos) + SUM(base.alunos_ativos_matricula) + SUM(base.alunos_ativos_rematricula))
  END as churn_recalculado
FROM base
GROUP BY 
  base.cod_empresafinanceiro,
  base.mes
ORDER BY 
  base.cod_empresafinanceiro,
  base.mes
