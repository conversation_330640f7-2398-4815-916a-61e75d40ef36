select e.cod_empresaf<PERSON><PERSON><PERSON>,
       e.nome as unidade,
       prof.nome as professor_mon<PERSON><PERSON>,
	p.codigo as codigoprograma,
	datalancamento,
	diasporsemana,
	genero,
	p.nome as nomeprograma,
	totalaulasprevistas,
	prof.codigocolaborador,
	f.nome as nomeficha,
	f.sexo as sexoficha,
	regexp_replace(f.mensa<PERSON>, E'[\\n\\r]+', ' ', 'g' ) as mensagemaluno,
	case
		ptf.tipoexecucao
		when 0 then 'ALTERNADA'
		when 1 then 'DIAS DA SEMANA'
	end as diasficha,
	ARRAY_TO_STRING(
		ARRAY(
			select
				diasemana
			from
				programatreinoficha_diasemana
			where
				programatreinoficha_codigo = ptf.codigo
		),
		';'
	) as diassemana,
	ptf.tipoexecucao,
	af.metodoexecucao,
	regexp_replace(af.complementonomeatividade, E'[\\n\\r]+', ' ', 'g' ) as complementonomeatividade,
	af.ordem as ordemficha,
	af.descanso as atv_descanso,
	a.nome as nomeatividade,
	case
		a.tipo
		when 0 then 'Neuromuscular'
		when 1 then 'Cardiovascular'
	end as tipoatividade,
	s.carga,
	s.cargaapp,
	regexp_replace(s.complemento, E'[\\n\\r]+', ' ', 'g' ) as complemento,
	s.descanso as serie_descanso,
	s.ordem as serie,
	s.distancia,
	s.duracao,
	s.velocidade,
	s.repeticao,
	g.nome as grupomuscular,
	apar.nome as aparelho
from
	programatreino p
	inner join professorsintetico prof on prof.codigo = p.professormontou_codigo
    inner join empresa e on prof.empresa_codigo = e.codigo
    inner join programatreinoficha ptf on ptf.programa_codigo = p.codigo
	inner join ficha f on f.codigo = ptf.ficha_codigo
	inner join atividadeficha af on af.ficha_codigo = f.codigo
	inner join atividade a on af.atividade_codigo = a.codigo
	left join atividadegrupomuscular agm on agm.atividade_codigo = a.codigo
	left join grupomuscular g on g.codigo = agm.grupomuscular_codigo
	left join atividadeaparelho aa on aa.atividade_codigo = a.codigo
	left join aparelho apar on apar.codigo = aa.aparelho_codigo
	inner join serie s on s.atividadeficha_codigo = af.codigo
where
	predefinido
	
-- order by
-- 	p.datalancamento,
-- 	f.codigo,
-- 	af.ordem
