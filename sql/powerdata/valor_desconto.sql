select
    con.codigo, con.situacaocontrato, pla.codigo as codigoplano, pla.descricao as nomeplano,
    trunc((select sum(mpro.precounitario) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where mpro.contrato  = con.codigo and pro.tipoproduto in ('PM','MA','RE','RN'))::numeric,2) as valorOriginalContrato, 
    trunc(con.valorfinal::numeric,2) as valorfinal, 
    trunc((select sum(mpro.valordesconto) from movproduto mpro where mpro.contrato  = con.codigo)::numeric,2) as valorDesconto 
from contrato con 
    inner join plano pla on pla.codigo = con.plano 