select c.emp<PERSON>a, 
        c.matricula, 
        t.desc<PERSON> as nome<PERSON>a,
        m.nome as nomemo<PERSON><PERSON><PERSON>, 
        al.dia, 
        h.ho<PERSON>,
        h.horafinal,
        ac.codigo as codigoconfirmacao
        
        
from "public"."turma" t
inner join horarioturma h on t.codigo = h.turma
inner join modalidade m on m.codigo = t.modalidade
inner join alunohorarioturma al on al.horarioturma = h.codigo
inner join cliente c on c.codigo = al.cliente
left  join aulaconfirmada ac on ac.cliente = c.codigo and ac.horario = h.codigo and ac.datalancamento = al.dia 

Where aulacoletiva

