select e.cod_empresafinanceiro || '-' || mpar.codigo   as id_parcela,
       mpar.descricao,
       mpar.vendaavulsa,
       mpar.aulaavulsadiaria,
       mpar.valorparcela,
       e.cod_empresafinanceiro || '-' || mpar.contrato as id_contrato,
       mpar.situacao                                   as situacao_parcela,
       mpar.datavencimento,
       mpar.dataregistro,
       mpar.personal,
       mpar.nrtentativas,
       mpar.regimerecorrencia,
       e.cod_empresafinanceiro || '-' || e.codigo      as id_empresa,
       e.nome                                          as empresa,
       e.cod_empresafinanceiro || '-' || mpar.pessoa   as id_pessoa,
       cont.vigenciade                                 as inicio_contrato,
       cont.vigenciaateajustada                        as final_contrato,
       cont.datalancamento                             as lancamento_contrato,
       cont.situacao                                   as situacao_contrato,
       cont.situacaocontrato                           as situacao_contrato_detalhada,
       cont.bolsa,
       mpar.incluidaspc,
       mpar.situacaospc, 
       mpar.jsonspc, 
       CASE
           when now() < cont.vigenciade then 'FUTURO'
           when now() > cont.vigenciaateajustada then 'PASSADO'
           else 'VIGENTE' end                          as tipo_vigencia
from movparcela mpar
         left join empresa e      ON mpar.empresa = e.codigo
         left join contrato cont  ON mpar.contrato = cont.codigo