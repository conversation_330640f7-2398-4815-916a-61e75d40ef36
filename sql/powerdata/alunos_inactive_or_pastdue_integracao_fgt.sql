SELECT
    emp.cod_emp<PERSON><PERSON><PERSON><PERSON><PERSON>,
    s.codigocliente,
    s.codigo<PERSON><PERSON><PERSON>,
    pes.nome,
    s.contrato,
    s.inicio,
    s.fim,
    s.contrato_situacao,
    s.tipo_aluno,
    pes.cfp AS cpf,
    e.email,
    l.datalancamento,
    l.resultado
FROM (SELECT
          cli.codigo AS codigocliente,
          cli.codigomatricula,
          cli.pessoa,
          con.codigo AS contrato,
          con.empresa,
          con.situacao as contrato_situacao,
          con.vigenciade AS inicio,
          con.vigenciaateajustada AS fim,
          'TITULAR' AS tipo_aluno
      FROM contrato con
               INNER JOIN cliente cli ON cli.pessoa = con.pessoa
      UNION
      SELECT
          cli.codigo AS codigocliente,
          cli.codigomatricula,
          cli.pessoa,
          con.codigo AS contrato,
          con.empresa,
          con.situacao as contrato_situacao,
          cond.datainicio AS inicio,
          CAST(cond.datafinalajustada AS date) AS fim,
          'DEPENDENTE' AS tipo_aluno
      FROM contratodependente cond
               INNER JOIN contrato con ON con.codigo = cond.contrato
               INNER JOIN cliente cli ON cli.codigo = cond.cliente) AS s
         INNER JOIN pessoa pes ON pes.codigo = s.pessoa
         LEFT JOIN email e ON e.codigo = (SELECT e2.codigo FROM email e2 WHERE e2.pessoa = s.pessoa ORDER BY e2.codigo DESC LIMIT 1)
    INNER JOIN empresa emp on emp.codigo = s.empresa
    INNER JOIN configuracaointegracaofoguete cfg_fgt ON cfg_fgt.empresa = s.empresa AND cfg_fgt.habilitada is true
    LEFT JOIN contratoplanoprodutosugerido cpps ON cpps.codigo = (SELECT cpps_sub.codigo FROM contratoplanoprodutosugerido cpps_sub INNER JOIN planoprodutosugerido pps ON pps.codigo = cpps_sub.planoprodutosugerido WHERE cpps_sub.contrato = s.contrato and pps.produto = cfg_fgt.produto LIMIT 1)
    LEFT join logintegracoes l on l.codigo= (SELECT l_sub.codigo FROM logintegracoes l_sub
    WHERE l_sub.servico = 'INTEGRACAO_FOGUETE'
    AND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente) ORDER BY codigo DESC LIMIT 1)
WHERE l.dadosrecebidos like '{%}'
AND coalesce((l.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status', '') IN ('INACTIVE', 'PAST_DUE')
--AND s.contrato_situacao = 'CA'
ORDER BY s.contrato
