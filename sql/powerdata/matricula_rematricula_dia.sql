-- https://pacto.atlassian.net/browse/IA-1001
-- Comando base na matricula_rematricula.sql agrupando por dia
WITH 
	matriculas as (
		select distinct
			emp.cod_empresafinanceiro,
		    emp.nome as nomeEmpresa,
			date_trunc('day', contrato.datalancamento) dia,
			cliente.codigo cliente,
			cliente.empresa as codigoempresa,
			categoria.nome AS nome_categoria,
			contrato.situacaoContrato as contrato_situacao
		from questionariocliente
		left join cliente on
			questionariocliente.cliente = cliente.codigo
		left join categoria on
			cliente.categoria = categoria.codigo
		left join contrato on
			contrato.pessoa = cliente.pessoa
			and date_trunc('month', contrato.datalancamento) = date_trunc('month', questionariocliente.data)
		left join pessoa on
			pessoa.codigo = cliente.pessoa
		left join empresa emp on
			emp.codigo = contrato.empresa
		where contrato.origemcontrato in (1, 2)
			and questionariocliente.tipobv in (1, 2, 3)
			and questionariocliente.origemSistema in(1, 17)
	)
select codigoempresa, cod_empresafinanceiro, nomeEmpresa, dia, nome_categoria, contrato_situacao, count(cliente) as quantidade
from matriculas
group by 1, 2, 3, 4, 5, 6
order by dia desc
