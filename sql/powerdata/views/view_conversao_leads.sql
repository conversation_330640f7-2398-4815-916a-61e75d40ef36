-- Pegar apenas não convertidos
SELECT
  cl.*,
  ef.nomefantasia
FROM (
  SELECT
    cliente,
    codigo,
    dados,
    datalancamento,
    dataregistro,
    dia,
    email,
    empresa,
    idlead,
    indicado,
    passivo,
    tipo,
    urlrd,
    uuid,
    _chave,
    "NAO CONVERTIDO" AS status
  FROM
    `dataset_4d3d9bfba7.conversao_lead_meta` clm
  WHERE
    clm.cliente NOT IN (
    SELECT
      cliente
    FROM
      `dataset_4d3d9bfba7.conversao_lead_atingida` )
  UNION ALL
    -- Pegar convertidos
  SELECT
    cliente,
    codigo,
    dados,
    CAST(NULL AS DATETIME) AS datalancamento,
    dataregistro,
    dia,
    email,
    empresa,
    idlead,
    indicado,
    passivo,
    tipo,
    urlrd,
    uuid,
    _chave,
    "CONVERTIDO" AS status,
  FROM
    `dataset_4d3d9bfba7.conversao_lead_atingida`) cl
LEFT JOIN
  `dataset_4d3d9bfba7.rede_empresa_financeiro` ef
ON
  cl._chave = ef.chavezw