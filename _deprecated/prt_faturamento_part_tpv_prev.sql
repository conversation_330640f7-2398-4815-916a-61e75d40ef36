select
--
  case
    when mprod.contrato is not null then p2.descricao 
    when mprod.vendaavulsa is not null then 'VENDA AVULSA'
    else 'OUTROS' end as Produto_Tipo,
--
    e.nome                  as   Unidade,
    e.cod_empresaf<PERSON><PERSON><PERSON> as   Unidade_IdPacto,
    e.co<PERSON><PERSON><PERSON>            as   Unidade_IdRede,
    uf.sigla                as   Unidade_uf,
    cid.nome                as   Unidade_cidade,
--
  us.nome as usuario_nome,
  pc.nome as Contrato_Consultor,
--
  CASE
      WHEN con.situacaocontrato = 'RN' THEN 'Renovação'
      WHEN con.situacaocontrato = 'MA' THEN 'Matricula'
      WHEN con.situacaocontrato = 'RE' THEN 'Rematricula'
      WHEN con.situacaocontrato is null THEN '_Outros'
      else con.situacaocontrato
  END as Contrato_Tipo,
--
  con.situacao          as Contrato_Situacao,
   CASE
        WHEN con.origemsistema = 1 THEN 'Academia'
        WHEN con.origemsistema = 2 THEN 'Agenda Web'
        WHEN con.origemsistema = 3 THEN 'Pacto Treino'
        WHEN con.origemsistema = 4 THEN 'App Treino'
        WHEN con.origemsistema = 5 THEN 'App Professor'
        WHEN con.origemsistema = 6 THEN 'Autoatendimento'
        WHEN con.origemsistema = 7 THEN 'Site Vendas'
        WHEN con.origemsistema = 8 THEN 'Buzz Lead'
        WHEN con.origemsistema = 9 THEN 'Vendas online'
        WHEN con.origemsistema = 10 THEN 'App do consultor'
        WHEN con.origemsistema = 11 THEN 'Booking Gympass'
        WHEN con.origemsistema = 12 THEN 'Fila de espera'
        WHEN con.origemsistema = 13 THEN 'Importação API'
        ELSE 'NÃO DEFINIDO'
    END AS Contrato_Origem,
    con.vigenciade as contrato_validode,
    con.vigenciaateajustada  as contrato_validoate,
    con.valorfinal           as contrato_valorfinal,  -- para descobrir os contratos de valor zerado...
    con.bolsa                as contrato_bolsa,
--
    case
	    when ( select max(1) from cliente cl where cl.pessoa = mprod.pessoa ) = 1 then 'CLIENTE'
	    when ( select max(1) from colaborador cl where cl.pessoa = mprod.pessoa) = 1 then 'COLABORADOR'
	    else 'CONSUMIDOR' 
    end as tipo_pessoa,
--
    case when (mprod.pessoa is not null) then pescliente.nome 
         when (mp.pessoa is null and va.codigo is not null) then va.nomecomprador
         else pescliente.nome
    end  as cliente_Nome,
--   
    pescliente.cfp   as cliente_cpf,
    pescliente.sexo  as cliente_Sexo,
    pescliente.datacadastro  as cliente_DataCadastro,
-- 
--
--
             mprod.pessoa as pessoa,
             mprod.contrato as contrato,
             mprod.vendaavulsa as venda_avulsa,
             mprod.valorfaturado::numeric                      as valor,
             mprod.datalancamento                              as dt_faturamento,
             to_timestamp(mprod.mesreferencia, 'MM/YYYY') as dt_competencia,
--             null as dt_recebimento,
--             null as dt_receita,
--             null as dt_receita_prev,
--             null as dt_receita_original,
--             null::timestamp as dt_vencimento,
--
             case
                 when fp.codigo is not null then	fp.tipoformapagamento
                 when bp.codigo is not null then 'BB'
                 when px.codigo is not null then 'PX'
                 when acc.codigo is not null then 'CA'
                 when abc.codigo is not null then 'BB'
                 when fpu.codigo is not null then fpu.tipoformapagamento
                 else null end AS tipo_forma_pagamento,
--
             case
                 when fp.codigo is not null then	fp.descricao
                 when bp.codigo is not null then 'BOLETO BANCÁRIO'
                 when px.codigo is not null then 'PIX'
                 when acc.codigo is not null then 'CARTÃO RECORRENTE'
                 when abc.codigo is not null then 'BOLETO BANCÁRIO'
                 when fpu.codigo is not null then fpu.descricao
                 else 'SEM COBRANÇA CADASTRADA' end AS forma_pagamento_descricao,
--
             case
                 when mp.conveniocobranca is not null then mp.conveniocobranca
                 when ccb.codigo is not null then ccb.codigo
                 when px.codigo is not null then px.conveniocobranca
                 when acc.codigo is not null then acc.codigo
                 when abc.codigo is not null then abc.codigo
                 else null end AS convenio,
--
             mprod.situacao as parcela_status,
             mprod.responsavellancamento as usuario,
             mprod.descricao  as descricao,
             case when (mp.pessoa is null and va.codigo is not null) then va.nomecomprador else '' end as consumidor
--             null as  nota_numero,
--             null::timestamp as nota_dataemissao
         from movproduto mprod
                  inner join empresa e on mprod.empresa = e.codigo
                  left join estado uf on uf.codigo = e.estado
                  left join cidade cid on cid.codigo = e.cidade
                  inner join produto prod on mprod.produto = prod.codigo
                  left join vendaavulsa va on mprod.vendaavulsa = va.codigo
                  left join movpagamento mp on mp.codigo = (select max(movpagamento) from pagamentomovparcela pmp
                                                                                              inner join movprodutoparcela mpp on pmp.movparcela = mpp.movparcela
                                                            where mpp.movproduto = mprod.codigo)
                  left join formapagamento fp on fp.codigo = mp.formapagamento
                  left join cliente cli ON cli.pessoa = mprod.pessoa
                  left join pix px on px.codigo = (select max(pm.pix) from pixmovparcela pm
                                                                               inner join movprodutoparcela mpp on mpp.movparcela = pm.movparcela
                                                   where mpp.movproduto = mprod.codigo)
                  left join boleto bp on bp.codigo = (select max(bm.boleto) from boletomovparcela bm
                                                                                     inner join boleto bol on bol.codigo = bm.boleto
                                                                                     inner join movprodutoparcela mpp on mpp.movparcela = bm.movparcela
                                                      where bol.situacao in (3,4,5) and mpp.movproduto = mprod.codigo)
                  left join conveniocobranca ccb on ccb.codigo = bp.conveniocobranca
                  left join autorizacaocobrancacliente auc on auc.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 1)
                  left join conveniocobranca acc on acc.codigo = auc.conveniocobranca
                  left join autorizacaocobrancacliente aub on aub.codigo = (select max(au.codigo) from autorizacaocobrancacliente au where au.ativa and au.cliente = cli.codigo and au.tipoautorizacao = 3)
                  left join conveniocobranca abc on abc.codigo = aub.conveniocobranca
                  left join movpagamento mpu on mpu.codigo = (select max(mp1.codigo) from movpagamento mp1 where mp1.pessoa = mprod.pessoa)
                  left join formapagamento fpu on fpu.codigo = mpu.formapagamento
              --
                  --  Para pegar dados do contrato e do Produto do plano -------------------------------------------------------------
    left join contrato con on con.codigo = mprod.contrato
    left join plano p on p.codigo  = con.plano
    left join produto p2 on p2.codigo = p.produtopadraogerarparcelascontrato
    left join usuario us on us.codigo = mprod.responsavellancamento
    left join colaborador c2  on c2.codigo = con.consultor
    left join pessoa pc on pc.codigo = c2.pessoa
--  Para pegar dados pessoa que fez a compra...
    left join pessoa pesCliente on mprod.pessoa = pescliente.codigo
 --   
 --   
 WHERE prod.tipoproduto not in ('DE', 'DR', 'MC', 'CC','RD','DV','DC')
  and mprod.datalancamento >= '2021-01-01 00:00:00'