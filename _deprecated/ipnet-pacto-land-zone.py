# -*- coding: utf-8 -*-

import pandas as pd
import requests
from pandas.io import gbq
from google.oauth2 import service_account
from requests import Request,Session
from http.client import HTTPConnection # py3
from extras.util_deprecated import *
from google.cloud import pubsub_v1

#Declarar variáveis importantes
global LISTA_ZONA

import traceback
import urllib3
import json
import os

#HTTPConnection.debuglevel = 1

credentials = service_account.Credentials.from_service_account_info(
{
  "type": "service_account",
  "project_id": "pacto-datalake-dev",
  "private_key_id": "4c683d60ff607745a7d694c06b7fff7715d153e7",
  "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
  "client_email": "<EMAIL>",
  "client_id": "109488522443674963556",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/pacto-zw-upload-storage%40pacto-datalake-dev.iam.gserviceaccount.com"
}
)
urllib3.disable_warnings()

def notificar_pub_sub():
  project_id = "pacto-datalake-dev"
  topic = "CloudFunctionTriggerAirflow"  

  publisher = pubsub_v1.PublisherClient(credentials=credentials)
  # The `topic_path` method creates a fully qualified identifier
  # in the form `projects/{project_id}/topics/{topic_id}`
  topic_path = publisher.topic_path(project_id, topic)

  # The message must be a bytestring.
  message1 = b"Lets GO IPNET"

  _message2 = "Good job!!!"
  message2 = _message2.encode("utf-8")

  # When you publish a message, the client returns a future.
  # future1 = publisher.publish(topic_path, message1)
  # print(future1.result())  

  future2 = publisher.publish(topic_path, message2)
  print(future2.result())    

def getdata(url):    
    http = urllib3.PoolManager()

    response = http.request('POST', url)
    try:
        data = response.data
    except:
        print("Falha ao obter dados remotos (%s)" % traceback.format_exc())
    return data


def initEmpresas():    

    URL_OAMD="https://app.pactosolucoes.com.br/oamd/prest/infra/qN0zL3zW9lC8lT0eZ1jK6pB8lL3eN0mF"

    JSON_ZONAS=json.loads(getdata(URL_OAMD))
    print(JSON_ZONAS)
    LISTA=JSON_ZONAS['return']
    print(LISTA)
    
    list_of_name = [obj['descricao'] for obj in LISTA]
    list_of_name.sort()
    print(list_of_name)
    return list_of_name

def getDataInZone(x, query):
    
    print("Executando comandos em ZW-" + x)  
    HTTPS_PORT="443"
    PORTA_PG="5432"
      
    if x not in ["20", "21", "22", "30", "31", "32", "40", "41", "42", "50", "51", "52", "80", "81", "82", "90", "91", "92"]:       
      PORTA_PG="5432"     
    else:     
      PORTA_PG="54" + x 
      HTTPS_PORT="90" + x
      
    if x in ["100"]:
      PORTA_PG="5410"
      HTTPS_PORT="9010"
      
    if x in ["101","102"]:
      PORTA_PG="54" + x.replace("0","")
      HTTPS_PORT="90" + x.replace("0","")
    
          
    #Baixar o arquivo gerado pelo ZW            
    params = [
      ("op", "selectParquet"), 
      ("prefixoBanco", "bdzillyon"),
      ("hostPG", "localhost"),
      ("portaPG", PORTA_PG),        
      ("userPG", "zillyonweb"),
      ("pwdPG", "pactodb"),      
      ("sql", query),
      ("lgn", os.environ.get('LGN_KEY')),
      ("except", "bdzillyonjustfitteste|bdzillyonjustfitproducao|bdzillyonpactojuliano|bdzillyonpactosp|bdzillyonpactoavaliacao|bdzillyonpactogqszw91|bdzillyonpactogqsvictordiniz|bdzillyoncomercialpacto"),
    ]    
    
    url="https://zw{0}.pactosolucoes.com.br:{1}/app/UpdateServlet".format(x, HTTPS_PORT)
    print (url)
    response = requests.post(url, data = params)
    
    data = response.content    

LISTA_ZONAS=initEmpresas()

print(LISTA_ZONAS)

for x in LISTA_ZONAS:  
  zona = x.replace('ZW-', '')
  print("Vou Acionar a zona " + zona)
  
  ################## DADOS CLIENTES ##################
  QUERY="select e.nome as nomeacadmeia, e.cod_empresafinanceiro, s.* from situacaoclientesinteticodw s inner join empresa e on e.codigo = s.empresacliente order by s.codigo"  
  getDataInZone(zona, QUERY)
  
  ################## DADOS TPV ##################
  QUERY=getsql('tpv')
  getDataInZone(zona, QUERY)
  
#notificar_pub_sub()
  