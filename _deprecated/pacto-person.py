# -*- coding: utf-8 -*-
from extras.Util import *


PROJECT_ID = "dados-767c9fb55d"
DATASET_ID = "person_0001"
# CHAVES_ESPECIFICAS = [
#     "60af85257bcbbea7569312fe6ab602ed",
#     "3ea0060c92d5b348e29a3c571dccc284",
#     "7238dcfb244fc931d63bd1aee764767a",
# ]
CHAVES_ESPECIFICAS = [
    "1a97fec7cf09b807c2547a62a5bd0194",
    "608e98cfc981506fb94565509c827f21",
    "ac649a8c04977062acc341016ce44af9",
]
#  ROTA DO DINHEIRO , ACADEMIA PACTO PRODUÇÃO, BRAVA , ESSENCE GYM, SAÚDE E CIA, HOPE ANÁPOLIS
# 83cf8a192ebe8e66a0b71e6614843c7c,aca438e8c9e947e64db2236bb2f1f7a9,495afa1552fb8735271a281669f11937

credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        f"project_id": "{PROJECT_ID}",
        "private_key_id": "d8429e9c4b0ce43cb97242044836bfbeb6a8d948",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace(
            "\\n", "\n"
        ),
        "client_email": "<EMAIL>",
        "client_id": "108542341588899437207",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-dados-767c9fb55d%40dados-767c9fb55d.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com",
    }
)


for chave in CHAVES_ESPECIFICAS:
    util = Util(
        project_id=PROJECT_ID,
        dataset_id=DATASET_ID,
        chave_rede=chave,
        credentials=credentials,
    )
    util.verificar_dataset(DATASET_ID)

    ################################### DADOS OAMD ####################################

    ############## DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##############
    # TABLE_ID='oamd_financeiro_forma_pagamento'
    # QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
    # util.generate_data_oamd(TABLE_ID, QUERY)

    ################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
    # TABLE_ID = "rede_empresa_financeiro"
    # QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
    # util.generate_data_oamd(
    #     TABLE_ID,
    #     QUERY,
    #     lst_date_times=["finalimplantacao", "inicioimplantacao", "inicioproducao"],
    # )

    ################################### DADOS ZONAS ###################################
    try:
        util.generate_data_all_zones("dadosgerenciais")
    except:
        util.print_log("\n\terro ao gerar dadosgerenciais", "red")
    try:
        util.generate_data_all_zones("tpv-previsao")
    except:
        util.print_log("\n\terro ao gerar tpv-previsao", "red")
    try:
        util.generate_data_all_zones("person")
    except:
        util.print_log("\n\terro ao gerar person", "red")
    try:
        util.generate_data_all_zones("acessos", parallel=True)
    except:
        util.print_log("\n\terro ao gerar acessos", "red")
    # util.generate_data_all_zones('plano')
    # util.generate_data_all_zones('colab')
    # util.generate_data_all_zones('marketing')
    # util.generate_data_all_zones('pagamentosconjuntos')
    # util.generate_data_all_zones('estorno_cancelamento')
    # util.generate_data_all_zones('finan_lancamentos')
    # util.generate_data_all_zones('crm', parallel=True)
    # util.generate_data_all_zones('agregacao_geral', parallel=True)
    # util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')

    # util.create_view()

    tables = ["dadosgerenciais", "previsao", "person", "acessos"]

    CLIENT = bigquery.Client(project=PROJECT_ID, credentials=credentials)
    dataset_ref = CLIENT.dataset(DATASET_ID)

    try:
        for table in tables:
            rename = f"""            
                ALTER TABLE `{PROJECT_ID}.{DATASET_ID}.{table}`
                RENAME TO `{table}_{chave}`;
            """
            job = CLIENT.query(rename)
            job.result()
            util.print_log(f"{table} renomeado com sucesso para: {table}_{chave}", "green")

    except:
        util.print_log("\n\terro nas renomeações", "red")

# try:
#     queries = ["dadosgerenciais", "tpv-previsao", "person", "acessos"]
#     for query in queries:
#         tables_to_union = [f"{query}_{chave}" for chave in CHAVES_ESPECIFICAS]
#         table_ids = [f"{DATASET_ID}.{table}" for table in tables_to_union]

#         union_queries = [f"SELECT * FROM {table_id}" for table_id in table_ids]
#         create_table_query = f"""
#         CREATE TABLE {DATASET_ID}.{query} AS
#         {' UNION ALL '.join(union_queries)};
#         """

#         # create_table_query = f"""
#         #     CREATE TABLE {DATASET_ID}.{query} AS
#         #     (SELECT * FROM {table_ids[0]}
#         #     UNION ALL
#         #     SELECT * FROM {table_ids[1]}
#         #     UNION ALL
#         #     SELECT * FROM {table_ids[2]});
#         # """

#         CLIENT = bigquery.Client(project=PROJECT_ID, credentials=credentials)
#         CLIENT.query(create_table_query).result()

#         for table_id in table_ids:
#             try:
#                 CLIENT.delete_table(table_id)
#             except NotFound:
#                 util.print_log(f"\n\tA tabela {table_id} não foi encontrada.", "red")

#         util.print_log("\n\tProcesso de união concluído.", "green")
# except:
#     util.print_log("\n\terro nas renomeações", "red")
