# -*- coding: utf-8 -*-
"""Max OAMD_FINAN_Todos os dados  SQL Server To BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1eHM0qjH81GPTehyw4F1YY0Lbu7f8oniE
"""
import sys
sys.path.insert(1, '/usr/src/app')

from google.cloud.exceptions import NotFound
from google.oauth2 import service_account
from google.cloud import bigquery
from termcolor import colored
import pandas as pd
import pandas_gbq
import datetime
import requests
import json
import os
import pytz


LGN=os.environ.get('LGN_KEY')
PROJECT_ID = "oamd-e-financeiro-pacto"
DATASET_ID = "oamd_financeiro"
CREDENTIALS = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "oamd-e-financeiro-pacto",
        "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
        "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
        "client_email": "<EMAIL>",
        "client_id": "117321030275837789997",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com",
    }
)


# Extrair dados pelo IFINAN
endpoint_ifinan_generate = f"https://fin.pactosolucoes.com.br/ifinan/updateServlet?op=encriptar&lgn={LGN}"
endpoint_ifinan_query = f"https://fin.pactosolucoes.com.br/ifinan/UpdateServlet?op=selectONE&lgn={LGN}"

tables = [
    {
        "database": "viewsintegracao",
        "table_name": "BQ_FINAN_Favorecido_PACTO_WAGI",
        "pk": "_pseudo_cod",
        "create_temp_table": True,
        "integer": ["VL_RECORRENCIA","ID_FAVORECIDO","Ano_Cad","Mes_Cad","Dia_Cad","Ano_Desat","Mes_Desat","Dia_Desat",
                    "CHAVE_EMPCOD","CHAVE_DIACONTAR","MESESCOMISSAO","ID_GRUPOFAVORECIDO","QtdeNaDesativacao","LTV_Meses",
                    "Ano_IniContrato","Mes_IniContrato","QtdeItens", 
                    "AlunosAtivos","AlunosVencidos",
                     "_pseudo_cod", "seq"],
        "float": ["VL_RECORRENCIA","MRR","PgNaDesativacao","TOTAL_PG","LTV_Valor_Soma","LTV_Valor_Medio"],
        "drop_columns": [],
        "string_to_timestamp": ["DATACADASTRO", "DATAINICIOCONTRATO", "DATAATUALIZACAO", "DATADESATIVACAO","AlunosDtHrSinc"]
    },
    {
        "database": "viewsintegracao",
        "table_name": "BQ_FINAN_Lancamentos_PACTO_com_WAGI",
        "pk": "_pseudo_cod",
        "create_temp_table": True,
        "integer": ["ID_FAVORECIDO", "ID_PARCELA", "Mes_Lanc", "Dia_Lanc", "seq", "AGD_PERIODICIDADE", "Ano_Lanc", "AGD_PERIODICO", 
                    "TRIMESTRE","nr_parcela", "id_SubClassificacao", "id_SubCategoria",
                    "AlunosAtivos","AlunosVencidos",
                    "_pseudo_cod" ],
        "float": ["ValorItem", "VALOR_DESCONTO"],
        "drop_columns": [],
        "string_to_timestamp": [
            "dthr_vencimento", "DataAtualizacaoCadastro", "dthr_quitacao", "Data_Expiracao", "dthr_lancamento",
            "DATACADASTRO", "DATAPROCESSAMENTONFSE", "DATACOMPETENCIA", "DATADESATIVACAO"
        ]
    },
    {
        "database": "viewsintegracao",
        "table_name": "BQ_FINAN_Favorecido_Produtos_PACTO_WAGI",
        "pk": "_pseudo_cod",
        "create_temp_table": True,
        "integer": ["ID_FAVORECIDO", "PRD_ID", "PRD_QTDE", "PRD_COMPRA_ANO", "PRD_COMPRA_MES", "PRD_COMPRA_DIA", "PRD_GARANTIA_ANO", 
                    "PRD_GARANTIA_MES", "PRD_GARANTIA_DIA", "codcategoria", "PRD_INICONTRATO_ANO", "PRD_INICONTRATO_MES", 
                    "PRD_INICONTRATO_DIA", "PRD_COD_AGENDAMENTO", "PRD_COD_FAIXA", "PRD_BLOQUEIO_ANO", "PRD_BLOQUEIO_MES", 
                    "PRD_BLOQUEIO_DIA", "PRD_CHURN_ANO", "PRD_CHURN_MES", "PRD_CHURN_DIA", "PRD_REAJUSTE_ANO", "PRD_REAJUSTE_MES", 
                    "PRD_REAJUSTE_DIA", "PRD_ISENCAO_ANO", "PRD_ISENCAO_MES"],
        "float": ["PRD_VL_MENSAL_CHURN"],
        "drop_columns": [],
        "string_to_timestamp": ["PRD_DTHRCOMPRA","PRD_DTHRGARANTIA","PRD_INICONTATO_DATA","PRD_BLOQUEIO_DATA","PRD_CHURN_DATA"]
    }
    ,
]


table_name = tables[0]["table_name"]
pk = tables[0]["pk"]

table_original = tables[0]["table_name"]
table_temp = f"temp_{tables[0]['table_name']}"

n_records = 5000
off_set = 0

query_template_count = "select count(*) as qtd_rows from {table_name}"
drop_table_if_exists = ("IF OBJECT_ID('dbo.{table_name}', 'U') IS NOT NULL DROP TABLE dbo.{table_name}")
create_temp_table = "select IDENTITY (decimal, 1,1) AS {pk}, {table_original}.* INTO {table_temp} from {table_original}"
select_data_table = "select top {n_records} * from {table_name} where {pk} between {off_set} and {n_records} ORDER BY 1"

# generate request token for query
# execute request for query and get results as Json or Text Plain
def post_query(query, database, returnAsJsonOrPlain=True):
    data = {
        "url": "****************************************/" + database,
        "user": "sa",
        "senha": "pactodb",
        "format": "json",
        "sql": query,
    }
    print(query)

    r = requests.post(endpoint_ifinan_generate, json=data)
    if r.status_code == 200:
        try:
            r_json_data = json.loads(r.text)
        except json.JSONDecodeError:
            print("JSONDecodeError: ", r.text)
    else:
        print("Error: ", r.status_code)

    endpoint_consultar = (endpoint_ifinan_query + "&params=" + r_json_data["sucesso"] + "&format=json")
    r2 = requests.post(endpoint_consultar)

    if returnAsJsonOrPlain:
        return json.loads(r2.text)
    else:
        return r2.text

# generate temp tables indexed for better performance
def prepare_temp_data(tbl):
    is_create_temp = True if tbl["create_temp_table"] else False
    if is_create_temp:
        temp_name = tbl["table_name"] + "_temp"
        original_name = tbl["table_name"]

        # dropping temp table if exists
        print(
            post_query(
                drop_table_if_exists.format(table_name=temp_name),
                tbl["database"],
                False,
            )
        )

        # create temp table e insert data from original table
        print(
            post_query(
                create_temp_table.format(
                    table_original=original_name, pk=tbl["pk"], table_temp=temp_name
                ),
                tbl["database"],
                False,
            )
        )

# get raw data from queries as Json paging results
def get_data(tbl):
    database = tbl["database"]
    table_name = tbl["table_name"]
    pk = tbl["pk"]

    if tbl["create_temp_table"]:
        table_name = tbl["table_name"] + "_temp"

    querycount = query_template_count.format(table_name=table_name)
    json_count = post_query(querycount, database)
    df = normalize_json_to_df(json_count)

    rows = df["qtd_rows"].values[0]
    # rows = 1000
    print("Total rows: " + str(rows))

    off_set = 0
    resto = rows % n_records
    n_requests = (int)(rows / n_records)
    if resto > 0:
        n_requests += 1

    result_json = []

    for i in range(0, n_requests * n_records, n_records):
        query_paginada = select_data_table.format(
            table_name=table_name, pk=pk, off_set=i, n_records=n_records + i
        )
        j = post_query(query_paginada, database)
        del j["nomeBanco"]
        result_json.append(j)

    df = normalize_json_to_df(result_json)
    return df

# covert json results to dataframe
# drop any columns
def normalize_json_to_df(json):
    df = pd.DataFrame.from_dict(json)
    df = pd.json_normalize(json, "result")
    return df


def register_execution(table_id):
        client = bigquery.Client(credentials=CREDENTIALS, project=PROJECT_ID)
        table_ref = client.dataset(DATASET_ID).table('datas_execucoes')

        try:
            client.get_table(table_ref)

        except NotFound:
            print(f"\nA tabela {table_ref.path} não existe. Criando tabela...")

            schema = [
                bigquery.SchemaField("tabela", "STRING"),
                bigquery.SchemaField("data_ultima_execucao", "TIMESTAMP"),
                bigquery.SchemaField("data_ultima_execucao_up", "STRING", mode="REQUIRED"),
            ]

            table = bigquery.Table(table_ref, schema=schema)
            client.create_table(table)
            print(f"Tabela {table_ref.path} criada com sucesso!\n")
            
        df = pandas_gbq.read_gbq(f"select * from {DATASET_ID}.datas_execucoes", project_id=PROJECT_ID)
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.loc[df['tabela'] == table_id, 'data_ultima_execucao_up'] = datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")

        if not df['tabela'].isin([table_id]).any():
            new_row = {'tabela': table_id, 'data_ultima_execucao': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'data_ultima_execucao_up': datetime.datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S.%f%z")} 
            
            if df.empty:
                df = pd.DataFrame(new_row, index=[0])                
            else:                               
                df = pd.concat([df, pd.DataFrame(new_row, index=[0])], ignore_index=True)

        df['data_ultima_execucao'] = pd.to_datetime(df['data_ultima_execucao'], utc=False)
        pandas_gbq.to_gbq(dataframe=df, destination_table=f'{DATASET_ID}.datas_execucoes', project_id=PROJECT_ID, if_exists='replace', credentials=CREDENTIALS)             
        print(colored(f"\nData e hora '{table_id}' atualizado com sucesso em datas_execucoes", "green"))


for tbl in tables:
    prepare_temp_data(tbl) # prepare data
    df = get_data(tbl) # get data and transform to dataframe
    df.columns = df.columns.str.replace(" ", "_")

    table_name = tbl["table_name"]
    date_columns = tbl["string_to_timestamp"]
    columns_to_drop = tbl["drop_columns"]
    columns_integer = tbl["integer"]
    columns_float = tbl["float"]

    df = df.astype(str)

    df[date_columns] = df[date_columns].apply(pd.to_datetime, format='%Y-%m-%d %H:%M:%S.%f', errors='coerce')
    df = df.drop(columns_to_drop, axis=1)
    # df[columns_integer] = df[columns_integer].astype(int)
    df[columns_integer] = df[columns_integer].apply(pd.to_numeric, errors='coerce')
    for column in columns_float:
        df[column] = pd.to_numeric(df[column], errors='coerce').astype(float)
    df.replace('\0', '', regex=True, inplace=True)

    df.to_gbq(destination_table=DATASET_ID+"."+table_name, project_id=PROJECT_ID, if_exists="replace", credentials=CREDENTIALS)
    
    register_execution(table_name)
