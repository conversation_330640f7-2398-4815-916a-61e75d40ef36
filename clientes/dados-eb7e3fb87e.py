# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-eb7e3fb87e" #THE SIMPLE GYM
DATASET_ID = "dados_eb7e3fb87e"
CHAVES_ESPECIFICAS = "eb7e3fb87e21a435a9923048ca5e1fdf,"
SQL_PATH = "powerdata"



credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "1c00575ff23ba054cb410bda87189e62e832dc33",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"), 
        "client_email": "<EMAIL>",
        "client_id": "102305715455330306883",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-dados-eb7e3fb87e%40dados-eb7e3fb87e.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
    },
)

location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('rede_empresa_financeiro_semrede').format(util.chaves_escaped)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# # ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('plano', parallel=True)
##util.generate_data_all_zones('faturamento', parallel=True)
util.generate_data_all_zones('dadosgerenciais', parallel=True)
util.generate_data_all_zones('churn', parallel=True)
util.generate_data_all_zones('ltv', parallel=True)
util.generate_data_all_zones('inadimplencia', parallel=True)
util.generate_data_all_zones('alunos_inadimplentes', parallel=True)
util.generate_data_all_zones('faturamento_consolidado', parallel=True)
util.generate_data_all_zones('acessos_gympass', parallel=True)
util.generate_data_all_zones('recebiveis', parallel=True)
util.generate_data_all_zones('contas_a_pagar', parallel=True)
util.generate_data_all_zones('mov_contrato_saida', parallel=True)
##util.create_view()

############################ FINAL ##############################################