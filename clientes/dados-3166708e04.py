# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-3166708e04" #REDE CORPORE
DATASET_ID = "dados_3166708e04"
CHAVE_ID = "3166708e04497353fce39b031030b622"
SQL_PATH = "powerdata"

credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "5ea76b0a7ad612b634c8a33b3000798e51f93b75",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "110500113470609806240",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-3166708e04%40dados-3166708e04.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com",
    },
)

location='southamerica-east1'
util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


########################################DADOS OAMD#################################################

#################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['dataexpiracao', 'dataexpiracaocreditodcc', 'periodofim','periodoinicio','dataultimoacesso',
#                                                         'renegociadoate','dataultimoacesso','data_ultimolancamento_contrato','dataultimologin',
#                                                         'dataconsulta','primeiranfseemitida','datacadastro','datadesativacao'])
#
#
################ DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID = "rede_empresa_financeiro"
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(
    TABLE_ID,
    QUERY,
    lst_date_times=["finalimplantacao", "inicioimplantacao", "inicioproducao"],
)


# # ####################################### DADOS ZONAS #################################################

util.generate_data_all_zones("colab", parallel=True)
util.generate_data_all_zones("plano", parallel=True)
util.generate_data_all_zones("person")
util.generate_data_all_zones("dadosgerenciais", parallel=True)

# # util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc', parallel=True)
# # util.generate_data_all_zones('treinos_executados', prefix='bdmusc', parallel=True)
# # util.generate_data_all_zones('treinos_alunos', prefix='bdmusc', parallel=True)
# # util.generate_data_all_zones('crm', parallel=True)

util.generate_data_all_zones("marketing", parallel=True)
util.generate_data_all_zones("tpv-previsao", parallel=True)
util.generate_data_all_zones("pagamentosconjuntos", parallel=True)
util.generate_data_all_zones("estorno_cancelamento", parallel=True)
util.generate_data_all_zones("finan_lancamentos", parallel=True)
util.generate_data_all_zones("acessos", parallel=True)

util.create_view()
