# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-341b908afd' # REDE PRATIQUE
DATASET_ID='dados_341b908afd'
CHAVE_ID='341b908afd7637c1d5b09f248d3498f1'
SQL_PATH='PRT'

credentials = service_account.Credentials.from_service_account_info(
                      {
                        "type": "service_account",
                        "project_id": "{PROJECT_ID}",
                        "private_key_id": "7b0b6afb8f980836df6d4f295fa4d1e2bffccbdd",
                        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110081520603865354074",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-341b908afd%40dados-341b908afd.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, base_path=SQL_PATH, location='southamerica-east1')
util.verificar_dataset(DATASET_ID)


if os.environ.get('QR'):
  util.run_queries()
else:    
  ########################################DADOS OAMD#################################################

  #################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
  #TABLE_ID='oamd_financeiro_forma_pagamento'
  #QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
  #util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['dataexpiracao', 'dataexpiracaocreditodcc', 'periodofim','periodoinicio','dataultimoacesso',
  #                                                         'renegociadoate','dataultimoacesso','data_ultimolancamento_contrato','dataultimologin',
  #                                                         'dataconsulta','primeiranfseemitida','datacadastro','datadesativacao'])
  #
  #
  ################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
  TABLE_ID='rede_empresa_financeiro'
  QUERY=util.getsql('rede_empresa_financeiro').format(util.chave_rede)
  util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

  ################# DADOS OAMD campanha_cupom_desconto ##################
  #TABLE_ID='campanha_cupom_desconto'
  #QUERY=util.getsql('../341b908afd/prt_campanha_cupom_desconto').format(util.chave_rede)
  #util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['campanha_vigenciainicial', 'campanha_vigenciafinal', 'cupom_dt_geracao', 'cupom_dt_utilizacao'])

  #######################################DADOS ZONAS#################################################


  ###
  ###util.generate_data_all_zones('marketing', parallel=True)
  ###util.generate_data_all_zones('dadosgerenciais', parallel=True)
  ####util.generate_data_all_zones('colab', parallel=True)
  ###util.generate_data_all_zones("acessos", parallel=True)
  
  ###util.generate_data_all_zones('person', parallel=True)
  util.generate_data_all_zones('prt_alunos', parallel=True)
  util.generate_data_all_zones('prt_aluno_dia', parallel=True)
  util.generate_data_all_zones('prt_alunos_acessos', parallel=True)
  util.generate_data_all_zones('prt_recebimento', parallel=True)
  util.generate_data_all_zones('prt_previsao', parallel=True)
  util.generate_data_all_zones('prt_faturamento', parallel=True)
  util.generate_data_all_zones('prt_venda_produtos', parallel=True)
  util.generate_data_all_zones('prt_estoque', parallel=True)
  util.generate_data_all_zones('prt_caixa', parallel=True)
  util.generate_data_all_zones('prt_venda_nao_finalizadas', parallel=True)
  util.generate_data_all_zones('prt_gympass', parallel=True)
  util.generate_data_all_zones('prt_gympass_hoje', parallel=True)
  util.generate_data_all_zones('prt_ocupacao_aula_professor', parallel=True)
  util.generate_data_all_zones('pix', parallel=True)
  util.generate_data_all_zones('prt_parcelas_cancel_reneg', parallel=True)
  util.generate_data_all_zones('prt_parcelas_em_aberto', parallel=True)
  util.generate_data_all_zones('plano_info', parallel=True)
  util.generate_data_all_zones('prt_conveniorateio', parallel=True)
  util.generate_data_all_zones('prt_convenio_pix', parallel=True)

  util.create_view()

