# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='pacto-datalake'
DATASET_ID = "perso_0001"
CHAVES_ESPECIFICAS = "698d5206f775c3bd67eb0349f66d2dab,c42bd5ee497c50012dd346bd7b765724,c89c45cc3aaa799d524f77afa8f78a62,717a9e3897cda9801f62901fc9323575,2366443b37686f5c012dcca87be2243c,bbf14ad27fc5e24321cf506b1b8775ce,9c869774a7df7fc715508cbffaf9e3df,53b820fbc6f59367dc29bbe15272f9aa,495afa1552fb8735271a281669f11937,dc16c8b86b075c9b55010696970d8a18,bd3778e66a1146cdecccd653631db063,6b774caa64735c129f2b912a12d33139,8dfda3b0eaa291a1d77cf4e568fbc429,bfbdf95a26a045ab87509dd0ca401532,efc2fc0fa6d5926fc12f60fc396a52c3,ff064cc6ac0cf57ec1891a41bfae0c47,4f423887cc96c655711a0cec648865dd,efc45e45f49c9af0c9b7c7c7315b3935,905cd02bbd3ff1b6213639abff62c793,f3c1bf148372dfa7013a931b0bbbedf1,de10f9af4adce8c142e7fd3edc69257e,80108eb5b974518ae14f61b30a4bbef6,f612e694dd0b4cda075802b3b50c138b,24f744b801fa50d0c019de28f4db63f0,63e406e78b40993f4fa3383405c7c963,35defced7bab8eace28114bfdb3cce4a,a277843ba7c1f602189ce2583772de80,11827fc3e7cadf9991ce58827c2a09de,25113214e74a5cc29286336ab47e5442,a175db64085ec5c3aa7446446ec6adf3,a4f3793c4a71839c5ac3e5793048ef12,60af85257bcbbea7569312fe6ab602ed,def384f146e5dafa45110a0d12c3df23,95f31d847d04ae6b792b1f4394789315,f9587903d24b01c7c30f97a0191946bf,2d68f169f97f3d6af2aa273d73fa0b5f,f824176d8863bc860f9670202ebf4e4b,1e7dcd4f25e4e4ec3b3f3c34328cab2a,622b243ee1a2bcfd464986cd5a513f84,fc4df98b93791c01e9e8cb5b517c65e8,d4beeda758f02429a5846b139b4e50ca,14d8427dc0582233b6db7f47ebcfd4c,708a8c26577dfe3eae1709937fb5cba3,8b2c684a055d183f0ffb4eb29936c6e1,1f7799048bf440282acaba50f2bf9917,84fcf98dd001bf5b86619d221200386,7d35edf457638200c5a3b2da2de3afa6,50a3c8e373324da3eb2fa78e745388,c8ec5ccb46507bd4a82ec681ccb6ca4,f6346c926a547ad524cdccd5bfcceba0,18584ace17a873dae5f9d434c3c5fb41"

CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)

#  ESSENCE GYM, SAUDE E CIA, HOPE ANAPOIS

# ROTA DO DINHEIRO , ACADEMIA PACTO PRODUÇÃO, BRAVA ,
# 83cf8a192ebe8e66a0b71e6614843c7c,aca438e8c9e947e64db2236bb2f1f7a9,495afa1552fb8735271a281669f11937

credentials = service_account.Credentials.from_service_account_info(
    *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
)


util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path = 'clientes')
util.verificar_dataset(DATASET_ID)
      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
# TABLE_ID='rede_empresa_financeiro'
# QUERY=util.getsql('rede_empresa_financeiro_perso').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

########################################DADOS ZONAS#################################################

util.generate_data_all_zones('dadosgerenciais')
util.generate_data_all_zones('tpv-previsao')
# util.generate_data_all_zones('plano')
util.generate_data_all_zones('person')
# util.generate_data_all_zones('crm', parallel=True)
util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)


# util.generate_data_all_zones('colab')
# util.generate_data_all_zones('marketing')
# util.generate_data_all_zones('pagamentosconjuntos')
# util.generate_data_all_zones('estorno_cancelamento')
# util.generate_data_all_zones('finan_lancamentos')
# util.generate_data_all_zones('agregacao_geral', parallel=True)
# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')
# util.create_view()

# churn process...
print("mvm")
from extras.load_chunks import *
main(credentials= credentials, project = PROJECT_ID, dataset_id= DATASET_ID)