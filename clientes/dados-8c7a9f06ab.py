# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-8c7a9f06ab" #FLY NOW
DATASET_ID = "dados_8c7a9f06ab"
CHAVE_REDE = "8c7a9f06abfa5809a3fc84cfe38a111"
SQL_PATH = "powerdata"


credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "48e80b5ab0c36271bfa8cbed3da58acf3308ded3",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "116629839800196772893",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-8c7a9f06ab%40dados-8c7a9f06ab.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com",
    },
)
location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)



######################################## DADOS OAMD #################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('dadosgerenciais', parallel=True)
util.generate_data_all_zones('colab', parallel=True)
util.generate_data_all_zones('plano', parallel=True)
util.generate_data_all_zones('marketing', parallel=True)
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
util.generate_data_all_zones('estorno_cancelamento', parallel=True)
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones("acessos_relatorio_zw", parallel=True)
util.generate_data_all_zones("convidado", parallel=True)
util.generate_data_all_zones("observacao", parallel=True)
util.generate_data_all_zones('qtd_bv', parallel=True)
util.generate_data_all_zones('qtd_bv_dia', parallel=True)
util.generate_data_all_zones('qtd_matricula_rematricula', parallel=True)
util.generate_data_all_zones('matricula_rematricula', parallel=True)
util.generate_data_all_zones('matricula_rematricula_dia', parallel=True)
util.generate_data_all_zones('qtd_visitas_mensal', parallel=True)
util.generate_data_all_zones('mov_contrato_saida_cat', parallel=True)

# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('treinos_executados', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('treinos_alunos', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('crm', parallel=True)
# util.create_view()
