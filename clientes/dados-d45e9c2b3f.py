# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

######################################################################################################
# Iniciar variáveis importantes
PROJECT_ID = "dados-d45e9c2b3f"  # REDE Velox
DATASET_ID = "dados_d45e9c2b3f"
CHAVE_ID = "d45e9c2b3f6460e0b24011bd335a009c"
SQL_PATH = "powerdata"

credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "f1386f414558bd4858ec76eea739ddb53e06ed09",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "106790855730977758396",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-d45e9c2b3f%40dados-d45e9c2b3f.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
    }
)

util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


########################################DADOS OAMD#################################################

#################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['dataexpiracao', 'dataexpiracaocreditodcc', 'periodofim','periodoinicio','dataultimoacesso',
#                                                         'renegociadoate','dataultimoacesso','data_ultimolancamento_contrato','dataultimologin',
#                                                         'dataconsulta','primeiranfseemitida','datacadastro','datadesativacao'])
#
#
################ DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID = "rede_empresa_financeiro"
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(
    TABLE_ID,
    QUERY,
    lst_date_times=["finalimplantacao", "inicioimplantacao", "inicioproducao"],
)

# # ####################################### DADOS ZONAS #################################################

util.generate_data_all_zones("colab", parallel=True)
util.generate_data_all_zones("plano", parallel=True)
util.generate_data_all_zones("person")
util.generate_data_all_zones("dadosgerenciais", parallel=True)
util.generate_data_all_zones("marketing", parallel=True)
util.generate_data_all_zones("tpv-previsao", parallel=False)
util.generate_data_all_zones("pagamentosconjuntos", parallel=True)
util.generate_data_all_zones("estorno_cancelamento", parallel=True)
util.generate_data_all_zones("finan_lancamentos", parallel=True)
# util.generate_data_all_zones("acessos", parallel=True)

util.create_view()
