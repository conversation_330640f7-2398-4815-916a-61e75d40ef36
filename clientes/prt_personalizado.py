# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-341b908afd' # REDE PRATIQUE pra personalizar o tempo que roda a query - 30 em 30 minutos
DATASET_ID='dados_341b908afd'
CHAVE_ID='341b908afd7637c1d5b09f248d3498f1'
SQL_PATH='PRT'

credentials = service_account.Credentials.from_service_account_info(
                      {
                        "type": "service_account",
                        "project_id": "{PROJECT_ID}",
                        "private_key_id": "7b0b6afb8f980836df6d4f295fa4d1e2bffccbdd",
                        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110081520603865354074",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-341b908afd%40dados-341b908afd.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, base_path=SQL_PATH, location='southamerica-east1')
util.verificar_dataset(DATASET_ID)


if os.environ.get('QR'):
  util.run_queries()
else:    
  util.generate_data_all_zones('plano_info')

  util.create_view()

