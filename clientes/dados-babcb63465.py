# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-babcb63465" # FABRICA DE MONSTRO
DATASET_ID = "dados_babcb63465"
CHAVE_REDE = "babcb63465f9e82d0b4495e1c5926530"
SQL_PATH = "powerdata"



credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "8c920e91c7928e87f8e80b2fa93ae153cb532f3f",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"), 
        "client_email": "<EMAIL>",
        "client_id": "102798606639937969732",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-babcb63465%40dados-babcb63465.iam.gserviceaccount.com",
  "universe_domain": "googleapis.com"
    },
)

location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# # ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('dadosgerenciais', parallel=True)

################# Integradores ##################
util.generate_data_all_zones('acessos_gympass', parallel=True)
util.generate_data_all_zones('acessos_totalpass', parallel=True)

util.create_view()
