# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-767c9fb55d'

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': 'd8429e9c4b0ce43cb97242044836bfbeb6a8d948',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "108542341588899437207",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-dados-767c9fb55d%40dados-767c9fb55d.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

util = Util(PROJECT_ID, 'dataset_767c9fb55d', '767c9fb55d088426f2014784391c07f1', credentials=credentials, base_path = 'powerdata')

      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
# TABLE_ID='rede_empresa_financeiro'
# QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

########################################DADOS ZONAS#################################################

# util.generate_data_all_zones('dadosgerenciais')

# util.generate_data_all_zones('tpv-previsao')

# util.generate_data_all_zones('plano')

# util.generate_data_all_zones('person')
# #
# util.generate_data_all_zones('colab')
# #
# util.generate_data_all_zones('marketing')
# #
# util.generate_data_all_zones('pagamentosconjuntos')
# #
# util.generate_data_all_zones('estorno_cancelamento')
# #
# util.generate_data_all_zones('finan_lancamentos')
# util.generate_data_all_zones('crm', parallel=True)
# util.generate_data_all_zones('agregacao_geral', parallel=True)
# util.generate_data_all_zones('todas_empresas', parallel=True)
# util.generate_data_all_zones('acessos', parallel=True)
# util.generate_data_all_zones('ocupacao_aula_professor', parallel=True)
# util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones("convidado", parallel=True)
util.generate_data_all_zones("observacao", parallel=True)

###util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')