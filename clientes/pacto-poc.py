# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='pacto-poc'
DATASET_ID = "pacto_poc"
CHAVES_ESPECIFICAS = "ef104c66579de336c8fc6dff69c5e006,3d9d99c9a2ff71c5ae2944eb8fb345da,1eb222184f95a841bb97d04cb48b288"

credentials = service_account.Credentials.from_service_account_info(
                      {
                        "type": "service_account",
                        "project_id": '{PROJECT_ID}',
                        "private_key_id": "db569d568ff232bb1855379ab59bfbd5c0a51140",
                        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "117186875007089774504",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-pacto-poc%40pacto-poc.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                        }
                      )


util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path = 'powerdata')
util.verificar_dataset(DATASET_ID)
      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
# TABLE_ID='rede_empresa_financeiro'
# QUERY=util.getsql('rede_empresa_financeiro_perso').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

########################################DADOS ZONAS#################################################
util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones('dadosgerenciais')
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('person')

# util.generate_data_all_zones('plano')
# util.generate_data_all_zones('crm', parallel=True)
# util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)
# util.generate_data_all_zones('grupo_de_risco', parallel=True)

# util.generate_data_all_zones('colab')
# util.generate_data_all_zones('marketing')
# util.generate_data_all_zones('pagamentosconjuntos')
# util.generate_data_all_zones('estorno_cancelamento')
# util.generate_data_all_zones('finan_lancamentos')
# util.generate_data_all_zones('agregacao_geral', parallel=True)
# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')
# util.create_view()