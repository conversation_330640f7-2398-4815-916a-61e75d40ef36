# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *

######################################################################################################
# Iniciar variáveis importantes
PROJECT_ID = "dados-495afa1552"  # ACADEMIA BRAVA
DATASET_ID = "dados_495afa1552"
CHAVE_ID = "495afa1552fb8735271a281669f11937"
SQL_PATH = "powerdata"

credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "396d78f4ce28af1f5b3d0b52ecb9d86d2cad1443",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "118162761149734717464",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gcp-495afa1552%40dados-495afa1552.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
    }
)

util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


########################################DADOS OAMD#################################################

#################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['dataexpiracao', 'dataexpiracaocreditodcc', 'periodofim','periodoinicio','dataultimoacesso',
#                                                         'renegociadoate','dataultimoacesso','data_ultimolancamento_contrato','dataultimologin',
#                                                         'dataconsulta','primeiranfseemitida','datacadastro','datadesativacao'])
#
#
################ DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID = "rede_empresa_financeiro"
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(
    TABLE_ID,
    QUERY,
    lst_date_times=["finalimplantacao", "inicioimplantacao", "inicioproducao"],
)

# # ####################################### DADOS ZONAS #################################################

util.generate_data_all_zones("person")
util.generate_data_all_zones("dadosgerenciais", parallel=True)

util.create_view()

