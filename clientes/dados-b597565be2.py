# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-b597565be2" # LIVE
DATASET_ID = "dados_b597565be2"
CHAVE_REDE = "b597565be26ca95e6549a458149c8e9f"
SQL_PATH = "powerdata"



credentials = service_account.Credentials.from_service_account_info(
      {
        "type": "service_account",
        "project_id": f"{PROJECT_ID}",
        "private_key_id": "552cbb5364936227b2bf3e45c9a2f8ad71cfc349",
        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
        "client_email": "<EMAIL>",
        "client_id": "105711213597528358002",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-b597565be2%40dados-b597565be2.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
      }
    )


location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)

if os.environ.get('QR'):
  # personalizado.run_queries()
  util.run_queries()

elif os.getenv('CHARGEBACK'):
    from projetos_internos.integracao_sistema_pacto.chargeback import generate_chargeback
    chargeback = generate_chargeback(util.chaves.split(','))
    util.to_gbq(df=chargeback, dataset_id='dados_b597565be2', table_id='chargeback', project_id=PROJECT_ID, credentials=credentials)
elif os.getenv('LIVE_TICKET_MEDIO'):
    from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
    ticket_medio = generate_ticket_medio(util.chaves.split(','))
    util.to_gbq(df=ticket_medio, dataset_id='dados_b597565be2', table_id='ticket_medio', project_id=PROJECT_ID, credentials=credentials)
else:
    ################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
    TABLE_ID='rede_empresa_financeiro'
    QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
    util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])
    
    
    # # ####################################### DADOS ZONAS #################################################
    util.generate_data_all_zones('person')
    util.generate_data_all_zones('finan_lancamentos', parallel=True)
    util.generate_data_all_zones("acessos", parallel=True)
    util.generate_data_all_zones('tpv-previsao')
    util.generate_data_all_zones('dadosgerenciais', parallel=True)

    ################# BI de KPIs ##################
    util.generate_data_all_zones('faturamento', parallel=True)
    util.generate_data_all_zones('inadimplencia', parallel=True)
    util.generate_data_all_zones('alunos_inadimplentes', parallel=True)
    util.generate_data_all_zones('churn', parallel=True)
    util.generate_data_all_zones('contas_a_pagar', parallel=True)
    util.generate_data_all_zones('mov_contrato_saida', parallel=True)
    util.generate_data_all_zones('recebiveis', parallel=True)
    util.generate_data_all_zones('acessos_gympass', parallel=True)
    util.generate_data_all_zones('marketing', parallel=True)
    util.generate_data_all_zones('pendencias_financeiras', parallel=True)
    util.generate_data_all_zones('faturamento_consolidado', parallel=True)

    util.create_view(create_unidades=False)

    # # # ####################################### BI Anti-Fraude #################################################
    util.generate_data_all_zones('alteracao_autorizacao_cobranca', parallel=True)
    
    ## util.create_view()
    