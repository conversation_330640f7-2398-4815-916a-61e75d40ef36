# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-ce4c5aaa54" #IPANEMA SPORTS
DATASET_ID = "dados_ce4c5aaa54"
SQL_PATH = "powerdata"
CHAVES_ESPECIFICAS = "ce4c5aaa540c74b7167fc23283f7c42b,ce4c5aaa540c74b7167fc23283f7c42b"
CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)



credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "df22dd1fdf8054755d6db4aeb717918e5e98e9d4",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"), 
        "client_email": "<EMAIL>",
        "client_id": "103800896945185445821",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-ce4c5aaa54%40dados-ce4c5aaa54.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
    },
)

util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path = 'powerdata', location='southamerica-east1')
util.verificar_dataset(DATASET_ID)

if os.getenv('IPANEMA_TICKET_MEDIO'):
    from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
    ticket_medio = generate_ticket_medio(util.chaves.split(','))
    util.to_gbq(df=ticket_medio, dataset_id='dados_ce4c5aaa54', table_id='ticket_medio', project_id=PROJECT_ID, credentials=credentials)
else:
################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
    TABLE_ID='rede_empresa_financeiro'
    QUERY=util.getsql('rede_empresa_financeiro_semrede').format(CHAVES_EMPRESA_FINANCEIRO_FORMAT)
    util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones('dadosgerenciais', parallel=True)
util.generate_data_all_zones('contratos_duracao', parallel=True)


    ################# BI de KPIs ##################
util.generate_data_all_zones('faturamento', parallel=True)
util.generate_data_all_zones('inadimplencia', parallel=True)
util.generate_data_all_zones('alunos_inadimplentes', parallel=True)
util.generate_data_all_zones('churn', parallel=True)
util.generate_data_all_zones('contas_a_pagar', parallel=True)
util.generate_data_all_zones('mov_contrato_saida', parallel=True)
util.generate_data_all_zones('recebiveis', parallel=True)
util.generate_data_all_zones('acessos_gympass', parallel=True)


# util.create_view()



