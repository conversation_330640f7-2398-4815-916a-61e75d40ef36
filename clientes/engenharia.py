# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *
try:
  from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
except:
  pass

#Iniciar variáveis importantes
PROJECT_ID='dados-fd10e19d77'
SQL_PATH = 'powerdata'
# SQL_PATH_PERSONALIZADO = 'powerdata/personalizados'

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '243693591c4b9a507d0351f36c5d8d7b55868c53',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        'client_email': '<EMAIL>',
                        'client_id': '117924762975739159681',
                        'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
                        'token_uri': 'https://oauth2.googleapis.com/token',
                        'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',
                        'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-fd10e19d77%40dados-fd10e19d77.iam.gserviceaccount.com'
                      }
                      )

util = Util(PROJECT_ID, 'dataset_fd10e19d77', 'fd10e19d777ffea95ae185d53fb6c10c', credentials=credentials, base_path = SQL_PATH)
# personalizado = Util(PROJECT_ID, 'dataset_personalizados', 'fd10e19d777ffea95ae185d53fb6c10c', credentials=credentials, base_path = SQL_PATH_PERSONALIZADO)

      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)

if os.environ.get('QR'):
  # personalizado.run_queries()
  util.run_queries()

elif os.environ.get('EDC_TICKET_MEDIO'):
  from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
  ticket_medio = generate_ticket_medio(util.chaves.split(','))
  util.to_gbq(df=ticket_medio, dataset_id='dataset_fd10e19d77', table_id='ticket_medio', project_id=PROJECT_ID, credentials=credentials)

elif os.environ.get('EDC_COMISSAO_CONSULTOR'):
  from projetos_internos.integracao_sistema_pacto.comissao_consultor_edc import generate_comissao_consultor
  comissao_consultor = generate_comissao_consultor(util.chaves.split(','))
  util.to_gbq(df=comissao_consultor, dataset_id='dataset_fd10e19d77', table_id='comissao_consultor', project_id=PROJECT_ID, credentials=credentials)

else:
  ################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
  TABLE_ID='rede_empresa_financeiro'
  QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
  util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

  #######################################DADOS ZONAS#################################################
  util.generate_data_all_zones('person',  columns_drop=['faseatualcrm', 'codigocontrato', 'endereco'], parallel=True)
  util.generate_data_all_zones('dadosgerenciais', parallel=True)
  util.generate_data_all_zones('colab', parallel=True)
  util.generate_data_all_zones('plano', parallel=True)
  ##### ------Por enquanto no temos BI em atividade deste hoje é 01/06/2023..
  ##### util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc', parallel=True)
  ##### util.generate_data_all_zones('treinos_executados', prefix='bdmusc', parallel=True)
  ##### util.generate_data_all_zones('treinos_alunos', prefix='bdmusc', parallel=True)
  util.generate_data_all_zones('crm', parallel=True)
  ### Waller - 21/05/2025 - Desligar marketing até ser refatorado consulta de Marketing (alto custo de processamento non-indexed query)
  ###util.generate_data_all_zones('marketing', parallel=True)
  util.generate_data_all_zones('tpv-previsao', parallel=True)
  util.generate_data_all_zones('movparcela', parallel=True)
  # util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
  util.generate_data_all_zones('estorno_cancelamento', parallel=True)
  util.generate_data_all_zones('finan_lancamentos', parallel=True)
  util.generate_data_all_zones('valor_desconto', parallel=True)
  util.generate_data_all_zones('perfilacesso', parallel=True)
  util.generate_data_all_zones('crm_leads', parallel=True)

  ################# BI de KPIs ##################
  util.generate_data_all_zones('faturamento', parallel=True)
  util.generate_data_all_zones('inadimplencia', parallel=True)
  util.generate_data_all_zones('alunos_inadimplentes', parallel=True)
  util.generate_data_all_zones('churn', parallel=True)
  util.generate_data_all_zones('contas_a_pagar', parallel=True)
  util.generate_data_all_zones('mov_contrato_saida', parallel=True)
  util.generate_data_all_zones('recebiveis', parallel=True)
  util.generate_data_all_zones('acessos_gympass', parallel=True)
  util.generate_data_all_zones('renegociacoes', parallel=True)
  util.generate_data_all_zones('alunos_inadimplentes_edc', parallel=True)

  ################# Integração Foguete ##################
  util.generate_data_all_zones('alunos_integracao_fgt', parallel=True)
  util.generate_data_all_zones('alunos_integracao_fgt_erros', parallel=True)
  util.generate_data_all_zones('alunos_cancelados_integracao_fgt', parallel=True)
  util.generate_data_all_zones('alunos_inactive_or_pastdue_integracao_fgt', parallel=True)

  

############################# SQLS PERSONALIZADOS #####################################################
  ################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
  # QUERY_PERSONALIZADO=personalizado.getsql('../../pacto/rede_empresa_financeiro').format(personalizado.chave_rede)
  # personalizado.generate_data_oamd(TABLE_ID, QUERY_PERSONALIZADO, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])
  
  
  # personalizado.generate_data_all_zones('exclusao_visitantes', parallel=True)
  # personalizado.generate_data_all_zones('contrato_in_acesso', parallel=True)
  # personalizado.generate_data_all_zones('estorno_contrato_adm', parallel=True)
  # personalizado.generate_data_all_zones('consultor_contrato_alterado', parallel=True)
  # personalizado.generate_data_all_zones('estorno_contrato_recorr', parallel=True)
  # personalizado.generate_data_all_zones('operacao_contrato_retroa', parallel=True)
  # personalizado.generate_data_all_zones('estorno_contrato_usuario_comum', parallel=True)


####### arrumar
  # personalizado.generate_data_all_zones('parcelas_canceladas', parallel=True)
  # personalizado.generate_data_all_zones('contratos_db_alterada', parallel=True)
  # personalizado.generate_data_all_zones('pag_db_alterada', parallel=True)
  # personalizado.generate_data_all_zones('reneg_parcelas', parallel=True)

  # personalizado.generate_data_all_zones('edicoes_pagamento', parallel=True)
  # personalizado.generate_data_all_zones('contratos_cancelados', parallel=True)
  # personalizado.generate_data_all_zones('transferidos_contratos_cancelados', parallel=True)
  # personalizado.generate_data_all_zones('clientes_com_bonus', parallel=True)
  # personalizado.generate_data_all_zones('clientes_com_freepass', parallel=True)
  # personalizado.generate_data_all_zones('clientes_com_gympass', parallel=True)
  # personalizado.generate_data_all_zones('alunos_com_bolsa', parallel=True)

  # personalizado.generate_data_all_zones('clientes_autorizacao_sem_ren_auto', parallel=True)
  # personalizado.generate_data_all_zones('clientes_com_autorizacao_ten_auto', parallel=True)
  # personalizado.generate_data_all_zones('valor_em_descontos', parallel=True)
  # personalizado.generate_data_all_zones('alunos_excluidos_treinoweb_vinc', parallel=True)
  # personalizado.generate_data_all_zones('alunos_excluidos_treinoweb_vinc', parallel=True)

############################ SQL PRA VERIFICAR #########################################
# personalizado.generate_data_all_zones('estornos_recibo', parallel=True)

  

  

  






  
  

