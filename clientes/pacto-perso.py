# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-767c9fb55d'
DATASET_ID = "person_0001"
CHAVES_ESPECIFICAS = "60af85257bcbbea7569312fe6ab602ed,3ea0060c92d5b348e29a3c571dccc284,7238dcfb244fc931d63bd1aee764767a"
#  ESSENCE GYM, SAUDE E CIA, HOPE ANAPOIS

# ROTA DO DINHEIRO , ACADEMIA PACTO PRODUÇÃO, BRAVA ,
# 83cf8a192ebe8e66a0b71e6614843c7c,aca438e8c9e947e64db2236bb2f1f7a9,495afa1552fb8735271a281669f11937

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': 'd8429e9c4b0ce43cb97242044836bfbeb6a8d948',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "108542341588899437207",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-dados-767c9fb55d%40dados-767c9fb55d.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

location='southamerica-east1'
util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path = 'powerdata', location=location)
util.verificar_dataset(DATASET_ID)
      

########################################DADOS OAMD#################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################

# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
# TABLE_ID='rede_empresa_financeiro'
# QUERY=util.getsql('rede_empresa_financeiro_perso').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

########################################DADOS ZONAS#################################################
util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones('dadosgerenciais')
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('plano')
util.generate_data_all_zones('person')
util.generate_data_all_zones('crm', parallel=True)
util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)
util.generate_data_all_zones('grupo_de_risco', parallel=True)


# util.generate_data_all_zones('colab')
# util.generate_data_all_zones('marketing')
# util.generate_data_all_zones('pagamentosconjuntos')
# util.generate_data_all_zones('estorno_cancelamento')
# util.generate_data_all_zones('finan_lancamentos')
# util.generate_data_all_zones('agregacao_geral', parallel=True)
# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')
util.create_view()