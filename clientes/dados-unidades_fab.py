# -*- coding: utf-8 -*-
import sys
sys.path.insert(5, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='academia-de-verdade'
DATASET_ID = "unidades_fm"
CHAVES_ESPECIFICAS = "4baa252fbd2c2b1315d0a7741ab30713,a49c3cfdd8e5df17a81e2abf452af2f,c7c3aa83e2fe52809c484a6087760041,6bc1781a0f8eb36129ff63b60996d0d3,9d31739b0191a8a61fd278391e764d6e,25f66506620e09cf610e0e9c8cf20fa6,7634e2f319f4ba387cbfe8d53f07f11b,a436babea373321d88f0be56d96b8886"

# ---------------------------------------------------------------------------------------------------------------------------------------------------------------
CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '5bb5e37635dba35c47f7d7a6bd09d2496d56a210',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110607617536668275794",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/academia-de-verdade%40academia-de-verdade.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )



util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path='powerdata')
util.verificar_dataset(DATASET_ID)

util.generate_data_all_zones('prt_faturamento', parallel=True)
util.generate_data_all_zones('dfsinteticodw', parallel=True)
# util.generate_data_all_zones('faturamento', parallel=True)
# util.generate_data_all_zones('finan_lancamentos', parallel=True)

