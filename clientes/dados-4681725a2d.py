# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='dados-4681725a2d'
DATASET_ID='dataset_4681725a2d'
CHAVE_REDE='4681725a2d9c1717548b65f85e6a923d'  # REDE IRONBERG
SQL_PATH = 'powerdata'
   
credentials = service_account.Credentials.from_service_account_info(
                        {
                            "type": "service_account",
                            "project_id": {PROJECT_ID},
                            "private_key_id": "844433875b8c4bd5343376589ff1a5510ce52237",
                            "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                            "client_email": "<EMAIL>",
                            "client_id": "103791749431534863104",
                            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                            "token_uri": "https://oauth2.googleapis.com/token",
                            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-4681725a2d%40dados-4681725a2d.iam.gserviceaccount.com",
                            "universe_domain": "googleapis.com"
                            }
                      )


location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)

if os.environ.get('QR'):
  # personalizado.run_queries()
  util.run_queries()
  
elif os.getenv('IRONBERG_TICKET_MEDIO'):
    from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
    ticket_medio = generate_ticket_medio(util.chaves.split(','))
    util.to_gbq(df=ticket_medio, dataset_id='dataset_4681725a2d', table_id='ticket_medio', project_id=PROJECT_ID, credentials=credentials)

elif os.getenv('IRON_DEMONSTRATIVO_FINANCEIRO'):
    from projetos_internos.integracao_sistema_pacto.demonstrativo_financeiro import generate_demonstrativo_financeiro
    demonstrativo_financeiro = generate_demonstrativo_financeiro(util.chaves.split(','))
    util.to_gbq(df=demonstrativo_financeiro, dataset_id='dataset_4681725a2d', table_id='demonstrativo_financeiro', project_id=PROJECT_ID, credentials=credentials)
else:
################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
    TABLE_ID='rede_empresa_financeiro'
    QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
    util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


    # ####################################### DADOS ZONAS #################################################
    util.generate_data_all_zones('person')
    util.generate_data_all_zones('tpv-previsao')
    util.generate_data_all_zones('finan_lancamentos', parallel=True)
    util.generate_data_all_zones("acessos", parallel=True)
    util.generate_data_all_zones("estoque_movprodutos", parallel=True)
    util.generate_data_all_zones("estoque_produtos", parallel=True)


    ################# BI de KPIs ##################
    util.generate_data_all_zones('faturamento', parallel=True)
    util.generate_data_all_zones('inadimplencia', parallel=True)
    util.generate_data_all_zones('alunos_inadimplentes', parallel=True)
    util.generate_data_all_zones('churn', parallel=True)
    util.generate_data_all_zones('contas_a_pagar', parallel=True)
    util.generate_data_all_zones('mov_contrato_saida', parallel=True)
    util.generate_data_all_zones('recebiveis', parallel=True)
    util.generate_data_all_zones('acessos_gympass', parallel=True)
    util.generate_data_all_zones('dadosgerenciais', parallel=True)
    util.generate_data_all_zones('matricula_rematricula', parallel=True)
    util.generate_data_all_zones('qtd_bv', parallel=True)


    ##util.create_view(create_unidades=False)
