# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

# Iniciar variáveis importantes
PROJECT_ID = "dados-3a2efdcfe3" #SAUDE E CORPO
DATASET_ID = "dados_3a2efdcfe3"
CHAVE_REDE = "3a2efdcfe34790041bc984b6f14063be"
SQL_PATH = "powerdata"



credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "ffa3f6cf4256a0dae9715c4ca50724143a3415ae",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"), 
        "client_email": "<EMAIL>",
        "client_id": "107249415598112220931",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-3a2efdcfe3%40dados-3a2efdcfe3.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
    },
)




location="southamerica-east1"
util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# # ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('dadosgerenciais', parallel=True)

util.create_view()
