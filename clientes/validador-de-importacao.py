# -*- coding: utf-8 -*-
import sys
sys.path.insert(2, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='validador-de-importacao'
DATASET_ID = "validador_de_importacao"
CHAVES_ESPECIFICAS = "672f34ad4973f520e2b12a7f9dc87505,2cff380a0bdbd282326d7a98ca19491e,43a00ff3b4b1994469f06d22974ab1c0"
#ACAD. BLUM - GO // CT MONTANHA TEAM - MG // ESCOLA INSTITUTO O GRITO - MG
CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)

credentials = service_account.Credentials.from_service_account_info(
                      {
                        "type": "service_account",
                        "project_id": '{PROJECT_ID}',
                        "private_key_id": "48e9386872c25ce9739fa5134bf2cd0dc42bfdd6",
                        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "113929026552070589023",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-validador-de-importa%40validador-de-importacao.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                        }
)


util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path = 'powerdata', location='southamerica-east1')
util.verificar_dataset(DATASET_ID)


################# DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('rede_empresa_financeiro_semrede').format(CHAVES_EMPRESA_FINANCEIRO_FORMAT)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])

########################################DADOS ZONAS#################################################

# util.generate_data_all_zones('crm', parallel=True)
# util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)
# util.generate_data_all_zones('grupo_de_risco', parallel=True)

# util.generate_data_all_zones('agregacao_geral', parallel=True)
# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc') 

# util.generate_data_all_zones('person', parallel=True)
# util.generate_data_all_zones('dadosgerenciais', parallel=True)
# util.generate_data_all_zones('colab', parallel=True)
# util.generate_data_all_zones('plano', parallel=True)
# util.generate_data_all_zones('marketing', parallel=True)
# util.generate_data_all_zones('tpv-previsao', date_cols=['dt_competencia', 'dt_faturamento', 'dt_recebimento', 'dt_receita', 'dt_receita_original', 'dt_receita_prev', 'dt_vencimento'], parallel=True)
util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
# util.generate_data_all_zones('estorno_cancelamento', parallel=True)
# util.generate_data_all_zones('finan_lancamentos', parallel=True)
# util.generate_data_all_zones("acessos", parallel=True)

util.create_view()