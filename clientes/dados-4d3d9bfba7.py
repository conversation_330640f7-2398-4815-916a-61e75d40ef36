# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from google.oauth2 import service_account
from extras.Util import *


# Iniciar variáveis importantes
PROJECT_ID = "dados-4d3d9bfba7"  # REDE GREENLIFE
DATASET_ID = "dataset_4d3d9bfba7"
CHAVE_ID = "4d3d9bfba7deb921c53210266f5240c" #chave da rede
SQL_PATH = "powerdata"


credentials = service_account.Credentials.from_service_account_info(
    {
        "type": "service_account",
        "project_id": "{PROJECT_ID}",
        "private_key_id": "71bedb8905950a93e9c5c2f483396474e4e0f2e3",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "107348537436795337206",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-4d3d9bfba7%40dados-4d3d9bfba7.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com",
    },
)
location='southamerica-east1'
util = Util(PROJECT_ID, DATASET_ID, CHAVE_ID, credentials=credentials, location=location, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)


########################################DADOS OAMD#################################################

#################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['dataexpiracao', 'dataexpiracaocreditodcc', 'periodofim','periodoinicio','dataultimoacesso',
#                                                         'renegociadoate','dataultimoacesso','data_ultimolancamento_contrato','dataultimologin',
#                                                         'dataconsulta','primeiranfseemitida','datacadastro','datadesativacao'])
#
#
################ DADOS OAMD-Financeiro rede_empresa_financeiro ##################
if os.environ.get('QR'):
  # personalizado.run_queries()
  util.run_queries()

else:
    TABLE_ID = "rede_empresa_financeiro"
    QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
    util.generate_data_oamd(
        TABLE_ID,
        QUERY,
        lst_date_times=["finalimplantacao", "inicioimplantacao", "inicioproducao"],
    )


# # ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('marketing', parallel=True)
util.generate_data_all_zones('person')
util.generate_data_all_zones('dadosgerenciais', parallel=True)
util.generate_data_all_zones('colab', parallel=True)
util.generate_data_all_zones('plano', parallel=True)
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
util.generate_data_all_zones('estorno_cancelamento', parallel=True)
util.generate_data_all_zones('finan_lancamentos', parallel=True)
util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones('crm', parallel=True)
util.generate_data_all_zones('crm_leads', parallel=True)
util.generate_data_all_zones('treinos_dashboard', prefix='bdmusc', parallel=True)
util.generate_data_all_zones('conversao_lead_meta', parallel=True)
util.generate_data_all_zones('conversao_lead_atingida', parallel=True)

##### SQL-MANUTENÇÃO
util.generate_data_all_zones('indicados_atingido', parallel=True)
util.generate_data_all_zones('conversao_indicados_atingida', parallel=True)

util.generate_data_all_zones('treinos_colaboradores', parallel=True)
util.generate_data_all_zones('relatorio_indicacao', parallel=True)
util.generate_data_all_zones('relatorio_agendamento', parallel=True)
util.generate_data_all_zones('relatorio_receptivo', parallel=True)
util.generate_data_all_zones('percent_indicacao', parallel=True)
util.generate_data_all_zones('porcent_leads', parallel=True)
util.generate_data_all_zones('qtd_matricula_rematricula', parallel=True)
util.generate_data_all_zones('qtd_bv', parallel=True)
util.generate_data_all_zones('agendamentos_original', parallel=True)
util.generate_data_all_zones('indicados_original', parallel=True)
util.generate_data_all_zones('indicados_por_dia', parallel=True)
util.generate_data_all_zones('contrato_operacoes', parallel=True)

#util.create_view(create_unidades=False)
