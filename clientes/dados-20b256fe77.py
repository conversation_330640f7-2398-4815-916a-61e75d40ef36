# -*- coding: utf-8 -*-
#!/usr/bin/python3
import sys
sys.path.insert(2, '/usr/src/app')


from extras.Util import *


#Iniciar variáveis importantes
PROJECT_ID='dados-20b256fe77' 
DATASET_ID='dataset_20b256fe77'
CHAVE_REDE='20b256fe77271461f26777c63b77ffad'  # ACQUA MOCA
SQL_PATH = 'powerdata'
   
credentials = service_account.Credentials.from_service_account_info(
                      {
                        "type": "service_account",
                        "project_id": "{PROJECT_ID}",
                        "private_key_id": "c6aa9e0ad145e401ac8144a527135e8e21e15125",
                        "private_key": '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110878751805475662576",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-20b256fe77%40dados-20b256fe77.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

util = Util(PROJECT_ID, DATASET_ID, CHAVE_REDE, credentials=credentials, base_path = SQL_PATH)
util.verificar_dataset(DATASET_ID)




######################################## DADOS OAMD #################################################

################### DADOS OAMD-Financeiro oamd_financeiro_forma_pagamento ##################
# TABLE_ID='oamd_financeiro_forma_pagamento'
# QUERY=util.getsql('oamd_financeiro_forma_pagamento').format(util.chaves_escaped)
# util.generate_data_oamd(TABLE_ID, QUERY)


################ DADOS OAMD-Financeiro rede_empresa_financeiro ##################
TABLE_ID='rede_empresa_financeiro'
QUERY=util.getsql('../pacto/rede_empresa_financeiro').format(util.chave_rede)
util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])


# ####################################### DADOS ZONAS #################################################
util.generate_data_all_zones('person')
util.generate_data_all_zones('dadosgerenciais', parallel=True)
util.generate_data_all_zones('colab', parallel=True)
util.generate_data_all_zones('plano', parallel=True)

# ##### ------Por enquanto no temos BI em atividade deste hoje é 01/06/2023..
# util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('treinos_executados', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('treinos_alunos', prefix='bdmusc', parallel=True)
# util.generate_data_all_zones('crm', parallel=True)

util.generate_data_all_zones('marketing', parallel=True)

util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('tpv-previsao-taxa')
util.generate_data_all_zones("acessos", parallel=True)
util.generate_data_all_zones('pagamentosconjuntos', parallel=True)
util.generate_data_all_zones('estorno_cancelamento', parallel=True)
util.generate_data_all_zones('finan_lancamentos', parallel=True)

util.create_view()

# carga consultas...


util.generate_data_all_zones('aulas_coletivas', parallel=True)
util.generate_data_all_zones('agendamentos', parallel=True)
