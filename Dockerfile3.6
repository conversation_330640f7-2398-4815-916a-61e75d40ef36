#FROM python:3.10
#FROM amancevice/pandas:1.5.3
FROM python:3.6
# Allow statements and log messages to immediately appear in the Knative logs
ENV PYTHONUNBUFFERED True

# Install required modules

WORKDIR /usr/src/app

#COPY *.py requirements.txt ./
#ADD *.py scripts/
#ADD requirements.txt scripts/
#ADD sql/* sql/

RUN apt-get update -y

RUN apt-get install libatlas-base-dev -y
RUN pip3 install --upgrade pip


# RUN pip3 install numpy \
#     && pip3 install scipy \
#     && pip3 install clint \    
#     && pip3 install pandas_gbq \
#     && pip3 install google-crc32c \
#     && pip3 install xmltodict \
#     && pip3 install google-colab \    
#     && pip3 install librosa soundfile google-cloud-storage google-cloud-bigquery google-cloud google-cloud-pubsub google-cloud-bigquery -U

RUN pip3 install librosa soundfile \
    && pip3 install xmltodict \
    && pip3 install scipy pandas_gbq google-cloud-storage google-cloud-bigquery google-cloud google-cloud-pubsub termcolor tqdm py-trello \
    && pip3 install python-gitlab
    
ADD extras/* extras/

#CMD ["python3", "app.py"]
#CMD ["python3", "app-pratique.py"]
CMD [ "$@" ]
#ENTRYPOINT [ "python3" ]
#