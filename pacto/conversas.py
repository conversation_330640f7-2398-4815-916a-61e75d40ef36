# -*- coding: utf-8 -*-
import sys
sys.path.insert(5, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='conversas-ai'
DATASET_ID = "development"

# producao
CHAVES_ESPECIFICAS = "b3c787b3459ac48df1921ab2f20c970,e0aa2e5c7ed462647540be7a58465a26"
# ---------------------------------------------------------------------------------------------------------------------------------------------------------------
CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '50a86fe67d5acdaca6ab2faf80e28dd48983f8d7',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "100993773084168766446",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/academia-de-verdade%40academia-de-verdade.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )


util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path='pacto')

util.generate_data_all_zones('vendas_conversas', parallel=True)
