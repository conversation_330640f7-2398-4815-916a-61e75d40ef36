# -*- coding: utf-8 -*-

import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab import *
import pandas as pd

TABLE_ID = "z_oamd_planosdesucesso_join"
FINAL_CSV = "z_oamd_planosdesucesso_join.csv"
QUERY_COPY = f"""select 
	ps.criadoem,
	ps.dataconclusao,
	ps.datafinal,
	ps.descricao,
	ps.duracao,
	ps.nome as nome_planodesucesso,
	ps.nomeresponsavel as nome_resposavelcliente,
	ps.nomeresponsavelpacto,
	ps.porcentagemconcluida,
	ps.urlcontatoresponsavelpacto,
	ps.empresafinanceiro_codigo,
	ps.responsavelpacto_codigo,
	ps.datainicio,
	ps.modeloplanopucesso_codigo,
	ps.logplanosucesso,
	psa.datasituacao,
	psa.datafinal as psa_datafinal,
	psa.duracao as psa_duracao,
	psa.nome as nome_acaodoplano,
	psa.nomeresponsavel as nome_resposavel_acao,
	psa.notas,
	psa.ordem,
	psa.paginaurl,
	psa.situacao,
	psa.situacaocaminhotooltipster,
	psa.strduracao,
	psa.planosucesso_codigo,
	psa.acaomarco,
	psa.datamarco,
	psa.concluidopor,
	cs.ativo as cs_ativo,
	cs.nome as cs_nome
from planosucesso ps 
 left join customersuccess cs on ps.responsavelpacto_codigo = cs.codigo
 left join planosucessoacao psa on ps.codigo = psa.planosucesso_codigo"""

colab = Colab()

colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";")

date_columns = [
    "criadoem", "dataconclusao", "datafinal", "datainicio", "datasituacao", "psa_datafinal", "datamarco"
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)
