# -*- coding: utf-8 -*-
import sys
sys.path.insert(1, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID="pers-api-403023"
DATASET_ID="data"

credentials = service_account.Credentials.from_service_account_info(
{
  "type": "service_account",
  "project_id": "{PROJECT_ID}",
  "private_key_id": "e88716bfe01026331a97d2a75982b93f431feef6",
  "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
  "client_email": "<EMAIL>",
  "client_id": "102432334668396765640",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/conta-gbq-pers-api-pipeline%40pers-api-403023.iam.gserviceaccount.com"  
}
)

util = Util(PROJECT_ID, DATASET_ID, None, credentials=credentials, base_path = 'pacto')

util.read_csv_and_upload('cadastros', names=["nome", "cpf", "dtnasc"], lst_date_times=('dtnasc'), upload=True)


