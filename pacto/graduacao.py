# -*- coding: utf-8 -*-
"""Max Send z_oamd_empresaformapagamento.csv To BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1M2LyqmiVMbKryRnm2lHjXNE0Hhv15wsb
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab_BR import *

#engenharia do corpo and camp.redeEmpresa_id = 467
TABLE_ID = "graduacao"
FINAL_CSV = "graduacao.csv"
QUERY_COPY = """
SELECT
  e."chave",
  e."nomeBD",
  e."ativa",
  e."modulos",
  e."codigo",
  e."usointerno",
  e."usoteste",
  e."implantacao",
  e."emproducao",
  e."tipoempresa",
  r."nomeempresa" AS "Nome da Empresa"
FROM
  "public"."empresa" AS e
JOIN
  "public"."recursoempresa" AS r ON e."chave" = r."chave"
WHERE
  r."recurso" IN (
    'INSERIU_ALUNO_FICHATECNICA_GRD',
    'CRIOU_AVALIACAO_GRD',
    'REGISTROU_COMENTARIO_ALUNO_GRD',
    'INCLUIU_ALUNO_AVALIACAO_PROGRESSO_TROCA_NIVEL_GRD'
  )
ORDER BY
  e."codigo", e."nomeBD";
"""

colab = Colab()


colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";")
colab.upload_to_gbq(df, TABLE_ID)
