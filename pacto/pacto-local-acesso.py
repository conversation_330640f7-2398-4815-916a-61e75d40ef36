# -*- coding: utf-8 -*-
import sys
sys.path.insert(1, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID="oamd-e-financeiro-pacto"
DATASET_ID="analise_local_acesso_south"

credentials = service_account.Credentials.from_service_account_info(
{
  "type": "service_account",
  "project_id": "{PROJECT_ID}",
  "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
  "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
  "client_email": "<EMAIL>",
  "client_id": "117321030275837789997",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
}
)

util = Util(PROJECT_ID, DATASET_ID, None, credentials=credentials, base_path = 'pacto')

util.generate_data_all_zones('analise_local_acesso', parallel=True)


