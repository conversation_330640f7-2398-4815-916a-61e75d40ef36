# -*- coding: utf-8 -*-
"""Max Send z_oamd_empresaformapagamento.csv To BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1M2LyqmiVMbKryRnm2lHjXNE0Hhv15wsb
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab_BR import *

#engenharia do corpo and camp.redeEmpresa_id = 467
TABLE_ID = "nota_fiscal"
FINAL_CSV = "nota_fiscal.csv"
QUERY_COPY = """SELECT *
                FROM notafiscal
                WHERE EXTRACT(YEAR FROM data) > 2022
            """

colab = Colab()


colab.conectar_postgresql(QUERY_COPY, FINAL_CSV, PREFIX="enotas")
df = pd.read_csv(FINAL_CSV, delimiter=";")

date_columns = [
    "data", "dataretorno", "dataatualizacao"
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)

