# -*- coding: utf-8 -*-
"""Max Send z_oamd_empresaformapagamento.csv To BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1M2LyqmiVMbKryRnm2lHjXNE0Hhv15wsb
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab import *


TABLE_ID = "z_oamd_empresaformapagamento"
FINAL_CSV = "z_oamd_empresaformapagamento_view.csv"
QUERY_COPY = "select * from z_oamd_empresaformapagamento_view"

colab = Colab()


colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";")

date_columns = [
    "data_ultimolancamento_contrato", "dataexpiracao", "dataexpiracaocreditodcc",
    "periodofim", "periodoinicio", "dataexpiracaocreditodcc", "dataultimoacesso",
    "dataultimologin", "dataconsulta", "datacadastro", "datadesativacao",
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)
