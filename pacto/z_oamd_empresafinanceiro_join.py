# -*- coding: utf-8 -*-
"""Max Send z_oamd_empresafinanceiro_join.ipynb

Original file is located at
    https://colab.research.google.com/drive/1wdzU-frrZTUr3az5o4t7DR-v6mK0505Z
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab import *
import pandas as pd

low_memory = False
TABLE_ID = "z_oamd_empresafinanceiro_join"
FINAL_CSV = "z_oamd_empresafinanceiro_join.csv"
QUERY_COPY = f"""select 
      ef.*,
	  REGEXP_REPLACE(motivacao, '<[^>]+>', '', 'g') AS motivacao_limpa,
	  d.inicioproducao,
	  d.inicioimplantacao,
	  d.finalimplantacao,
	  d.tipocliente,
	  d.nivelreceitamensal,
	  d.categoria as detalhes_categoria,
      re.nome as rede_nome, 
      re.chaverede as rede_chave,
      re.tipocobrancapacto as rede_tipocobrancapacto,
      re.empresaprincipal as rede_empresaprincipal,
      re.urlservidorbiometricounificado as rede_ursservidorbiometrico,
 	  re.dtultimacobrancapacto,
      inte.nome AS Nome_implantador,
      respPacto.nome AS Nome_CS,
	  ( SELECT STRING_AGG(tag.nome, ', ') FROM tagempresafinanceiro tef JOIN tag ON tef.tag_codigo = tag.codigo and  tef.empresafinanceiro_codigo = ef.codigo ) as Tags,
	  e.chave 		as empresa_chave,
	  e.usoteste 	as empresa_usoteste,
	  e.modulos 	as empresa_modulos,
	  e.datacadastro as empresa_datacadastro, 
	  e.ativa 		as empresa_chaveativa,
	  e.urltreino  	as empresa_urltreino,
	  e.urltreinoweb as empresa_urltreinoweb,
	  e.identificadorempresa as empresa_nome_identificador,
	  case when e.urltreino = e.urltreinoweb then 'igual'
	       when ( (e.urltreino is null ) or (e.urltreinoweb is null)) then 'nao tem'
	       else 'diferente'
	  end 			as empresa_localurltreino,
	  e."nomeBD"  	as empresa_nomebd,
	  e."hostBD"    as empresa_hostDB,
	  ni.descricao 	as empresa_infra,
	  ni.datacenter as empresa_datacenter,
	  ni.infoinfra  as empresa_infoinfra,
	  CASE
	    WHEN ni.datacenter IN ('LOCAWEB', 'AWS', 'IWEB', 'LOCALHOST','OCI')
		  AND e.ativa = true
		  AND ni.descricao NOT IN ('HOMOLOGACAO', 'ZW_RELEASE', 'DEVI9', 'ZW_PRATIQUE')
		  AND e."nomeBD" NOT ILIKE '%automatizado%'
		  AND e."nomeBD" NOT ILIKE '%teste%'
		  AND e."nomeBD" NOT ILIKE '%justfit%'
		  and e."nomeBD" not ilike '%homologacao%' 
		  AND e."hostBD" NOT ILIKE '%homologacao%'
		  AND e."hostBD" NOT ILIKE '%devi9.pactosolucoes.com.br%'
		  AND e."hostBD" NOT ILIKE '%sesigoias.com.br%'
		  AND e."hostBD" NOT ILIKE '%release.pactosolucoes.com.br%'
	      AND e.datacadastro < NOW() - INTERVAL '1 DAY'
	    THEN 'Valido'
	    ELSE 'Invalido'
	  END AS status_infra
	FROM empresa e
    FULL JOIN empresafinanceiro ef on e.chave = ef.chavezw
	LEFT JOIN redeempresa re ON ef.redeempresa_id = re.id
	LEFT JOIN detalheempresa d ON ef.detalheempresa_codigo = d.codigo
	LEFT JOIN customersuccess respPacto ON respPacto.codigo = d.responsavelpacto_codigo
	LEFT JOIN customersuccess inte ON inte.codigo = d.implantador_codigo
	left join nomeinfra ni on ni.infoinfra = e.infoinfra"""

colab = Colab()

colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";", dtype={"cnpj": str, "identificador": str}, low_memory=False)

# Converter colunas problemáticas para string
# df['cnpj'] = df['cnpj'].astype(str)
# df['identificador'] = df['identificador'].astype(str)

# if 'cnpj_' in df.columns:
# 	df.rename(columns={'cnpj_': 'cnpj'}, inplace=True)


# if 'identificador' in df.columns:
#     df['identificador'] = df['identificador'].astype(str)

date_columns = [
    "datacadastro", "datadesativacao", "ultimaatualizacao",
    "datasuspensaoempresazw", "dataultimasicronizacaofavorecido",
    "dataexpiracaozw", "dataexpiracaooutros", "rede_dtultimacobrancapacto",
    "empresa_datacadastro",
	"inicioproducao","inicioimplantacao","finalimplantacao"
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)
