#!/usr/bin/python3
import sys
sys.path.insert(1, '/usr/src/app')

import pandas as pd
import requests
from pandas.io import gbq
from google.oauth2 import service_account
from http.client import HTTPConnection # py3
import os

#Declarar variáveis importantes
PROJECT_ID="oamd-e-financeiro-pacto"
DATASET_ID="oamd_financeiro"
TABLE_ID="usuarios_ativos"
FINAL_CSV="usuarios_ativos.csv"

# credential de conta de serviço do GCP para essa finalidade apenas
credentials = service_account.Credentials.from_service_account_info(
{
  "type": "service_account",
  "project_id": "oamd-e-financeiro-pacto",
  "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
  "private_key": "{0}".format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),  
  "client_email": "<EMAIL>",
  "client_id": "117321030275837789997",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
}
)

def get_data_zones():
    
    HTTPConnection.debuglevel = 1
    
    print(credentials)
    
    zonas = ["20", "21", "22", "23", "24", "26", "27", "30", "31", "32", "33", "34", "35", "36", "40", "41", "42", "50", "51", "52", "53", "54", "55", "60", "61", "63", "64", "65", "70", "71", "73", "80", "81", "82", "83", "84", "85", "90", "91", "92", "100", "101", "102", "200"]
    
    primeira = True 

    for x in zonas:
    
      print("Executando comandos em ZW-" + x)  
      HTTPS_PORT="443"
      PORTA_PG="5432"
      
      if x not in ["20", "21", "22", "30", "31", "32", "40", "41", "42", "50", "51", "52", "80", "81", "82", "90", "91", "92"]:       
        PORTA_PG="5432"     
      else:     
        PORTA_PG="54" + x 
        HTTPS_PORT="90" + x
      
      if x in ["100"]:
        PORTA_PG="5410"
        HTTPS_PORT="9010"
        
      if x in ["101","102"]:
        PORTA_PG="54" + x.replace("0","")
        HTTPS_PORT="90" + x.replace("0","")

      FINAL_CSV="zw_" + x + ".csv"

      #Baixar o arquivo gerado pelo ZW     
      QUERY="select (select count(col.codigo) from colaborador col inner join usuario u2 on u2.colaborador = col.codigo where situacao = 'AT') as qtd_usuarios_colab, (select count(c.codigo) from cliente c where c.situacao = 'AT') as qtd_usuarios_alunos"
            
      params = [
        ("op", "selectALL"), 
        ("prefixoBanco", "bdzillyon"), 
        ("format", "csv"), 
        ("mimetype", "text/csv"),
        ("hostPG", "localhost"),
        ("portaPG", PORTA_PG),        
        ("userPG", "zillyonweb"),
        ("pwdPG", "pactodb"),
        ("sql", QUERY),
        ("lgn", os.environ.get('LGN_KEY')),
        ("except", "bdzillyonjustfitteste|bdzillyonjustfitproducao|bdzillyonpactojuliano|bdzillyonpactosp|bdzillyonpactoavaliacao|bdzillyonpactogqszw91|bdzillyonpactogqsvictordiniz|bdzillyoncomercialpacto"),
      ]

      #!echo "{DATA_POST}"

      #!curl -d "{DATA_POST}" -o "{FINAL_CSV}"  "https://zw{x}.pactosolucoes.com.br:{HTTPS_PORT}/app/UpdateServlet" # type: ignore  
      
      response = requests.post("https://zw{0}.pactosolucoes.com.br:{1}/app/UpdateServlet".format(x, HTTPS_PORT), data = params)
      data = response.content
      
      with open(FINAL_CSV, 'wb') as s:
        s.write(data)

      df = pd.read_csv(FINAL_CSV, encoding='iso8859-1', sep='-----', engine='python', 
                       infer_datetime_format='%Y-%m-%d %H:%M:%S.%f', 
                       on_bad_lines='skip', skip_blank_lines=True, quotechar=None, quoting=3)
            
      if primeira:
        df.to_gbq(destination_table=DATASET_ID+'.'+TABLE_ID, project_id=PROJECT_ID, if_exists='replace', credentials=credentials)
      else:
        df.to_gbq(destination_table=DATASET_ID+'.'+TABLE_ID, project_id=PROJECT_ID, if_exists='append', credentials=credentials)      
      primeira = False
      del df


if __name__ == "__main__":
    get_data_zones()
