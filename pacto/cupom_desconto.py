# -*- coding: utf-8 -*-
"""Max Send z_oamd_empresaformapagamento.csv To BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1M2LyqmiVMbKryRnm2lHjXNE0Hhv15wsb
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab_BR import *

#engenharia do corpo and camp.redeEmpresa_id = 467
TABLE_ID = "cupom_desconto"
FINAL_CSV = "cupom_desconto.csv"
QUERY_COPY = """select
    hcup.chavePortadorCupom as chave,
    (select e.nomefantasia from empresafinanceiro e where e.chaveZW = hcup.chavePortadorCupom and e.empresazw = hcup.empresaportadorcupom limit 1) as empresa,
    hcup.nomeportadorcupom,
    hcup.datapremioportadorcupom,
    hcup.contrato,
    hcup.contratoestornado,
    hcup.numerocupom,
    hcup.cupomnomefixo,
    camp.id as campanha_id,
    camp.descricaocampanha
    from HistoricoUtilizacaoCupomDesconto hcup
inner join campanhaCupomDesconto camp on camp.id = hcup.campanhaCupomDesconto_id
    where 1 = 1
    and ((hcup.dataPremioPortadorCupom is not null) or (hcup.dataPremioAluno is not null)) and camp.redeEmpresa_id=467"""

colab = Colab()


colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";")

date_columns = [
    "datapremioportadorcupom"
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)
