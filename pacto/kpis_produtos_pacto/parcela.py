  # -*- coding: utf-8 -*-
import sys
sys.path.insert(1, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID="dadosmercado-415119"
DATASET_ID="sistema_pacto_geral"

credentials = service_account.Credentials.from_service_account_info(
{
        "type": "service_account",
        "project_id": "dadosmercado-415119",
        "private_key_id": "1ad17cf6a3324b511b5e936be7b587f2b3471595",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "104771537170357748391",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/dados-mercado%40dadosmercado-415119.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
}
)

util = Util(PROJECT_ID, DATASET_ID, None, credentials=credentials, location="southamerica-east1", base_path ='kpis_produtos_pacto')

if __name__ == "__main__":
    index = sys.argv[1]
    if index == "all":
        for file in os.listdir('sql/kpis_produtos_pacto'):
            if file.startswith('parcela_'):
                file_name = file.split('.')[0]
                try:
                    ver = int(file_name.split('_')[1])
                except:
                    continue
                util.generate_data_all_zones(file_name, prefix='bdzillyon', parallel=True, upload=True)
    else:
        indexes = index.split(',') if ',' in index else [index]
        for index in indexes:
          util.generate_data_all_zones(f'parcela_{index}', prefix='bdzillyon', parallel=True, upload=True)
