  # -*- coding: utf-8 -*-
import sys
sys.path.insert(1, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID="dadosmercado-415119"
DATASET_ID="sistema_pacto_geral"

credentials = service_account.Credentials.from_service_account_info(
{
        "type": "service_account",
        "project_id": "dadosmercado-415119",
        "private_key_id": "1ad17cf6a3324b511b5e936be7b587f2b3471595",
        "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
        "client_email": "<EMAIL>",
        "client_id": "104771537170357748391",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/dados-mercado%40dadosmercado-415119.iam.gserviceaccount.com",
        "universe_domain": "googleapis.com"
}
)

util = Util(PROJECT_ID, DATASET_ID, None, credentials=credentials, location="southamerica-east1", base_path ='kpis_produtos_pacto')

util.generate_data_oamd_pagination('graducacao_alunos_ficha_tecnica', 
                                   prefix='graduacao', 
                                   host='localhost',
                                   port=5488, 
                                   userPG='pydata', 
                                   pwd=os.environ.get("PWD_PG_AURORA"), 
                                   incremental_query=True, field_id='nivelaluno.id', upload=True)
