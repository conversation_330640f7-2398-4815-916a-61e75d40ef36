# -*- coding: utf-8 -*-
import sys
import os
import pandas as pd
from datetime import date
from google.cloud import bigquery
from google.oauth2 import service_account

sys.path.insert(1, '/usr/src/app')
from extras.Util import *

# 🔐 Credenciais GCP
PROJECT_ID = "dadosmercado-415119"
DATASET_ID = "sistema_pacto_geral"

credentials = service_account.Credentials.from_service_account_info({
    "type": "service_account",
    "project_id": "dadosmercado-415119",
    "private_key_id": "1ad17cf6a3324b511b5e936be7b587f2b3471595",
    "private_key": "{0}".format(os.environ.get("KEY_GCP_DATALAKE")).replace("\\n", "\n"),
    "client_email": "<EMAIL>",
    "client_id": "104771537170357748391",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/dados-mercado%40dadosmercado-415119.iam.gserviceaccount.com",
    "universe_domain": "googleapis.com"
})

# 🔧 Instanciar utilitário
util = Util(PROJECT_ID, DATASET_ID, None, credentials=credentials, location="southamerica-east1", base_path='pacto')

# 🔍 Buscar empresas ativas
df_empresas = util.query_sql_global("""
    SELECT
      chave,
      modulos,
      identificadorempresa
    FROM empresa
    WHERE ativa IS TRUE
      AND (usoteste IS FALSE OR usoteste IS NULL)
      AND (usointerno IS FALSE OR usointerno IS NULL)
""")

# 🔄 Verificar quais empresas usam treino por IA
resultados = []

for zona in util.get_zones():
    try:
        df_config = util.query_sql(zona, """
            SELECT
              identificadorempresa,
              LOWER(configuracao) AS configuracao,
              LOWER(CAST(valor AS STRING)) AS valor
            FROM configuracaosistema
            WHERE LOWER(configuracao) = 'permitir_criar_treino_automatizado_ia'
        """)

        empresas_ia_ativas = df_config[df_config['valor'] == 'true']['identificadorempresa'].unique()

        for empresa_id in empresas_ia_ativas:
            resultados.append({
                'identificadorempresa': empresa_id,
                'treino_ia_ativo': True
            })

    except Exception as e:
        print(f"[ERRO] Falha ao processar zona '{zona}': {e}")

# 🧩 Consolidar com empresas ativas
df_ia = pd.DataFrame(resultados).drop_duplicates()

df_empresas = df_empresas.merge(
    df_ia, on='identificadorempresa', how='left'
)

df_empresas['treino_ia_ativo'] = df_empresas['treino_ia_ativo'].fillna(False)
df_empresas['data_snapshot'] = pd.to_datetime(date.today())

# 💾 Salvar no BigQuery
client = bigquery.Client(project=PROJECT_ID, credentials=credentials)

table_id = f"{PROJECT_ID}.{DATASET_ID}.snapshots_treino_ia"

job = client.load_table_from_dataframe(df_empresas, table_id)
job.result()

print("[✔] Snapshot de treino IA salvo com sucesso.")
