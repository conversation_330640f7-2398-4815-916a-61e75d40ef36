# -*- coding: utf-8 -*-
"""Max Send z_oamd_pactopay to BigQuery.ipynb

Original file is located at
    https://colab.research.google.com/drive/1z0qScd5ty0w39lhWNEfIhC1nRNjj6EOF
"""
import sys
sys.path.insert(1, '/usr/src/app')

from extras.Colab_BR import *

TABLE_ID="z_oamd_pactopay_faturamento"
FINAL_CSV="z_oamd_pactopay_faturamento.csv"
QUERY_COPY="select * from z_oamd_pactopay_faturamento"

colab = Colab()

colab.conectar_postgresql(QUERY_COPY, FINAL_CSV)
df = pd.read_csv(FINAL_CSV, delimiter=";")

date_columns = [
    "data_consulta", "data_referencia"
]

colab.convert_to_datetime(df, date_columns)
colab.upload_to_gbq(df, TABLE_ID)
