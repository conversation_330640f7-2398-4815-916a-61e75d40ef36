# Documentação do Projeto Gymbot

## 1. Visão Geral
**Finalidade:** Integrar dados da API Gymbot ao Google BigQuery pra análises, com suporte a carga incremental e total.

**Estrutura:**
- `runners/`: Scripts por endpoint.
- `api/`: Integração com API e BigQuery.
- `utils/`: Configurações auxiliares.
- `config/`: Parâmetros centralizados.

**Motivo:** Modularidade e reutilização de código.

## 2. Componentes

### 2.1. EnhancedAPIClient
- **Funcionalidade:** Coleta dados paginados da API com retries e deduplicação.
- **Estrutura:** Sessão HTTP otimizada, logs reduzidos (`DEBUG` por página).
- **Finalidade:** Extrair dados brutos eficientemente.

### 2.2. BigQueryManager
- **Funcionalidade:** Carrega dados no BigQuery (incremental ou total), sanitiza campos.
- **Estrutura:** Suporte a novos campos, logs de sanitização em `DEBUG`.
- **Finalidade:** Transformar e armazenar dados.

### 2.3. Runners
- **Não Incrementais:** `WRITE_TRUNCATE` pra recriação (ex.: `chatbots`).
- **Incrementais:** `WRITE_APPEND` com filtro (ex.: `contacts`).
- **Estrutura:** Padrão pra escalabilidade.
- **Finalidade:** Orquestrar ETL por endpoint.

### 2.4. setup_logging
- **Funcionalidade:** Logging pra console (sem arquivos).
- **Estrutura:** Flexível pra níveis de log.
- **Finalidade:** Monitoramento enxuto.

## 3. Configuração
- **Arquivo:** `config.json`.
- **Motivo:** Manutenção centralizada.

## 4. Finalidade da API Gymbot
- **Contexto:** Sistema de automação.
- **Objetivo:** Data warehouse pra análises.