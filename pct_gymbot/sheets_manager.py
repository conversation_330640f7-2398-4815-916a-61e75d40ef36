import gspread
from google.oauth2.service_account import Credentials
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SheetsManager:
    def __init__(self, credentials_file: str, spreadsheet_id: str):
        self.credentials_file = credentials_file
        self.spreadsheet_id = spreadsheet_id

    def update_sessions(self, data: list):
        """Atualiza a planilha com dados de sessions"""
        if not data:
            logger.warning("Nenhum dado para atualizar")
            return False

        try:
            creds = Credentials.from_service_account_file(self.credentials_file)
            client = gspread.authorize(creds)
            spreadsheet = client.open_by_key(self.spreadsheet_id)

            # Obtém ou cria a worksheet
            try:
                worksheet = spreadsheet.worksheet("sessions")
                worksheet.clear()
            except gspread.WorksheetNotFound:
                worksheet = spreadsheet.add_worksheet("sessions", rows=1000, cols=20)

            # Prepara os dados
            headers = list(data[0].keys())
            rows = [headers] + [[item.get(h, "") for h in headers] for item in data]

            # Atualiza em lotes
            worksheet.update("A1", rows)
            logger.info(f"Dados atualizados: {len(data)} registros")
            return True

        except Exception as e:
            logger.error(f"Erro ao atualizar planilha: {str(e)}")
            return False
        