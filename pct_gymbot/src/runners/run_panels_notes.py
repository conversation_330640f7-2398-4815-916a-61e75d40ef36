# Autor: <PERSON>  
# Cientista de Dados | [17/04/2025]  
# Descrição: Script para buscar dados do endpoint panel/card/cardId/note baseado nos PanelIds e CardIds.

import json
import sys
from pathlib import Path
import requests

current_dir = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(current_dir))

from src.api.api_client import EnhancedAP<PERSON>lient
from src.api.bigquery_manager import BigQueryManager
from src.utils.logging_config import setup_logging

def run_panel_notes(config_path: str = str(Path(__file__).parent.parent.parent / "config" / "config.json")):
    logger = setup_logging("panel_notes")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)

        api_client = EnhancedAPIClient(
            base_url=CONFIG["api"]["base_url"],
            headers=CONFIG["api"]["headers"]
        )
        bq_manager = BigQueryManager(
            credentials_file=CONFIG["service_account_file"],
            project_id=CONFIG["project_id"]
        )

        if not bq_manager.initialize(logger):
            raise Exception("BigQuery initialization failed")

        # Configs dos endpoints
        panels_config = CONFIG["api"]["endpoints"]["panels"]
        panel_card_config = CONFIG["api"]["endpoints"]["panel_card"]
        panel_note_config = CONFIG["api"]["endpoints"]["panel_note"]
        excluded_fields = panel_note_config.get("excluded_fields", [])

        # Buscar panels
        panels_items, _ = api_client.fetch_paginated_data(
            endpoint=panels_config["path"],
            params=panels_config["params"],
            max_items=panels_config.get("max_items"),
            max_duration=panels_config.get("max_duration"),
            logger=None,
            use_pagination=True
        )

        note_items = []
        total_cards = 0
        failed_cards = 0
        failed_notes = 0
        successful_notes = 0

        for item in panels_items:
            panel_id = item.get("id") or item.get("PanelId") or item.get("panelId")
            if not panel_id:
                failed_cards += 1
                continue

            card_params = panel_card_config["params"].copy()
            card_params["PanelId"] = panel_id

            try:
                cards, _ = api_client.fetch_paginated_data(
                    endpoint=panel_card_config["path"],
                    params=card_params,
                    max_items=panel_card_config.get("max_items"),
                    max_duration=panel_card_config.get("max_duration"),
                    logger=None,
                    use_pagination=True
                )

                if not cards:
                    failed_cards += 1
                    continue

                total_cards += len(cards)

                for card in cards:
                    card_id = card.get("id") or card.get("CardId") or card.get("cardId")
                    if not card_id:
                        failed_cards += 1
                        continue

                    note_params = panel_note_config["params"].copy()
                    endpoint = panel_note_config["path"].format(cardId=card_id)

                    try:
                        notes, _ = api_client.fetch_paginated_data(
                            endpoint=endpoint,
                            params=note_params,
                            max_items=panel_note_config.get("max_items"),
                            max_duration=panel_note_config.get("max_duration"),
                            logger=None,
                            use_pagination=True
                        )

                        if notes:
                            for note in notes:
                                note["cardId"] = card_id
                            note_items.extend(notes)
                            successful_notes += len(notes)

                    except requests.exceptions.RequestException:
                        failed_notes += 1

            except requests.exceptions.RequestException:
                failed_cards += 1
                continue

        success = False
        if note_items:
            success = bq_manager.load_data(
                table_id="panel_notes",
                data=note_items,
                logger=logger,
                incremental_param=None,
                excluded_fields=excluded_fields,
                write_mode="WRITE_TRUNCATE"
            )

        logger.info(
            f"\n📊 Panel Notes Result:\n"
            f"- Total PanelIds: {len(panels_items)}\n"
            f"- Total Cards Encontrados: {total_cards}\n"
            f"- Total Notes Coletadas: {len(note_items)}\n"
            f"- Successful Notes: {successful_notes}\n"
            f"- Failed Cards: {failed_cards}\n"
            f"- Failed Notes: {failed_notes}\n"
            f"- Status: {'✅ SUCCESS' if success else '❌ FAILURE'}"
        )

    except Exception as e:
        logger.critical(f"Erro crítico: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    run_panel_notes()
