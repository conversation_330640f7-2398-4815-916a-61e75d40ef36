# Autor: <PERSON>  
# Cientista de Dados | [04/04/2025]  
# Descrição: Script individual para o endpoint da API (Contatos).

import json
import sys
from pathlib import Path

current_dir = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(current_dir))

from src.api.api_client import EnhancedAPIClient
from src.api.bigquery_manager import BigQueryManager
from src.utils.logging_config import setup_logging

def run_contacts(config_path: str = str(Path(__file__).parent.parent.parent / "config" / "config.json")):
    logger = setup_logging("contacts")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)

        api_client = EnhancedAPIClient(
            base_url=CONFIG["api"]["base_url"],
            headers=CONFIG["api"]["headers"]
        )
        bq_manager = BigQueryManager(
            credentials_file=CONFIG["service_account_file"],
            project_id=CONFIG["project_id"]
        )

        if not bq_manager.initialize(logger):
            raise Exception("BigQuery initialization failed")

        config = CONFIG["api"]["endpoints"]["contacts"]
        params = config["params"].copy()
        excluded_fields = config.get("excluded_fields", [])

        # Buscar dados da API
        items, stats = api_client.fetch_paginated_data(
            endpoint=config["path"],
            params=params,
            max_items=config.get("max_items"),
            max_duration=config.get("max_duration"),
            logger=logger,
            use_pagination=True
        )

        # Logar os dados brutos pra inspeção
        #logger.info(f"Dados brutos retornados pela API: {json.dumps(items, ensure_ascii=False)}")

        # Carregar os dados (incremental com WRITE_APPEND por padrão)
        success = bq_manager.load_data(
            table_id="contacts",
            data=items,
            logger=logger,
            incremental_param=None,
            excluded_fields=excluded_fields,
            write_mode="WRITE_TRUNCATE"
        )

        logger.info(
            f"\n📊 Contacts Result:\n"
            f"- Items: {len(items)}\n"
            f"- Pages: {stats['successful_pages']}\n"
            f"- Status: {'✅ SUCCESS' if success else '❌ FAILURE'}"
        )
    except Exception as e:
        logger.critical(f"Critical error: {str(e)}", exc_info=True)

if __name__ == "__main__":
    run_contacts()