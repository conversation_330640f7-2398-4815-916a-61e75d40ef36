import os
import logging
import argparse
from jira import JIRA
from google.cloud import bigquery
from google.oauth2 import service_account
import re
from datetime import datetime, date
from typing import List, Dict, Optional, Any
import json
import dotenv
import sys

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Carrega variáveis de ambiente do arquivo configuracoes.env
dotenv.load_dotenv()

# Classe BigQueryManager
class BigQueryManager:
    def __init__(self, credentials_file: str = r"C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json", 
                 project_id: str = "oamd-e-financeiro-pacto"):
        self.credentials_file = credentials_file
        self.project_id = project_id
        self.client = None

    def initialize(self, logger) -> bool:
        try:
            private_key = os.environ.get("KEY_GCP_DATALAKE")
            if private_key:
                logger.info("Usando KEY_GCP_DATALAKE como private_key no ambiente (GitLab)")
                credentials_info = {
                    "type": "service_account",
                    "project_id": self.project_id,
                    "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
                    "private_key": private_key.replace('\\n', '\n'),
                    "client_email": "<EMAIL>",
                    "client_id": "117321030275837789997",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
                }
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = bigquery.Client(project=self.project_id, credentials=credentials, location="southamerica-east1")
                logger.info("✅ BigQuery connection established usando private_key do GitLab")
            else:
                logger.info(f"Tentando usar arquivo local: {self.credentials_file}")
                if os.path.exists(self.credentials_file):
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.credentials_file
                    self.client = bigquery.Client(project=self.project_id, location="southamerica-east1")
                    logger.info(f"Usando arquivo local: {self.credentials_file}")
                else:
                    logger.error(f"Arquivo de credencial não encontrado: {self.credentials_file}")
                    raise FileNotFoundError(f"Credentials file not found at {self.credentials_file}")

            logger.info("✅ BigQuery connection established")
            return True
        except Exception as e:
            logger.error(f"❌ Initialization failed: {str(e)}", exc_info=True)
            return False

    def _sanitize_field_name(self, field_name: str) -> str:
        import unicodedata
        field_name = unicodedata.normalize('NFKD', field_name).encode('ASCII', 'ignore').decode('ASCII')
        sanitized = re.sub(r'[^0-9a-zA-Z_]', '_', field_name.lower())
        sanitized = re.sub(r'_+', '_', sanitized).strip('_')
        return sanitized if sanitized else 'unnamed_field'

    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_', excluded_fields: List[str] = None) -> List[Dict]:
        if excluded_fields is None:
            excluded_fields = []
        
        filtered_d = {k: v for k, v in d.items() if k not in excluded_fields}
        base_items = {k: v for k, v in filtered_d.items() if not isinstance(v, (dict, list))}
        nested_items = {k: v for k, v in filtered_d.items() if isinstance(v, (dict, list))}
        
        flattened = []
        if not nested_items:
            flattened.append(base_items)
            return flattened

        has_list = False
        for key, value in nested_items.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key
            if isinstance(value, list) and value:
                has_list = True
                for i, subitem in enumerate(value):
                    if isinstance(subitem, dict):
                        sub_flattened = self._flatten_dict(subitem, new_key, sep=sep, excluded_fields=excluded_fields)
                        for sub in sub_flattened:
                            flattened.append({**base_items, **sub})
                    else:
                        flattened.append({**base_items, f"{new_key}_{i}": subitem})
            elif isinstance(value, dict):
                sub_flattened = self._flatten_dict(value, new_key, sep=sep, excluded_fields=excluded_fields)
                for sub in sub_flattened:
                    flattened.append({**base_items, **sub})

        if not has_list and not flattened:
            flattened.append(base_items)
        
        return flattened if flattened else [base_items]

    def _sanitize_data(self, data: List[Dict], logger, excluded_fields: List[str] = None) -> List[Dict]:
        sanitized_data = []
        for item in data:
            flattened_items = self._flatten_dict(item, excluded_fields=excluded_fields)
            for flattened_item in flattened_items:
                sanitized_item = {}
                for key, value in flattened_item.items():
                    sanitized_key = self._sanitize_field_name(key)
                    if sanitized_key == "timeservice" and isinstance(value, str):
                        try:
                            h, m, s = map(int, value.split(':'))
                            if 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59:
                                sanitized_item[sanitized_key] = value
                            else:
                                if h > 23 and m <= 59 and s <= 59:
                                    corrected_h = int(str(h)[:2]) if len(str(h)) >= 2 else h % 24
                                    if 0 <= corrected_h <= 23:
                                        corrected_value = f"{corrected_h:02d}:{m:02d}:{s:02d}"
                                        logger.debug(f"Valor inválido de timeService '{value}' corrigido pra '{corrected_value}'")
                                        sanitized_item[sanitized_key] = corrected_value
                                    else:
                                        logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None (não corrigível)")
                                        sanitized_item[sanitized_key] = None
                                else:
                                    logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None")
                                    sanitized_item[sanitized_key] = None
                        except (ValueError, TypeError):
                            logger.debug(f"Formato inválido de timeService '{value}' corrigido pra None")
                            sanitized_item[sanitized_key] = None
                    else:
                        sanitized_item[sanitized_key] = value
                sanitized_data.append(sanitized_item)
                logger.debug(f"Dado sanitizado: {json.dumps(sanitized_item, ensure_ascii=False)}")
        logger.info(f"Total de itens sanitizados: {len(sanitized_data)}")
        return sanitized_data

    def table_exists(self, table_id: str, logger) -> bool:
        try:
            table_ref = self.client.dataset("GYMBOT").table(table_id)
            self.client.get_table(table_ref)
            logger.info(f"Tabela {table_id} existe no BigQuery")
            return True
        except Exception as e:
            logger.warning(f"Tabela {table_id} não encontrada: {str(e)}")
            return False

    def column_exists(self, table_id: str, column_name: str, logger) -> bool:
        try:
            table_ref = self.client.dataset("GYMBOT").table(table_id)
            table = self.client.get_table(table_ref)
            for field in table.schema:
                if field.name.lower() == column_name.lower():
                    logger.info(f"Coluna {column_name} encontrada na tabela {table_id}")
                    return True
            logger.warning(f"Coluna {column_name} não encontrada na tabela {table_id}")
            return False
        except Exception as e:
            logger.error(f"Erro ao verificar coluna {column_name} na tabela {table_id}: {str(e)}")
            return False

    def delete_data_by_date(self, table_id: str, start_date: str, end_date: str, logger) -> bool:
        """Deleta registros da tabela onde a data de criação está entre start_date e end_date."""
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return False
        
        if not self.table_exists(table_id, logger):
            logger.info(f"Tabela {table_id} não existe, pulando deleção")
            return True

        if not self.column_exists(table_id, "created", logger):
            logger.error("Coluna 'created' não encontrada na tabela, abortando deleção")
            return False

        try:
            datetime.strptime(start_date, "%Y-%m-%d")
            datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError as e:
            logger.error(f"Formato de data inválido para start_date ({start_date}) ou end_date ({end_date}): {str(e)}")
            return False

        query = f"""
        DELETE FROM `{self.project_id}.GYMBOT.{table_id}`
        WHERE DATE(created) BETWEEN '{start_date}' AND '{end_date}'
        """
        logger.debug(f"Executando query de deleção: {query}")
        try:
            query_job = self.client.query(query)
            query_job.result()
            logger.info(f"Registros deletados com sucesso da tabela {table_id} entre {start_date} e {end_date}")
            return True
        except Exception as e:
            logger.error(f"Falha ao deletar registros da tabela {table_id}: {str(e)}", exc_info=True)
            return False

    def ensure_incremental_state_table(self, logger) -> bool:
        """Verifica se a tabela incremental_state existe e a cria se necessário."""
        table_id = "incremental_state"
        if not self.table_exists(table_id, logger):
            logger.warning(f"Tabela {table_id} não existe, criando...")
            try:
                schema = [
                    bigquery.SchemaField("table_name", "STRING", mode="REQUIRED"),
                    bigquery.SchemaField("last_processed_timestamp", "TIMESTAMP", mode="NULLABLE"),
                    bigquery.SchemaField("last_run_at", "TIMESTAMP", mode="REQUIRED")
                ]
                table_ref = self.client.dataset("GYMBOT").table(table_id)
                table = bigquery.Table(table_ref, schema=schema)
                self.client.create_table(table)
                logger.info(f"Tabela {table_id} criada com sucesso")
            except Exception as e:
                logger.error(f"Falha ao criar tabela {table_id}: {str(e)}")
                return False
        logger.info(f"Tabela {table_id} verificada com sucesso")
        return True

    def get_last_processed_timestamp(self, table_name: str, logger) -> Optional[datetime]:
        """Obtém o último timestamp processado para a tabela especificada."""
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return None

        query = f"""
        SELECT last_processed_timestamp
        FROM `{self.project_id}.GYMBOT.incremental_state`
        WHERE table_name = '{table_name}'
        """
        try:
            query_job = self.client.query(query)
            results = query_job.result()
            for row in results:
                return row.last_processed_timestamp
            logger.info(f"Nenhum timestamp encontrado para table_name={table_name}")
            return None
        except Exception as e:
            logger.error(f"Falha ao obter last_processed_timestamp para {table_name}: {str(e)}")
            return None

    def update_incremental_state(self, table_name: str, last_processed_timestamp: Optional[str], last_run_at: str, logger) -> bool:
        """Atualiza o estado incremental com o último timestamp e last_run_at."""
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return False

        timestamp_value = f"TIMESTAMP('{last_processed_timestamp}')" if last_processed_timestamp else "NULL"
        query = f"""
        MERGE `{self.project_id}.GYMBOT.incremental_state` T
        USING (SELECT '{table_name}' AS table_name, {timestamp_value} AS last_processed_timestamp, TIMESTAMP('{last_run_at}') AS last_run_at) S
        ON T.table_name = S.table_name
        WHEN MATCHED THEN
            UPDATE SET last_processed_timestamp = S.last_processed_timestamp, last_run_at = S.last_run_at
        WHEN NOT MATCHED THEN
            INSERT (table_name, last_processed_timestamp, last_run_at)
            VALUES (S.table_name, S.last_processed_timestamp, S.last_run_at)
        """
        logger.debug(f"Executando query MERGE: {query}")
        try:
            query_job = self.client.query(query)
            query_job.result()
            logger.info(f"Estado incremental atualizado para table_name={table_name}, last_processed_timestamp={last_processed_timestamp or 'NULL'}, last_run_at={last_run_at}")
            return True
        except Exception as e:
            logger.error(f"Falha ao atualizar incremental_state para {table_name}: {str(e)}")
            return False

    def load_data(self, table_id: str, data: List[Dict], logger, excluded_fields: List[str] = None, 
                  write_mode: str = "WRITE_APPEND", job_config: Optional[Dict] = None) -> bool:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            raise ValueError("BigQuery client not initialized")

        if not data:
            logger.info(f"Nenhum dado pra carregar na tabela {table_id}")
            return True

        logger.debug(f"Dados brutos recebidos: {json.dumps(data, ensure_ascii=False)}")
        logger.info(f"Total de itens recebidos da API pra {table_id}: {len(data)}")
        logger.info(f"Modo de escrita: {write_mode}")

        sanitized_data = self._sanitize_data(data, logger, excluded_fields=excluded_fields)
        if not sanitized_data:
            logger.error("Nenhum dado sanitizado pra carregar após sanitização")
            return False

        logger.info(f"Total de itens sanitizados pra carga: {len(sanitized_data)}")
        dataset_ref = self.client.dataset("GYMBOT")
        table_ref = dataset_ref.table(table_id)

        load_job_config = bigquery.LoadJobConfig()
        if write_mode == "WRITE_TRUNCATE":
            logger.info(f"Tabela {table_id} será truncada e recarregada com WRITE_TRUNCATE.")
            load_job_config.write_disposition = "WRITE_TRUNCATE"
            load_job_config.autodetect = True
        else:
            try:
                table = self.client.get_table(table_ref)
                logger.info(f"Tabela {table_id} já existe, usando WRITE_APPEND com esquema existente.")
                load_job_config.write_disposition = "WRITE_APPEND"
            except Exception:
                logger.info(f"Tabela {table_id} não existe, criando com WRITE_TRUNCATE e autodetect.")
                load_job_config.write_disposition = "WRITE_TRUNCATE"
                load_job_config.autodetect = True

        if job_config:
            logger.info(f"Aplicando configurações personalizadas de job_config: {job_config}")
            for key, value in job_config.items():
                setattr(load_job_config, key, value)

        try:
            logger.debug(f"Enviando dados pro BigQuery: {json.dumps(sanitized_data, ensure_ascii=False)}")
            job = self.client.load_table_from_json(sanitized_data, table_ref, job_config=load_job_config)
            job.result()
            logger.info(f"Dados carregados com sucesso na tabela {table_id} ({len(sanitized_data)} itens)")
            return True
        except Exception as e:
            logger.error(f"Falha ao carregar dados na tabela {table_id}: {str(e)}", exc_info=True)
            return False

# Configurações
JIRA_URL = "https://pacto.atlassian.net"
JIRA_USERNAME = "<EMAIL>"
JIRA_API_TOKEN = os.getenv("JIRA_API_TOKEN")
PROJECT_ID = "oamd-e-financeiro-pacto"
CREDENTIALS_FILE = r"C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json"
DATASET_ID = "GYMBOT"
TABLE_NAME = "jira_issues"

# Inicialização do BigQueryManager
bq_manager = BigQueryManager(credentials_file=CREDENTIALS_FILE, project_id=PROJECT_ID)

def connect_to_jira():
    if not JIRA_API_TOKEN:
        logger.error("JIRA_API_TOKEN não configurado")
        raise ValueError("JIRA_API_TOKEN não configurado")
    try:
        jira_client = JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USERNAME, JIRA_API_TOKEN)
        )
        logger.info("Conexão com Jira estabelecida com sucesso")
        return jira_client
    except Exception as e:
        logger.error(f"Erro ao conectar ao Jira: {str(e)}")
        raise

def extract_jira_issues(jira_client, jql_query: str) -> List[Dict]:
    issues = []
    logger.info(f"Executando consulta JQL: {jql_query}")
    try:
        start_at = 0
        max_results = 100
        total_issues = 0
        while True:
            batch = jira_client.search_issues(
                jql_query,
                startAt=start_at,
                maxResults=max_results,
                fields="*all",
                validate_query=True  # Adiciona validação explícita do JQL
            )
            logger.info(f"Lote retornado: {len(batch)} issues (startAt={start_at}, maxResults={max_results})")
            if not batch:
                break
            issues.extend(batch)
            total_issues = batch.total  # Obtém o total de issues esperado
            start_at += max_results
            logger.info(f"Extraindo issues: {len(issues)} issues coletadas até agora de {total_issues} esperados.")
            if start_at >= total_issues:
                break  # Para o loop se todos os issues foram coletados
        
        logger.info(f"Total de issues extraídas: {len(issues)} de {total_issues} esperados.")
        
        # Gerar load_date como timestamp atual
        load_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"Adicionando load_date: {load_date} a todas as issues")

        processed_issues = []
        for issue in issues:
            this_issue = {}
            fields = issue.raw.get('fields', {})
            try:
                this_issue['Key'] = issue.key if issue.key else 'None'
                this_issue['LoadDate'] = load_date
                
                if 'customfield_10099' in fields:
                    customfield_10099 = fields.get('customfield_10099')
                    this_issue['Category'] = customfield_10099.get('value', 'None') if customfield_10099 else 'None'

                if 'customfield_11117' in fields:
                    customfield_11117 = fields.get('customfield_11117')
                    this_issue['Desenvolvedores'] = ', '.join(
                        [dev['displayName'] for dev in customfield_11117]
                    ) if customfield_11117 else 'None'

                if 'customfield_11118' in fields:
                    customfield_11118 = fields.get('customfield_11118')
                    this_issue['QAs'] = ', '.join(
                        [qa['displayName'] for qa in customfield_11118]
                    ) if customfield_11118 else 'None'

                if 'reporter' in fields:
                    reporter = fields.get('reporter')
                    this_issue['Reporter'] = reporter.get('displayName', 'None') if reporter else 'None'

                if 'assignee' in fields:
                    assignee = fields.get('assignee')
                    this_issue['Assignee'] = assignee.get('displayName', 'None') if assignee else 'None'

                if 'created' in fields:
                    created = fields.get('created')
                    this_issue['Created'] = created if created else 'None'

                if 'updated' in fields:
                    updated = fields.get('updated')
                    this_issue['UpdatedAt'] = updated if updated else 'None'

                if 'issuetype' in fields:
                    issuetype = fields.get('issuetype')
                    this_issue['Type'] = issuetype.get('name', 'None') if issuetype else 'None'

                if 'timeoriginalestimate' in fields:
                    this_issue['Estimate'] = fields.get('timeoriginalestimate', 0.0)

                if 'priority' in fields:
                    priority = fields.get('priority')
                    this_issue['Priority'] = priority.get('name', 'None') if priority else 'None'

                if 'project' in fields:
                    project = fields.get('project')
                    this_issue['ProjectName'] = project.get('name', 'None') if project else 'None'
                    this_issue['ProjectKey'] = project.get('key', 'None') if project else 'None'

                if 'resolutiondate' in fields:
                    resolutiondate = fields.get('resolutiondate')
                    this_issue['Resolved'] = resolutiondate if resolutiondate else 'None'

                if 'status' in fields:
                    status = fields.get('status')
                    this_issue['Status'] = status.get('name', 'None') if status else 'None'

                if 'aggregatetimespent' in fields:
                    this_issue['TimeSpent'] = fields.get('aggregatetimespent', 0.0)

                if 'workratio' in fields:
                    this_issue['WorkRatio'] = fields.get('workratio', 0)

                if 'customfield_10052' in fields:
                    end_date = fields.get('customfield_10052')
                    this_issue['EndDate'] = end_date if end_date else 'None'

                if 'progress' in fields:
                    progress = fields.get('progress')
                    this_issue['Progress'] = progress.get('percent', 0) / 100 if progress else 0.0

                if 'parent' in fields:
                    parent = fields.get('parent')
                    this_issue['Parent'] = parent.get('key', 'None') if parent else 'None'

                if 'customfield_11116' in fields:
                    customfield_11116 = fields.get('customfield_11116')
                    this_issue['DiasAtraso'] = customfield_11116 if customfield_11116 else None

                if 'customfield_10069' in fields:
                    customfield_10069 = fields.get('customfield_10069')
                    this_issue['FailedTests'] = customfield_10069 if customfield_10069 else 'None'

                if 'description' in fields:
                    description = fields.get('description')
                    this_issue['Description'] = description if description else 'None'

                if 'summary' in fields:
                    this_issue['Summary'] = fields.get('summary', 'None')

                if 'aggregatetimeoriginalestimate' in fields:
                    this_issue['OriginalEstimate'] = fields.get('aggregatetimeoriginalestimate', 0.0)

                if 'aggregatetimeestimate' in fields:
                    this_issue['RemainingEstimate'] = fields.get('aggregatetimeestimate', 0.0)

                if 'duedate' in fields:
                    duedate = fields.get('duedate')
                    this_issue['DueDate'] = duedate if duedate else 'None'

                if 'customfield_10015' in fields:
                    start_date = fields.get('customfield_10015')
                    this_issue['StartDate'] = start_date if start_date else 'None'

                if 'statuscategory' in fields:
                    status_category = fields.get('statuscategory')
                    this_issue['StatusCategory'] = status_category.get('name', 'None') if status_category else 'None'

                if 'customfield_10046' in fields:
                    nome_empresa = fields.get('customfield_10046')
                    this_issue['NomeEmpresa'] = nome_empresa if nome_empresa else 'None'

                if 'customfield_10020' in fields:
                    sprint = ', '.join(
                        [sprint['name'] for sprint in fields.get('customfield_10020')]
                    ) if fields.get('customfield_10020') else 'None'
                    this_issue['Sprint'] = sprint if sprint else 'None'

                # Garantir tipos compatíveis
                fillna_values = {
                    'Desenvolvedores': 'None',
                    'QAs': 'None',
                    'Estimate': 0.0,
                    'Resolved': 'None',
                    'TimeSpent': 0.0,
                    'WorkRatio': 0,
                    'EndDate': 'None',
                    'DiasAtraso': 0,
                    'Description': 'None',
                    'FailedTests': 'None',
                    'Parent': 'None',
                    'OriginalEstimate': 0.0,
                    'RemainingEstimate': 0.0,
                    'DueDate': 'None',
                    'Category': 'None',
                    'Reporter': 'None',
                    'Assignee': 'None',
                    'Type': 'None',
                    'Priority': 'None',
                    'ProjectName': 'None',
                    'ProjectKey': 'None',
                    'Status': 'None',
                    'Summary': 'None',
                    'Key': 'None',
                    'Progress': 0.0,
                    'StartDate': 'None',
                    'StatusCategory': 'None',
                    'NomeEmpresa': 'None',
                    'Sprint': 'None',
                    'Created': 'None',
                    'UpdatedAt': 'None',
                    'LoadDate': load_date
                }
                for key, default in fillna_values.items():
                    if key not in this_issue or this_issue[key] is None:
                        this_issue[key] = default

                processed_issues.append(this_issue)
            except Exception as e:
                logger.error(f"Erro ao processar issue {issue.key}: {str(e)}")
                continue
        
        logger.info(f"Total de issues processadas: {len(processed_issues)}")
        return processed_issues
    except Exception as e:
        logger.error(f"Erro ao extrair issues do Jira para JQL '{jql_query}': {str(e)}", exc_info=True)
        raise

def connect_to_jira():
    if not JIRA_API_TOKEN:
        logger.error("JIRA_API_TOKEN não configurado")
        raise ValueError("JIRA_API_TOKEN não configurado")
    try:
        jira_client = JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USERNAME, JIRA_API_TOKEN)
        )
        # Verificar informações do usuário
        user_info = jira_client.myself()
        logger.info(f"Conexão com Jira estabelecida com sucesso. Usuário: {user_info['displayName']} ({user_info['emailAddress']})")
        return jira_client
    except Exception as e:
        logger.error(f"Erro ao conectar ao Jira: {str(e)}", exc_info=True)
        raise

def load_to_bigquery(issues: List[Dict[str, Any]], load_date: str, created_date: Optional[str] = None):
    try:
        if not bq_manager.client:
            if not bq_manager.initialize(logger):
                logger.error("Falha ao inicializar BigQueryManager")
                raise ValueError("Falha ao inicializar BigQueryManager")
        
        # Verificar se a tabela incremental_state existe
        if not bq_manager.ensure_incremental_state_table(logger):
            logger.error("Falha ao verificar/criar tabela incremental_state, abortando carga")
            raise ValueError("Falha ao verificar/criar tabela incremental_state")

        # Definir o modo de escrita com base em created_date
        write_mode = "WRITE_TRUNCATE" if not created_date else "WRITE_APPEND"
        logger.info(f"Modo de escrita selecionado: {write_mode}")

        # Configurar job_config condicionalmente
        job_config = {
            "autodetect": True,
            "write_disposition": write_mode
        }
        if write_mode == "WRITE_APPEND":
            job_config["schema_update_options"] = [bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION]
            logger.info(f"Aplicando schema_update_options para WRITE_APPEND: {job_config['schema_update_options']}")

        # Deletar registros no BigQuery apenas no modo incremental
        filtered_issues = issues
        if created_date:
            try:
                datetime.strptime(created_date, "%Y-%m-%d")
                current_date = date.today().strftime("%Y-%m-%d")
                logger.info(f"Tentando deletar registros de {created_date} até {current_date}")
                if not bq_manager.delete_data_by_date(TABLE_NAME, created_date, current_date, logger):
                    logger.error("Falha ao deletar registros no BigQuery, abortando carga")
                    raise ValueError("Falha ao deletar registros no BigQuery")
            except ValueError as e:
                logger.error(f"Formato de created_date inválido ({created_date}): {str(e)}")
                raise ValueError(f"Formato de created_date inválido: {created_date}")

            # Filtrar issues com base em last_processed_timestamp
            last_processed_timestamp = bq_manager.get_last_processed_timestamp(TABLE_NAME, logger)
            logger.info(f"Último timestamp processado: {last_processed_timestamp}")
            if last_processed_timestamp:
                filtered_issues = []
                for issue in issues:
                    updated_at_str = issue.get('UpdatedAt')
                    if updated_at_str and updated_at_str != 'None':
                        try:
                            updated_at = datetime.strptime(updated_at_str, "%Y-%m-%dT%H:%M:%S.%f%z")
                            if updated_at > last_processed_timestamp:
                                filtered_issues.append(issue)
                        except ValueError as e:
                            logger.warning(f"Formato inválido de UpdatedAt '{updated_at_str}' para issue {issue.get('Key')}, ignorando: {str(e)}")
                            continue
                    else:
                        logger.debug(f"Issue {issue.get('Key')} sem UpdatedAt válido, ignorando")
                logger.info(f"{len(filtered_issues)} issues filtradas com UpdatedAt > {last_processed_timestamp}")
            else:
                logger.info("Nenhum timestamp anterior encontrado, carregando todas as issues filtradas por created_date")

        if not filtered_issues:
            logger.info("Nenhuma issue para carregar")
            # Ainda atualizar last_run_at mesmo sem issues
            last_run_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if not bq_manager.update_incremental_state(TABLE_NAME, None, last_run_at, logger):
                logger.error("Falha ao atualizar incremental_state (last_run_at)")
            return

        # Carregar os dados
        success = bq_manager.load_data(
            table_id=TABLE_NAME,
            data=filtered_issues,
            logger=logger,
            excluded_fields=[],
            write_mode=write_mode,
            job_config=job_config
        )
        
        if not success:
            logger.error("Falha ao carregar issues no BigQuery")
            raise ValueError("Falha ao carregar issues no BigQuery")

        # Atualizar incremental_state com o timestamp mais recente e last_run_at
        max_timestamp = None
        for issue in filtered_issues:
            updated_at_str = issue.get('UpdatedAt')
            if updated_at_str and updated_at_str != 'None':
                try:
                    updated_at = datetime.strptime(updated_at_str, "%Y-%m-%dT%H:%M:%S.%f%z")
                    if max_timestamp is None or updated_at > max_timestamp:
                        max_timestamp = updated_at
                except ValueError:
                    continue
        last_run_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        max_timestamp_str = max_timestamp.strftime("%Y-%m-%d %H:%M:%S.%f%z") if max_timestamp else None
        
        if not bq_manager.update_incremental_state(TABLE_NAME, max_timestamp_str, last_run_at, logger):
            logger.error("Falha ao atualizar incremental_state, mas dados foram carregados")
        else:
            logger.info(f"{len(filtered_issues)} issues enviadas para o BigQuery com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao enviar dados para o BigQuery: {str(e)}")
        raise

def is_project_active(jira_client, project_key: str) -> bool:
    try:
        test_jql = f"project={project_key}"
        jira_client.search_issues(test_jql, maxResults=1, fields="key")
        logger.info(f"Projeto {project_key} é ativo e acessível")
        return True
    except Exception as e:
        logger.warning(f"Projeto {project_key} não é acessível ou não existe: {str(e)}")
        return False

def run_jira(jql: Optional[str] = None, created_date: Optional[str] = None):
    try:
        logger.info(f"Iniciando execução do script Jira to BigQuery")
        logger.info(f"Parâmetros: jql={jql}, created_date={created_date}, modo={'incremental' if created_date else 'completo'}")
        
        load_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        jira_client = connect_to_jira()
        all_issues = []
        
        if jql:
            logger.info("Usando JQL fornecido pelo usuário")
            issues = extract_jira_issues(jira_client, jql)
            all_issues.extend(issues)
        else:
            logger.info("Nenhum JQL fornecido, listando projetos disponíveis")
            projects = jira_client.projects()
            logger.info(f"Encontrados {len(projects)} projetos: {[p.key for p in projects]}")
            
            if not projects:
                logger.warning("Nenhum projeto encontrado. Verifique permissões do usuário.")
                return
            
            active_projects = []
            for project in projects:
                if is_project_active(jira_client, project.key):
                    active_projects.append(project)
                else:
                    logger.info(f"Ignorando projeto {project.key} (inativo ou inacessível)")
            
            logger.info(f"Projetos ativos encontrados: {len(active_projects)} ({[p.key for p in active_projects]})")
            
            for project in active_projects:
                project_key = project.key
                project_jql = f"project={project_key}"
                if created_date:
                    try:
                        datetime.strptime(created_date, "%Y-%m-%d")
                        project_jql += f" AND created >= '{created_date}'"
                    except ValueError:
                        logger.error(f"Formato de created_date inválido ({created_date}), ignorando filtro de created")
                project_jql += " ORDER BY updated DESC"
                logger.info(f"Extraindo issues do projeto {project_key} com JQL: {project_jql}")
                try:
                    issues = extract_jira_issues(jira_client, project_jql)
                    logger.info(f"{len(issues)} issues extraídas do projeto {project_key}")
                    all_issues.extend(issues)
                except Exception as e:
                    logger.error(f"Falha ao extrair issues do projeto {project_key}: {str(e)}")
                    continue
        
        if not all_issues:
            logger.info("Nenhuma issue encontrada para os projetos ou JQL fornecido.")
            # Ainda atualizar last_run_at mesmo sem issues
            last_run_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if not bq_manager.update_incremental_state(TABLE_NAME, None, last_run_at, logger):
                logger.error("Falha ao atualizar incremental_state (last_run_at)")
            return
        
        logger.info(f"Total de {len(all_issues)} issues extraídas. Enviando para o BigQuery...")
        load_to_bigquery(all_issues, load_date, created_date)
        logger.info("Processo concluído com sucesso.")
    except Exception as e:
        logger.error(f"Erro na execução do script: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extrair issues do Jira e carregar no BigQuery com data de carga")
    parser.add_argument("--jql", type=str, default=None, help="Consulta JQL para filtrar issues (opcional)")
    parser.add_argument("--created_date", type=str, default=None, help="Data mínima de criação no formato YYYY-MM-DD (ex.: 2025-01-01)")
    args = parser.parse_args()
    
    if args.created_date:
        try:
            datetime.strptime(args.created_date, "%Y-%m-%d")
        except ValueError:
            logger.error("Formato de created_date inválido. Use YYYY-MM-DD (ex.: 2025-01-01)")
            sys.exit(1)
    
    run_jira(jql=args.jql, created_date=args.created_date)