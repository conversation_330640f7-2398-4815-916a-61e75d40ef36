# This code sample uses the 'requests' library:
# http://docs.python-requests.org
import requests
from requests.auth import HTTPBasicAuth
import json

url = "https://your-domain.atlassian.net/rest/api/3/search/jql"

auth = HTTPBasicAuth("<EMAIL>", "ATATT3xFfGF01hpk6GrBGX4Ti6IlwBDBmxWGN0wC_h3WHwUKkn65yCWl7rGlu2KTMfhDHQ75B3o-7p0shRWRZEv_yTzPBo_Lf5OqaqG48lEkVqlbr6Bwnqr234SH5x7AraWDdSTcfMAQXKlh5crpkbBqvvRBkhg0iV6f4F5uHQSX_pW0btyPKF4=42FC5C54")

headers = {
  "Accept": "application/json"
}

query = {
  'jql': 'project = HSP',
  'nextPageToken': '<string>',
  'maxResults': '{maxResults}',
  'fields': '{fields}',
  'expand': '<string>',
  'reconcileIssues': '{reconcileIssues}'
}

response = requests.request(
   "GET",
   url,
   headers=headers,
   params=query,
   auth=auth
)

print(json.dumps(json.loads(response.text), sort_keys=True, indent=4, separators=(",", ": ")))