import requests
import pandas as pd
import time

# Configuração da API
BASE_URL = "https://api.wts.chat"
ENDPOINT = "/chat/v1/session"
HEADERS = {
    "Authorization": "pn_BC1PYkWuQAGxq4KoaabB07SkCWDJbYm5sIjzrs71SY",
    "accept": "application/json",
    "X-Request-Source": "PactoGymBot"
}
PARAMS = {}
MAX_DURATION = 120
INCREMENTAL_PARAM = "CreatedAt.After"

def fetch_sessions():
    all_items = []
    start_time = time.time()
    url = f"{BASE_URL}{ENDPOINT}"
    
    while True:
        elapsed_time = time.time() - start_time
        if elapsed_time > MAX_DURATION:
            print("Tempo máximo atingido")
            break
        
        response = requests.get(url, headers=HEADERS, params=PARAMS)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code != 200:
            print("Erro na chamada à API")
            break
        
        data = response.json()
        items = data if isinstance(data, list) else data.get("data", [])
        all_items.extend(items)
        
        if not items:
            print("Nenhum item retornado nesta página")
            break
        
        if "next" not in data or not data["next"]:
            print("Nenhuma página adicional encontrada")
            break
        
        if INCREMENTAL_PARAM and items:
            last_item = items[-1]
            PARAMS[INCREMENTAL_PARAM] = last_item.get("createdAt", "")
            print(f"Próximo CreatedAt.After: {PARAMS[INCREMENTAL_PARAM]}")
    
    return pd.DataFrame(all_items)

dataset = fetch_sessions()