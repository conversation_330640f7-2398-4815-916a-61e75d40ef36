import json
import sys
from pathlib import Path
import requests
from urllib.parse import urljoin
from collections import Counter

current_dir = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(current_dir))

from src.api.api_client import EnhancedAPIClient
from src.api.bigquery_manager import BigQueryManager
from src.utils.logging_config import setup_logging

def merge_cards(cards, id_field="id"):
    """
    Mescla cards com o mesmo ID, tratando listas e dicionários adequadamente.
    """
    logger = setup_logging("panel_cards")
    merged_cards = {}
    id_counts = Counter(card.get(id_field) for card in cards if card.get(id_field))
    for card_id, count in id_counts.items():
        if count > 1:
            logger.info(f"Duplicata detectada: ID {card_id} aparece {count} vezes")

    for card in cards:
        card_id = card.get(id_field)
        if not card_id:
            logger.warning(f"Card sem {id_field}, ignorando: {json.dumps(card, ensure_ascii=False)}")
            continue
        if card_id not in merged_cards:
            merged_cards[card_id] = card.copy()
            logger.debug(f"Iniciando card ID {card_id}: {json.dumps(merged_cards[card_id], ensure_ascii=False)}")
        else:
            for key, value in card.items():
                if key == id_field:
                    continue
                existing_value = merged_cards[card_id].get(key)
                if value is not None:
                    if existing_value is None:
                        merged_cards[card_id][key] = value
                        logger.debug(f"Adicionando {key} para {card_id}: {value}")
                    elif isinstance(value, list) and isinstance(existing_value, list):
                        merged_cards[card_id][key] = list(dict.fromkeys(existing_value + value))
                        logger.debug(f"Mesclando lista para {card_id} em {key}: {merged_cards[card_id][key]}")
                    elif isinstance(value, dict) and isinstance(existing_value, dict):
                        merged_cards[card_id][key] = {**existing_value, **value}
                        logger.debug(f"Mesclando dicionário para {card_id} em {key}: {merged_cards[card_id][key]}")
                    elif key.startswith("contact_") and existing_value != value:
                        merged_cards[card_id][key] = value
                        logger.debug(f"Atualizando {key} para {card_id}: {value}")
                    elif key in ["jira", "tipo_do_jira", "sla_do_jira", "projeto_jira"] and existing_value != value:
                        logger.warning(f"Conflito em {card_id} para {key}: mantendo {existing_value}, ignorando {value}")
                    elif existing_value != value:
                        logger.warning(f"Conflito em {card_id} para {key}: mantendo {existing_value}, ignorando {value}")
                    else:
                        logger.debug(f"Valor idêntico para {card_id} em {key}: {value}")
                elif existing_value is None:
                    merged_cards[card_id][key] = None
                    logger.debug(f"Definindo {key} como None para {card_id}")
    return list(merged_cards.values())

def run_panel_cards(config_path: str = str(Path(__file__).parent.parent.parent / "config" / "config.json")):
    """
    Função para buscar dados de cards de panels via API e carregá-los no BigQuery.
    """
    logger = setup_logging("panel_cards")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)

        api_client = EnhancedAPIClient(
            base_url=CONFIG["api"]["base_url"],
            headers=CONFIG["api"]["headers"]
        )
        bq_manager = BigQueryManager(
            credentials_file=CONFIG["service_account_file"],
            project_id=CONFIG["project_id"]
        )

        if not bq_manager.initialize(logger):
            raise Exception("Falha na inicialização do BigQuery")

        panels_config = CONFIG["api"]["endpoints"]["panels"]
        panels_params = panels_config["params"].copy()

        panels_items, _ = api_client.fetch_paginated_data(
            endpoint=panels_config["path"],
            params=panels_params,
            max_items=panels_config.get("max_items"),
            max_duration=panels_config.get("max_duration"),
            logger=logger,
            use_pagination=True
        )

        panel_card_config = CONFIG["api"]["endpoints"].get("panel_card", {
            "path": "/crm/v1/panel/card",
            "params": {"pageSize": 100},
            "max_items": 100000,
            "max_duration": 60
        })
        excluded_fields = panel_card_config.get("excluded_fields", [])

        # Definir os campos desejados (excluindo os que vêm de customFields)
        desired_fields = [
            "id", "createdAt", "updatedAt", "companyId", "panelId", "panelTitle",
            "stepId", "stepTitle", "position", "title", "description", "key",
            "number", "dueDate", "sessionId", "monetaryAmount", "responsibleUser"
        ]

        # Campos a serem extraídos de customFields
        custom_fields = [
            "jira", "tipo_do_jira", "projeto_jira", "sla_do_jira",
            "link_do_ticket_e_not", "solu_o", "primeira_resposta", "ticket_17"
        ]

        # Mapeamento de prefixos para capturar campos dinâmicos
        custom_field_prefixes = {
            "jira": [
                "jira", "Jira", "jira_", "Jira_", "jira-", "Jira-",
                "jiraId", "jiraID", "JiraId", "JiraID"
            ],
            "tipo_do_jira": [
                "tipo_do_jira", "Tipo_do_Jira", "tipo_do_jira_", "Tipo_do_Jira_",
                "tipo-do-jira", "Tipo-do-Jira", "tipo-do-jira-", "Tipo-do-Jira-",
                "tipoDoJira", "TipoDoJira", "tipoDoJira_", "TipoDoJira_",
                "tipo", "Tipo", "typeJira", "TypeJira"
            ],
            "projeto_jira": [
                "projeto_jira", "Projeto_Jira", "projeto_jira_", "Projeto_Jira_",
                "projeto-jira", "Projeto-Jira", "projeto-jira-", "Projeto-Jira-",
                "projetoJira", "ProjetoJira", "projetoJira_", "ProjetoJira_",
                "projectJira", "ProjectJira", "projeto", "Projeto"
            ],
            "sla_do_jira": [
                "sla_do_jira", "Sla_do_Jira", "sla_do_jira_", "Sla_do_Jira_",
                "sla-do-jira", "Sla-do-Jira", "sla-do-jira-", "Sla-do-Jira-",
                "slaDoJira", "SlaDoJira", "slaDoJira_", "SlaDoJira_",
                "slaJira", "SlaJira", "sla", "Sla"
            ],
            "link_do_ticket_e_not": [
                "link_do_ticket_e_not", "linkDoTicketENot", "link-do-ticket-e-not",
                "linkTicketNot", "LinkTicketNot"
            ],
            "solu_o": [
                "solu_o", "solucao", "solução", "solucao_", "solução_",
                "solution", "Solution"
            ],
            "primeira_resposta": [
                "primeira_resposta", "primeiraResposta", "primeira-resposta",
                "firstResponse", "FirstResponse"
            ],
            "ticket_17": [
                "ticket_17", "ticket17", "ticket-17", "ticket_", "Ticket_",
                "ticketId", "TicketId"
            ]
        }

        card_items = []
        successful_requests = 0
        failed_requests = 0

        for item in panels_items:
            panel_id = item.get("id") or item.get("PanelId") or item.get("panelId")
            if not panel_id:
                logger.warning(f"Item sem ID")
                failed_requests += 1
                continue

            card_params = panel_card_config["params"].copy()
            card_params["PanelId"] = panel_id
            card_params["pageSize"] = card_params.get("pageSize", 100)

            try:
                cards, _ = api_client.fetch_paginated_data(
                    endpoint=panel_card_config["path"],
                    params=card_params,
                    max_items=panel_card_config.get("max_items"),
                    max_duration=panel_card_config.get("max_duration"),
                    logger=logger,
                    use_pagination=True
                )

                if cards:
                    for card in cards:
                        # Logar customFields bruto para diagnóstico
                        if "customFields" in card and isinstance(card["customFields"], dict):
                            logger.debug(f"customFields bruto para card {card.get('id')}: {json.dumps(card['customFields'], ensure_ascii=False)}")

                        # Criar um novo dicionário com os campos desejados
                        filtered_card = {field: card.get(field) for field in desired_fields if field in card}

                        # Transformar responsibleUser
                        if "responsibleUser" in card and isinstance(card["responsibleUser"], dict) and "name" in card["responsibleUser"]:
                            filtered_card["responsibleUser"] = card["responsibleUser"]["name"]
                        else:
                            filtered_card["responsibleUser"] = None

                        # Transformar contacts em colunas enumeradas
                        if "contacts" in card and isinstance(card["contacts"], list):
                            for i, contact in enumerate(card["contacts"], 1):
                                if isinstance(contact, dict) and "name" in contact:
                                    filtered_card[f"contact_{i}"] = contact["name"]
                                else:
                                    filtered_card[f"contact_{i}"] = None
                            filtered_card.pop("contacts", None)

                        # Extrair e agrupar campos de customFields com prefixos dinâmicos
                        if "customFields" in card and isinstance(card["customFields"], dict):
                            custom_fields_data = card["customFields"]
                            for desired_field in custom_fields:
                                value = None
                                found_key = None
                                # Procurar por qualquer chave que comece com os prefixos
                                for key in custom_fields_data:
                                    key_lower = key.lower()
                                    for prefix in custom_field_prefixes[desired_field]:
                                        if key_lower.startswith(prefix.lower()) and custom_fields_data[key] is not None:
                                            value = custom_fields_data[key]
                                            found_key = key
                                            break
                                    if value is not None:
                                        break
                                # Logar chave encontrada e valor selecionado
                                if found_key:
                                    logger.debug(f"Campo {desired_field} encontrado em {found_key}: {value}")
                                filtered_card[desired_field] = value
                            filtered_card.pop("customFields", None)

                        # Adicionar campos status e Painel
                        filtered_card["status"] = 331
                        filtered_card["Painel"] = 339

                        logger.debug(f"Card processado para PanelId {panel_id}, id {card.get('id')}: {json.dumps(filtered_card, ensure_ascii=False)}")
                        card_items.append(filtered_card)
                        successful_requests += 1

                    logger.info(f"Cards coletados para PanelId {panel_id}: {len(cards)} itens")
                else:
                    logger.warning(f"Nenhum card retornado para PanelId: {panel_id}")
                    failed_requests += 1

            except requests.exceptions.RequestException as e:
                logger.error(f"Erro ao buscar cards para PanelId {panel_id}: {str(e)}")
                failed_requests += 1
                continue

        logger.info(f"Total de cards antes da deduplicação: {len(card_items)}")
        card_items = merge_cards(card_items, id_field="id")
        logger.info(f"Total de cards após deduplicação: {len(card_items)}")

        if card_items:
            success = bq_manager.load_data(
                table_id="panel_cards",
                data=card_items,
                logger=logger,
                incremental_param=None,
                excluded_fields=excluded_fields,
                write_mode="WRITE_TRUNCATE"
            )
        else:
            success = False
            logger.warning("Nenhum dado para carregar no BigQuery.")

        logger.info(
            f"\n📊 Panel Cards Result:\n"
            f"- Total PanelIds: {len(panels_items)}\n"
            f"- Total Cards Coletados: {len(card_items)}\n"
            f"- Successful Requests: {successful_requests}\n"
            f"- Failed Requests: {failed_requests}\n"
            f"- Status: {'✅ SUCCESS' if success else '❌ FAILURE'}"
        )

    except Exception as e:
        logger.critical(f"Erro crítico: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    run_panel_cards()