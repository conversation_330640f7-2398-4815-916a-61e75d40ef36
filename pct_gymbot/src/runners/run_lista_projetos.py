from fastapi import FastAP<PERSON>, HTTPException
from jira import JIRA
from datetime import datetime
import os
import logging
from typing import List, Dict, Any
import json
import dotenv
from google.cloud import bigquery
from google.oauth2 import service_account
import re
from datetime import datetime
from typing import List, Dict, Optional

# Classe BigQueryManager (copiada do seu código)
class BigQueryManager:
    def __init__(self, credentials_file: str = r"C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json", 
                 project_id: str = "oamd-e-financeiro-pacto"):
        self.credentials_file = credentials_file
        self.project_id = project_id
        self.client = None

    def initialize(self, logger) -> bool:
        try:
            private_key = os.environ.get("KEY_GCP_DATALAKE")
            if private_key:
                logger.info("Usando KEY_GCP_DATALAKE como private_key no ambiente (GitLab)")
                credentials_info = {
                    "type": "service_account",
                    "project_id": self.project_id,
                    "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
                    "private_key": private_key.replace('\\n', '\n'),
                    "client_email": "<EMAIL>",
                    "client_id": "117321030275837789997",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
                }
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = bigquery.Client(project=self.project_id, credentials=credentials)
                logger.info("✅ BigQuery connection established usando private_key do GitLab")
            else:
                logger.info(f"Tentando usar arquivo local: {self.credentials_file}")
                if os.path.exists(self.credentials_file):
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.credentials_file
                    self.client = bigquery.Client(project=self.project_id)
                    logger.info(f"Usando arquivo local: {self.credentials_file}")
                else:
                    logger.error(f"Arquivo de credencial não encontrado: {self.credentials_file}")
                    raise FileNotFoundError(f"Credentials file not found at {self.credentials_file}")

            logger.info("✅ BigQuery connection established")
            return True
        except Exception as e:
            logger.error(f"❌ Initialization failed: {str(e)}", exc_info=True)
            return False

    def _sanitize_field_name(self, field_name: str) -> str:
        import unicodedata
        field_name = unicodedata.normalize('NFKD', field_name).encode('ASCII', 'ignore').decode('ASCII')
        sanitized = re.sub(r'[^0-9a-zA-Z_]', '_', field_name.lower())
        sanitized = re.sub(r'_+', '_', sanitized).strip('_')
        return sanitized if sanitized else 'unnamed_field'

    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_', excluded_fields: List[str] = None) -> List[Dict]:
        if excluded_fields is None:
            excluded_fields = []
        
        filtered_d = {k: v for k, v in d.items() if k not in excluded_fields}
        base_items = {k: v for k, v in filtered_d.items() if not isinstance(v, (dict, list))}
        nested_items = {k: v for k, v in filtered_d.items() if isinstance(v, (dict, list))}
        
        flattened = []
        if not nested_items:
            flattened.append(base_items)
            return flattened

        has_list = False
        for key, value in nested_items.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key
            if isinstance(value, list) and value:
                has_list = True
                for i, subitem in enumerate(value):
                    if isinstance(subitem, dict):
                        sub_flattened = self._flatten_dict(subitem, new_key, sep=sep, excluded_fields=excluded_fields)
                        for sub in sub_flattened:
                            flattened.append({**base_items, **sub})
                    else:
                        flattened.append({**base_items, f"{new_key}_{i}": subitem})
            elif isinstance(value, dict):
                sub_flattened = self._flatten_dict(value, new_key, sep=sep, excluded_fields=excluded_fields)
                for sub in sub_flattened:
                    flattened.append({**base_items, **sub})

        if not has_list and not flattened:
            flattened.append(base_items)
        
        return flattened if flattened else [base_items]

    def _sanitize_data(self, data: List[Dict], logger, excluded_fields: List[str] = None) -> List[Dict]:
        sanitized_data = []
        for item in data:
            flattened_items = self._flatten_dict(item, excluded_fields=excluded_fields)
            for flattened_item in flattened_items:
                sanitized_item = {}
                for key, value in flattened_item.items():
                    sanitized_key = self._sanitize_field_name(key)
                    if sanitized_key == "timeservice" and isinstance(value, str):
                        try:
                            h, m, s = map(int, value.split(':'))
                            if 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59:
                                sanitized_item[sanitized_key] = value
                            else:
                                if h > 23 and m <= 59 and s <= 59:
                                    corrected_h = int(str(h)[:2]) if len(str(h)) >= 2 else h % 24
                                    if 0 <= corrected_h <= 23:
                                        corrected_value = f"{corrected_h:02d}:{m:02d}:{s:02d}"
                                        logger.debug(f"Valor inválido de timeService '{value}' corrigido pra '{corrected_value}'")
                                        sanitized_item[sanitized_key] = corrected_value
                                    else:
                                        logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None (não corrigível)")
                                        sanitized_item[sanitized_key] = None
                                else:
                                    logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None")
                                    sanitized_item[sanitized_key] = None
                        except (ValueError, TypeError):
                            logger.debug(f"Formato inválido de timeService '{value}' corrigido pra None")
                            sanitized_item[sanitized_key] = None
                    else:
                        sanitized_item[sanitized_key] = value
                sanitized_data.append(sanitized_item)
                logger.debug(f"Dado sanitizado: {json.dumps(sanitized_item, ensure_ascii=False)}")
        logger.info(f"Total de itens sanitizados: {len(sanitized_data)}")
        return sanitized_data

    def get_last_processed_timestamp(self, table_id: str, logger) -> Optional[datetime]:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return None
        query = f"""
        SELECT last_processed_timestamp
        FROM `{self.project_id}.GYMBOT.incremental_state`
        WHERE table_name = '{table_id}'
        LIMIT 1
        """
        logger.debug(f"Consultando último timestamp pra {table_id} com query: {query}")
        try:
            query_job = self.client.query(query)
            result = query_job.result()
            for row in result:
                logger.info(f"Timestamp encontrado pra {table_id}: {row.last_processed_timestamp}")
                return row.last_processed_timestamp
            logger.info(f"Nenhum estado incremental encontrado pra tabela {table_id}")
            return None
        except Exception as e:
            logger.error(f"Falha ao consultar último timestamp pra {table_id}: {str(e)}", exc_info=True)
            return None

    def update_incremental_state(self, table_id: str, max_timestamp: str, logger) -> bool:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return False
        query = f"""
        MERGE `{self.project_id}.GYMBOT.incremental_state` AS target
        USING (SELECT '{table_id}' AS table_name, TIMESTAMP('{max_timestamp}') AS last_processed_timestamp, CURRENT_TIMESTAMP() AS last_run_at) AS source
        ON target.table_name = source.table_name
        WHEN MATCHED THEN
          UPDATE SET last_processed_timestamp = source.last_processed_timestamp, last_run_at = source.last_run_at
        WHEN NOT MATCHED THEN
          INSERT (table_name, last_processed_timestamp, last_run_at)
          VALUES (source.table_name, source.last_processed_timestamp, source.last_run_at)
        """
        logger.debug(f"Query de atualização de estado pra {table_id}: {query}")
        try:
            query_job = self.client.query(query)
            query_job.result()
            logger.info(f"Estado incremental atualizado com sucesso pra tabela {table_id}: {max_timestamp}")
            return True
        except Exception as e:
            logger.error(f"Falha ao atualizar estado incremental pra {table_id}: {str(e)}", exc_info=True)
            return False

    def load_data(self, table_id: str, data: List[Dict], logger, incremental_param: Optional[str] = None, 
                  excluded_fields: List[str] = None, write_mode: str = "DEFAULT", job_config: Optional[Dict] = None) -> bool:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            raise ValueError("BigQuery client not initialized")

        if not data:
            logger.info(f"Nenhum dado pra carregar na tabela {table_id}")
            return True

        logger.debug(f"Dados brutos recebidos: {json.dumps(data, ensure_ascii=False)}")
        logger.info(f"Total de itens recebidos da API pra {table_id}: {len(data)}")
        logger.info(f"Parâmetro incremental configurado: {incremental_param}, Modo de escrita: {write_mode}")

        if incremental_param:
            last_timestamp = self.get_last_processed_timestamp(table_id, logger)
            if last_timestamp:
                logger.info(f"Último timestamp registrado: {last_timestamp}")
                new_data = []
                timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                for item in data:
                    timestamp_str = item.get(timestamp_field, "1970-01-01T00:00:00Z")
                    if '+' not in timestamp_str and '-' not in timestamp_str.split('T')[1]:
                        timestamp_str = timestamp_str.replace("Z", "+00:00")
                    try:
                        item_timestamp = datetime.fromisoformat(timestamp_str)
                        logger.debug(f"Comparando {timestamp_field} {item_timestamp} com last_timestamp {last_timestamp}")
                        if item_timestamp > last_timestamp:
                            new_data.append(item)
                    except ValueError as e:
                        logger.warning(f"Timestamp inválido em '{timestamp_field}': {timestamp_str}. Usando valor padrão. Erro: {str(e)}")
                        item_timestamp = datetime.fromisoformat("1970-01-01T00:00:00+00:00")
                        if item_timestamp > last_timestamp:
                            new_data.append(item)
                data_to_load = new_data
                logger.info(f"Filtrados {len(new_data)} itens novos desde o último timestamp {last_timestamp}")
            else:
                data_to_load = data
                logger.info(f"Nenhum timestamp anterior encontrado, carregando todos os {len(data)} itens")
        else:
            data_to_load = data
            logger.info(f"Carregando todos os {len(data)} itens (sem filtro incremental)")

        if not data_to_load:
            logger.info("Nenhum dado novo após filtragem incremental, pulando carga")
            if incremental_param and data:
                try:
                    timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                    max_timestamp = max(item.get(timestamp_field, "1970-01-01T00:00:00Z") for item in data)
                    logger.info(f"Nenhum dado novo, mas atualizando estado com max_timestamp dos dados brutos: {max_timestamp}")
                    self.update_incremental_state(table_id, max_timestamp, logger)
                except Exception as e:
                    logger.error(f"Erro ao calcular max_timestamp dos dados brutos pra {table_id}: {str(e)}", exc_info=True)
            return True

        sanitized_data = self._sanitize_data(data_to_load, logger, excluded_fields=excluded_fields)
        if not sanitized_data:
            logger.error("Nenhum dado sanitizado pra carregar após sanitização")
            return False

        logger.info(f"Total de itens sanitizados pra carga: {len(sanitized_data)}")
        dataset_ref = self.client.dataset("GYMBOT")
        table_ref = dataset_ref.table(table_id)

        load_job_config = bigquery.LoadJobConfig()
        if write_mode == "WRITE_TRUNCATE":
            logger.info(f"Tabela {table_id} será truncada e recarregada com WRITE_TRUNCATE.")
            load_job_config.write_disposition = "WRITE_TRUNCATE"
            load_job_config.autodetect = True
        else:
            try:
                table = self.client.get_table(table_ref)
                logger.info(f"Tabela {table_id} já existe, usando WRITE_APPEND com esquema existente.")
                load_job_config.write_disposition = "WRITE_APPEND"
            except Exception:
                logger.info(f"Tabela {table_id} não existe, criando com WRITE_TRUNCATE e autodetect.")
                load_job_config.write_disposition = "WRITE_TRUNCATE"
                load_job_config.autodetect = True

        if job_config:
            logger.info(f"Aplicando configurações personalizadas de job_config: {job_config}")
            for key, value in job_config.items():
                setattr(load_job_config, key, value)

        try:
            logger.debug(f"Enviando dados pro BigQuery: {json.dumps(sanitized_data, ensure_ascii=False)}")
            job = self.client.load_table_from_json(sanitized_data, table_ref, job_config=load_job_config)
            job.result()
            logger.info(f"Dados carregados com sucesso na tabela {table_id} ({len(sanitized_data)} itens)")
            
            if incremental_param and data:
                try:
                    timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                    max_timestamp = max(item.get(timestamp_field, "1970-01-01T00:00:00Z") for item in data)
                    logger.info(f"Tentando atualizar estado incremental pra {table_id} com max_timestamp: {max_timestamp}")
                    if not self.update_incremental_state(table_id, max_timestamp, logger):
                        logger.error(f"Falha ao atualizar estado incremental pra {table_id}, mas dados foram carregados")
                    else:
                        logger.info(f"Estado incremental atualizado com sucesso pra {table_id} após carga")
                except Exception as e:
                    logger.error(f"Erro ao calcular max_timestamp pra {table_id}: {str(e)}", exc_info=True)
            return True
        except Exception as e:
            logger.error(f"Falha ao carregar dados na tabela {table_id}: {str(e)}", exc_info=True)
            return False

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Jira to BigQuery API")

# Carrega variáveis de ambiente
dotenv.load_dotenv()

# Configurações do Jira
JIRA_URL = os.getenv("JIRA_URL", "https://pactosolucoes.atlassian.net")
JIRA_USERNAME = os.getenv("JIRA_USERNAME", "<EMAIL>")
JIRA_API_TOKEN = os.getenv("JIRA_API_TOKEN", "ATATT3xFfGF01hpk6GrBGX4Ti6IlwBDBmxWGN0wC_h3WHwUKkn65yCWl7rGlu2KTMfhDHQ75B3o-7p0shRWRZEv_yTzPBo_Lf5OqaqG48lEkVqlbr6Bwnqr234SH5x7AraWDdSTcfMAQXKlh5crpkbBqvvRBkhg0iV6f4F5uHQSX_pW0btyPKF4=42FC5C54")

# Configurações do BigQuery
PROJECT_ID = os.getenv("BQ_PROJECT_ID", "oamd-e-financeiro-pacto")
CREDENTIALS_FILE = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", r"C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json")
DATASET_ID = os.getenv("BQ_DATASET_ID", "GYMBOT")
TABLE_ID = os.getenv("BQ_TABLE_ID", "jira_issues")

# Inicialização do BigQueryManager
bq_manager = BigQueryManager(credentials_file=CREDENTIALS_FILE, project_id=PROJECT_ID)

# Função para conectar ao Jira
def connect_to_jira():
    try:
        jira_client = JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USERNAME, JIRA_API_TOKEN)
        )
        return jira_client
    except Exception as e:
        logger.error(f"Erro ao conectar ao Jira: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro ao conectar ao Jira: {str(e)}")

# Função para extrair dados do Jira
def extract_jira_issues(jira_client, jql_query: str = "order by created DESC"):
    issues = []
    try:
        start_at = 0
        max_results = 100
        while True:
            batch = jira_client.search_issues(
                jql_query,
                startAt=start_at,
                maxResults=max_results,
                fields="*all"
            )
            if not batch:
                break
            issues.extend(batch)
            start_at += max_results
            logger.info(f"Extraindo issues: {len(issues)} issues coletadas até agora.")
        
        processed_issues = []
        for issue in issues:
            issue_data = {
                "issue_key": issue.key,
                "summary": issue.fields.summary,
                "project_key": issue.fields.project.key,
                "project_name": issue.fields.project.name,
                "assignee": issue.fields.assignee.displayName if issue.fields.assignee else None,
                "reporter": issue.fields.reporter.displayName if issue.fields.reporter else None,
                "status": issue.fields.status.name,
                "createdAt": issue.fields.created,  # Renomeado para createdAt para compatibilidade com incremental_param
                "updatedAt": issue.fields.updated,  # Renomeado para updatedAt para compatibilidade com incremental_param
                "due_date": issue.fields.duedate,
                "priority": issue.fields.priority.name if issue.fields.priority else None,
                "labels": issue.fields.labels,
                "fields": json.dumps(issue.raw['fields'])
            }
            processed_issues.append(issue_data)
        
        return processed_issues
    except Exception as e:
        logger.error(f"Erro ao extrair issues do Jira: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro ao extrair issues: {str(e)}")

# Função para carregar dados no BigQuery usando BigQueryManager
def load_to_bigquery(issues: List[Dict[str, Any]]):
    try:
        if not bq_manager.client:
            if not bq_manager.initialize(logger):
                logger.error("Falha ao inicializar BigQueryManager")
                raise HTTPException(status_code=500, detail="Falha ao inicializar BigQueryManager")
        
        job_config = {
            "autodetect": True,
            "write_disposition": "WRITE_APPEND",
            "schema_update_options": [bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION]
        }
        
        success = bq_manager.load_data(
            table_id=TABLE_ID,
            data=issues,
            logger=logger,
            incremental_param="updatedAt",  # Usa updatedAt para carga incremental
            excluded_fields=["fields"],  # Exclui o campo JSON bruto, já que os principais campos já estão extraídos
            write_mode="DEFAULT",
            job_config=job_config
        )
        
        if success:
            logger.info(f"{len(issues)} issues enviadas para o BigQuery com sucesso.")
        else:
            logger.error("Falha ao carregar issues no BigQuery")
            raise HTTPException(status_code=500, detail="Falha ao carregar issues no BigQuery")
    except Exception as e:
        logger.error(f"Erro ao enviar dados para o BigQuery: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro ao enviar dados para o BigQuery: {str(e)}")

# Endpoint da API
@app.get("/extract-jira-to-bigquery")
async def extract_jira_to_bigquery(jql: str = "order by created DESC"):
    try:
        # Inicializa o BigQueryManager
        if not bq_manager.client:
            if not bq_manager.initialize(logger):
                raise HTTPException(status_code=500, detail="Falha ao inicializar BigQueryManager")
        
        # Conecta ao Jira
        jira_client = connect_to_jira()
        
        # Extrai as issues
        issues = extract_jira_issues(jira_client, jql)
        
        if not issues:
            return {"message": "Nenhuma issue encontrada para o JQL fornecido."}
        
        # Envia para o BigQuery
        load_to_bigquery(issues)
        
        return {"message": f"{len(issues)} issues extraídas e enviadas para o BigQuery com sucesso."}
    except Exception as e:
        logger.error(f"Erro na execução da API: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Executa a API
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)