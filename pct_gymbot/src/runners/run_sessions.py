# Autor: <PERSON>  
# Cientista de Dados | [17/04/2025]  
# Descrição: Script para buscar dados do endpoint sessions com suporte incremental e logging padronizado.

import json
import sys
from pathlib import Path

current_dir = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(current_dir))

from src.api.api_client import EnhancedAPIClient
from src.api.bigquery_manager import BigQueryManager
from src.utils.logging_config import setup_logging

def run_sessions(config_path: str = str(Path(__file__).parent.parent.parent / "config" / "config.json")):
    logger = setup_logging("sessions")

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)

        api_client = EnhancedAPIClient(
            base_url=CONFIG["api"]["base_url"],
            headers=CONFIG["api"]["headers"]
        )
        bq_manager = BigQueryManager(
            credentials_file=CONFIG["service_account_file"],
            project_id=CONFIG["project_id"]
        )

        if not bq_manager.initialize(logger):
            raise Exception("BigQuery initialization failed")

        config = CONFIG["api"]["endpoints"]["sessions"]
        params = config["params"].copy()
        incremental_param = config.get("incremental_param")
        excluded_fields = config.get("excluded_fields", [])

        if incremental_param:
            last_timestamp = bq_manager.get_last_processed_timestamp("sessions", logger)
            if last_timestamp:
                timestamp_str = last_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ")
                params[incremental_param] = timestamp_str
                logger.info(f"📅 Filtro incremental aplicado: {incremental_param} = {timestamp_str}")

        # Buscar os dados via API
        items, stats = api_client.fetch_paginated_data(
            endpoint=config["path"],
            params=params,
            max_items=config.get("max_items"),
            max_duration=config.get("max_duration"),
            logger=None,  # Desativa logs internos de paginação
            use_pagination=True
        )

        # Carregar no BigQuery
        success = bq_manager.load_data(
            table_id="sessions",
            data=items,
            logger=logger,
            incremental_param=incremental_param,
            excluded_fields=excluded_fields
        )

        # Log final resumido
        logger.info(
            f"\n📊 Sessions Result:\n"
            f"- Total de registros: {len(items)}\n"
            f"- Páginas processadas: {stats['successful_pages']}\n"
            f"- Status: {'✅ SUCCESS' if success else '❌ FAILURE'}"
        )

    except Exception as e:
        logger.critical(f"Erro crítico: {str(e)}", exc_info=True)

if __name__ == "__main__":
    run_sessions()
