# Autor: <PERSON>  
# Cientista de Dados | [04/04/2025]  
# Descrição: Configura o sistema de logging pra gerar logs detalhados em arquivos.

import logging
from pathlib import Path

def setup_logging(name: str):
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # Handler pra console apenas
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formato dos logs
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)

    # Adicionar apenas o handler de console
    logger.addHandler(console_handler)

    return logger