# Autor: <PERSON>  
# Cientista de Dados | [04/04/2025]  
# Descrição: Classe que gerencia a conexão com o BigQuery, carrega dados e mantém o estado incremental.  

from google.cloud import bigquery
from google.oauth2 import service_account
import os
import json
import re
from datetime import datetime
from typing import List, Dict, Optional
from src.utils.logging_config import setup_logging  # Importação adicionada

class BigQueryManager:
    def __init__(self, credentials_file: str = r"C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json", 
                 project_id: str = "oamd-e-financeiro-pacto"):
        self.credentials_file = credentials_file
        self.project_id = project_id
        self.client = None

    def initialize(self, logger) -> bool:
        try:
            private_key = os.environ.get("KEY_GCP_DATALAKE")
            if private_key:
                logger.info("<PERSON>ando KEY_GCP_DATALAKE como private_key no ambiente (GitLab)")
                credentials_info = {
                    "type": "service_account",
                    "project_id": self.project_id,
                    "private_key_id": "bda984cf2b68a4dd37caec01f8eaa17271c7c5ed",
                    "private_key": private_key.replace('\\n', '\n'),
                    "client_email": "<EMAIL>",
                    "client_id": "117321030275837789997",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-bigquery-oamd-financei%40oamd-e-financeiro-pacto.iam.gserviceaccount.com"
                }
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = bigquery.Client(project=self.project_id, credentials=credentials)
                logger.info("✅ BigQuery connection established usando private_key do GitLab")
            else:
                logger.info(f"Tentando usar arquivo local: {self.credentials_file}")
                if os.path.exists(self.credentials_file):
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.credentials_file
                    self.client = bigquery.Client(project=self.project_id)
                    logger.info(f"Usando arquivo local: {self.credentials_file}")
                else:
                    logger.error(f"Arquivo de credencial não encontrado: {self.credentials_file}")
                    raise FileNotFoundError(f"Credentials file not found at {self.credentials_file}")

            logger.info("✅ BigQuery connection established")
            return True
        except Exception as e:
            logger.error(f"❌ Initialization failed: {str(e)}", exc_info=True)
            return False

    def _sanitize_field_name(self, field_name: str) -> str:
        import unicodedata
        field_name = unicodedata.normalize('NFKD', field_name).encode('ASCII', 'ignore').decode('ASCII')
        sanitized = re.sub(r'[^0-9a-zA-Z_]', '_', field_name.lower())
        sanitized = re.sub(r'_+', '_', sanitized).strip('_')
        return sanitized if sanitized else 'unnamed_field'

    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_', excluded_fields: List[str] = None) -> Dict:
        """
        Aplana um dicionário, convertendo listas em strings JSON e achatando dicionários de nível superior.
        
        Args:
            d (Dict): Dicionário a ser achatado.
            parent_key (str): Chave pai para prefixo.
            sep (str): Separador para chaves aninhadas.
            excluded_fields (List[str]): Campos a serem excluídos.
        
        Returns:
            Dict: Dicionário achatado (um único registro por card).
        """
        if excluded_fields is None:
            excluded_fields = []
        
        logger = setup_logging("bigquery_manager")
        filtered_d = {k: v for k, v in d.items() if k not in excluded_fields}
        flattened = {}
        
        for key, value in filtered_d.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key
            if isinstance(value, dict):
                sub_flattened = self._flatten_dict(value, new_key, sep=sep, excluded_fields=excluded_fields)
                flattened.update(sub_flattened)
            elif isinstance(value, list):
                flattened[new_key] = json.dumps(value, ensure_ascii=False)
                logger.debug(f"Convertendo lista {new_key} para JSON: {flattened[new_key]}")
            else:
                flattened[new_key] = value
        
        logger.debug(f"Dicionário achatado para id {d.get('id')}: {json.dumps(flattened, ensure_ascii=False)}")
        return flattened

    def _sanitize_data(self, data: List[Dict], logger, excluded_fields: List[str] = None) -> List[Dict]:
        if excluded_fields is None:
            excluded_fields = []
        
        sanitized_data = []
        for item in data:
            flattened_item = self._flatten_dict(item, excluded_fields=excluded_fields)
            sanitized_item = {}
            for key, value in flattened_item.items():
                sanitized_key = self._sanitize_field_name(key)
                if sanitized_key == "timeservice" and isinstance(value, str):
                    try:
                        h, m, s = map(int, value.split(':'))
                        if 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59:
                            sanitized_item[sanitized_key] = value
                        else:
                            if h > 23 and m <= 59 and s <= 59:
                                corrected_h = int(str(h)[:2]) if len(str(h)) >= 2 else h % 24
                                if 0 <= corrected_h <= 23:
                                    corrected_value = f"{corrected_h:02d}:{m:02d}:{s:02d}"
                                    logger.debug(f"Valor inválido de timeService '{value}' corrigido pra '{corrected_value}'")
                                    sanitized_item[sanitized_key] = corrected_value
                                else:
                                    logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None")
                                    sanitized_item[sanitized_key] = None
                            else:
                                logger.debug(f"Valor inválido de timeService '{value}' corrigido pra None")
                                sanitized_item[sanitized_key] = None
                    except (ValueError, TypeError):
                        logger.debug(f"Formato inválido de timeService '{value}' corrigido pra None")
                        sanitized_item[sanitized_key] = None
                else:
                    sanitized_item[sanitized_key] = value
            sanitized_data.append(sanitized_item)
            logger.debug(f"Dado sanitizado para id {item.get('id')}: {json.dumps(sanitized_item, ensure_ascii=False)}")
        
        logger.info(f"Total de itens sanitizados: {len(sanitized_data)}")
        return sanitized_data

    def get_last_processed_timestamp(self, table_id: str, logger) -> Optional[datetime]:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return None
        query = f"""
        SELECT last_processed_timestamp
        FROM `{self.project_id}.GYMBOT.incremental_state`
        WHERE table_name = '{table_id}'
        LIMIT 1
        """
        logger.debug(f"Consultando último timestamp pra {table_id} com query: {query}")
        try:
            query_job = self.client.query(query)
            result = query_job.result()
            for row in result:
                logger.info(f"Timestamp encontrado pra {table_id}: {row.last_processed_timestamp}")
                return row.last_processed_timestamp
            logger.info(f"Nenhum estado incremental encontrado pra tabela {table_id}")
            return None
        except Exception as e:
            logger.error(f"Falha ao consultar último timestamp pra {table_id}: {str(e)}", exc_info=True)
            return None

    def update_incremental_state(self, table_id: str, max_timestamp: str, logger) -> bool:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            return False
        query = f"""
        MERGE `{self.project_id}.GYMBOT.incremental_state` AS target
        USING (SELECT '{table_id}' AS table_name, TIMESTAMP('{max_timestamp}') AS last_processed_timestamp, CURRENT_TIMESTAMP() AS last_run_at) AS source
        ON target.table_name = source.table_name
        WHEN MATCHED THEN
          UPDATE SET last_processed_timestamp = source.last_processed_timestamp, last_run_at = source.last_run_at
        WHEN NOT MATCHED THEN
          INSERT (table_name, last_processed_timestamp, last_run_at)
          VALUES (source.table_name, source.last_processed_timestamp, source.last_run_at)
        """
        logger.debug(f"Query de atualização de estado pra {table_id}: {query}")
        try:
            query_job = self.client.query(query)
            query_job.result()
            logger.info(f"Estado incremental atualizado com sucesso pra tabela {table_id}: {max_timestamp}")
            return True
        except Exception as e:
            logger.error(f"Falha ao atualizar estado incremental pra {table_id}: {str(e)}", exc_info=True)
            return False

    def load_data(self, table_id: str, data: List[Dict], logger, incremental_param: Optional[str] = None, 
                  excluded_fields: List[str] = None, write_mode: str = "DEFAULT", 
                  job_config: Optional[Dict] = None) -> bool:
        if not self.client:
            logger.error("BigQuery client não inicializado")
            raise ValueError("BigQuery client not initialized")

        if not data:
            logger.info(f"Nenhum dado pra carregar na tabela {table_id}")
            return True

        logger.info(f"Total de itens recebidos da API pra {table_id}: {len(data)}")
        logger.info(f"Parâmetro incremental configurado: {incremental_param}, Modo de escrita: {write_mode}")

        if incremental_param:
            last_timestamp = self.get_last_processed_timestamp(table_id, logger)
            if last_timestamp:
                logger.info(f"Último timestamp registrado: {last_timestamp}")
                new_data = []
                timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                for item in data:
                    timestamp_str = item.get(timestamp_field, "1970-01-01T00:00:00Z")
                    if '+' not in timestamp_str and '-' not in timestamp_str.split('T')[1]:
                        timestamp_str = timestamp_str.replace("Z", "+00:00")
                    try:
                        item_timestamp = datetime.fromisoformat(timestamp_str)
                        logger.debug(f"Comparando {timestamp_field} {item_timestamp} com last_timestamp {last_timestamp}")
                        if item_timestamp > last_timestamp:
                            new_data.append(item)
                    except ValueError as e:
                        logger.warning(f"Timestamp inválido em '{timestamp_field}': {timestamp_str}. Usando valor padrão. Erro: {str(e)}")
                        item_timestamp = datetime.fromisoformat("1970-01-01T00:00:00+00:00")
                        if item_timestamp > last_timestamp:
                            new_data.append(item)
                data_to_load = new_data
                logger.info(f"Filtrados {len(new_data)} itens novos desde o último timestamp {last_timestamp}")
            else:
                data_to_load = data
                logger.info(f"Nenhum timestamp anterior encontrado, carregando todos os {len(data)} itens")
        else:
            data_to_load = data
            logger.info(f"Carregando todos os {len(data)} itens (sem filtro incremental)")

        if not data_to_load:
            logger.info("Nenhum dado novo após filtragem incremental, pulando carga")
            if incremental_param and data:
                try:
                    timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                    max_timestamp = max(item.get(timestamp_field, "1970-01-01T00:00:00Z") for item in data)
                    logger.info(f"Nenhum dado novo, mas atualizando estado com max_timestamp dos dados brutos: {max_timestamp}")
                    self.update_incremental_state(table_id, max_timestamp, logger)
                except Exception as e:
                    logger.error(f"Erro ao calcular max_timestamp dos dados brutos pra {table_id}: {str(e)}", exc_info=True)
            return True

        sanitized_data = self._sanitize_data(data_to_load, logger, excluded_fields=excluded_fields)
        if not sanitized_data:
            logger.error("Nenhum dado sanitizado pra carregar após sanitização")
            return False

        logger.info(f"Total de itens sanitizados pra carga: {len(sanitized_data)}")
        dataset_ref = self.client.dataset("GYMBOT")
        table_ref = dataset_ref.table(table_id)

        load_job_config = bigquery.LoadJobConfig()
        if write_mode == "WRITE_TRUNCATE":
            logger.info(f"Tabela {table_id} será truncada e recarregada com WRITE_TRUNCATE.")
            load_job_config.write_disposition = "WRITE_TRUNCATE"
            load_job_config.autodetect = True
        else:
            try:
                table = self.client.get_table(table_ref)
                logger.info(f"Tabela {table_id} já existe, usando WRITE_APPEND com esquema existente.")
                load_job_config.write_disposition = "WRITE_APPEND"
            except Exception:
                logger.info(f"Tabela {table_id} não existe, criando com WRITE_TRUNCATE e autodetect.")
                load_job_config.write_disposition = "WRITE_TRUNCATE"
                load_job_config.autodetect = True

        if job_config:
            logger.info(f"Aplicando configurações personalizadas de job_config: {job_config}")
            for key, value in job_config.items():
                setattr(load_job_config, key, value)

        try:
            job = self.client.load_table_from_json(sanitized_data, table_ref, job_config=load_job_config)
            job.result()
            logger.info(f"Dados carregados com sucesso na tabela {table_id} ({len(sanitized_data)} itens)")
            
            if incremental_param and data:
                try:
                    timestamp_field = "createdAt" if "CreatedAt" in incremental_param else "updatedAt"
                    max_timestamp = max(item.get(timestamp_field, "1970-01-01T00:00:00Z") for item in data)
                    logger.info(f"Tentando atualizar estado incremental pra {table_id} com max_timestamp: {max_timestamp}")
                    if not self.update_incremental_state(table_id, max_timestamp, logger):
                        logger.error(f"Falha ao atualizar estado incremental pra {table_id}, mas dados foram carregados")
                    else:
                        logger.info(f"Estado incremental atualizado com sucesso pra {table_id} após carga")
                except Exception as e:
                    logger.error(f"Erro ao calcular max_timestamp pra {table_id}: {str(e)}", exc_info=True)
            return True
        except Exception as e:
            logger.error(f"Falha ao carregar dados na tabela {table_id}: {str(e)}", exc_info=True)
            return False