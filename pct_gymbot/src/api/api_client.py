# Autor: <PERSON>  
# Cientista de Dados | [04/04/2025]  
# Descrição: Classe responsável por fazer requisições HTTP à API WTS Chat, com suporte a paginação e retries.

import requests
import logging
from typing import Dict, List, Tuple
from urllib.parse import urljoin
from datetime import datetime, timedelta
import time

class EnhancedAPIClient:
    def __init__(self, base_url: str, headers: Dict[str, str]):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update(headers)
        
        adapter = requests.adapters.HTTPAdapter(
            max_retries=3,
            pool_connections=10,
            pool_maxsize=100
        )
        self.session.mount('https://', adapter)
        self.current_page_size = 100

    def fetch_paginated_data(
        self,
        endpoint: str,
        params: Dict = None,
        max_items: int = None,
        max_duration: int = None,
        logger=None,
        use_pagination: bool = True
    ) -> <PERSON><PERSON>[List[Dict], Dict]:
        if logger is None:
            logger = logging.getLogger(__name__)

        params = params or {}
        all_items = []
        seen_ids = set()
        current_page = 1
        start_time = datetime.now()
        stats = {
            "successful_pages": 0,
            "total_items": 0,
            "duplicate_items": 0
        }

        # Logar se há filtro incremental nos params
        for key in params:
            if "After" in key:  # Ex.: CreatedAt.After ou UpdatedAt.After
                logger.info(f"Filtro incremental detectado: {key}={params[key]}")

        try:
            while True:
                if max_items and len(all_items) >= max_items:
                    logger.info(f"Limite de {max_items} itens atingido.")
                    break
                if max_duration and (datetime.now() - start_time) > timedelta(minutes=max_duration):
                    logger.info(f"Limite de tempo de {max_duration} minutos atingido.")
                    break

                request_params = params.copy()
                if use_pagination:
                    request_params.update({"pageNumber": current_page, "pageSize": self.current_page_size})
                
                url = urljoin(self.base_url, endpoint)
                logger.debug(f"URL gerada: {url} com params: {request_params}")
                response = self.session.get(
                    url,
                    params=request_params,
                    timeout=50
                )
                
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    logger.warning(f"Rate limit atingido. Aguardando {retry_after}s...")
                    time.sleep(retry_after)
                    continue
                elif response.status_code >= 400:
                    logger.error(f"Erro HTTP {response.status_code}: {response.text}")
                    raise requests.exceptions.HTTPError(f"Erro {response.status_code}")

                response.raise_for_status()
                data = response.json()

                if isinstance(data, dict):
                    items = data.get("items", [])
                    has_more_pages = data.get("hasMorePages", False) if use_pagination else False
                elif isinstance(data, list):
                    items = data
                    has_more_pages = len(items) == self.current_page_size if use_pagination else False
                else:
                    logger.error(f"Formato inesperado da resposta: {type(data)}")
                    raise ValueError("Resposta da API em formato inválido")

                if not items:
                    logger.info("Nenhum dado adicional encontrado.")
                    break

                new_items = []
                for item in items:
                    item_id = item.get("id")
                    if item_id and item_id not in seen_ids:
                        seen_ids.add(item_id)
                        new_items.append(item)
                    else:
                        stats["duplicate_items"] += 1
                        logger.debug(f"Item duplicado encontrado: {item_id}")

                if not new_items and items:
                    logger.info("Todos os itens da página já foram vistos anteriormente.")
                    break

                all_items.extend(new_items)
                stats["successful_pages"] += 1
                logger.debug(f"Página {current_page}: {len(new_items)} novos itens (Total: {len(all_items)}, Duplicatas: {stats['duplicate_items']})")

                if not has_more_pages:
                    logger.info("Todas as páginas foram processadas.")
                    break

                current_page += 1
                time.sleep(1)

        except requests.exceptions.RequestException as e:
            logger.error(f"Erro na requisição: {str(e)}", exc_info=True)
            raise

        stats["total_items"] = len(all_items)
        logger.info(f"Total de itens coletados: {len(all_items)} em {stats['successful_pages']} páginas")
        return all_items, stats