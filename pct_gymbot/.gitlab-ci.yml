stages:
  - build
  - chat_log

variables:
  TAG: registry.gitlab.com/plataformazw/pacto-pydata-eng:$CI_COMMIT_REF_SLUG
  TAG_MASTER: registry.gitlab.com/plataformazw/pacto-pydata-eng:master

before_script:
  - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
  - export PYTHONDONTWRITEBYTECODE=1

.build-common:
  stage: build
  interruptible: true
  except:
    variables:
      - $IPNET
      - $REDE_FAB
      - $ATIVOS
      - $ATIVOS_LISTA
      - $PACTO_GRUPO_PACTO_QR
      - $ENGENHARIA

# Jobs existentes
pct-taxa-cartao:
  extends: .build-common
  only:
    variables:
      - $TAXA_CARTAO
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/taxas_cartao.py

pct-tela-login:
  extends: .build-common
  only:
    variables:
      - $TELA_LOGIN
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" -e LGN_KEY=$LGN_KEY --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pacto/tela_login.py

# Jobs do pct_gymbot
pct-fetch-sessions:
  extends: .build-common
  only:
    variables:
      - $FETCH_SESSIONS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_sessions.py
  artifacts:
    paths:
      - pct_gymbot/logs/sessions.log

pct-fetch-contacts:
  extends: .build-common
  only:
    variables:
      - $FETCH_CONTACTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_contacts.py
  artifacts:
    paths:
      - pct_gymbot/logs/contacts.log

pct-fetch-agents:
  extends: .build-common
  only:
    variables:
      - $FETCH_AGENTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_agents.py
  artifacts:
    paths:
      - pct_gymbot/logs/agents.log

pct-fetch-panels:
  extends: .build-common
  only:
    variables:
      - $FETCH_PANELS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_panels.py
  artifacts:
    paths:
      - pct_gymbot/logs/panels.log

pct-fetch-channels:
  extends: .build-common
  only:
    variables:
      - $FETCH_CHANNELS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_channels.py
  artifacts:
    paths:
      - pct_gymbot/logs/channels.log

pct-fetch-departments:
  extends: .build-common
  only:
    variables:
      - $FETCH_DEPARTMENTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_departments.py
  artifacts:
    paths:
      - pct_gymbot/logs/departments.log

pct-fetch-tags:
  extends: .build-common
  only:
    variables:
      - $FETCH_TAGS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_tags.py
  artifacts:
    paths:
      - pct_gymbot/logs/tags.log

pct-fetch-chatbots:
  extends: .build-common
  only:
    variables:
      - $FETCH_CHATBOTS
  script:
    - docker run -e KEY_GCP_DATALAKE="$KEY_GCP_DATALAKE_MERCADO" --rm -v "$(pwd)":/usr/src/app --network host $TAG_MASTER pct_gymbot/src/runners/run_chatbots.py
  artifacts:
    paths:
      - pct_gymbot/logs/chatbots.log
