from src.runners import run_sessions, run_contacts, run_agents, run_channels, run_departments, run_tags, run_chatbots, run_panels

def main():
    runners = {
        "sessions": run_sessions,
        "contacts": run_contacts,
        "panels": run_panels, 
        "agents": run_agents,
        "channels": run_channels,
        "departments": run_departments,
        "tags": run_tags,
        "chatbots": run_chatbots
    }
    for name, runner in runners.items():
        runner()    

if __name__ == "__main__":
    main()