2025-03-28 10:41:03,011 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:41:03,012 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:41:03,020 - INFO - ✅ BigQuery connection established
2025-03-28 10:41:03,355 - INFO - Página 1: 16 novos itens (Total: 16, Duplicatas: 0)
2025-03-28 10:41:03,357 - INFO - <PERSON><PERSON> as páginas foram processadas.
2025-03-28 10:41:07,121 - CRITICAL - Critical error: 400 Duplicate(Case Insensitive) field names: textColor and textcolor. Table: tags_ba46483c_4100_40a6_9ba0_83f05ff01a05_source; reason: invalidQuery, location: query, message: Duplicate(Case Insensitive) field names: textColor and textcolor. Table: tags_ba46483c_4100_40a6_9ba0_83f05ff01a05_source
Traceback (most recent call last):
  File "c:\Users\<USER>\pacto-pydata-eng\pct_gymbot\src\runners\run_tags.py", line 40, in run_tags
    success = bq_manager.load_data("tags", items, logger)
  File "C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\src\api\bigquery_manager.py", line 82, in load_data
    job.result()  # Aguarda o job completar
    ~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\cloud\bigquery\job\base.py", line 971, in result
    return super(_AsyncJob, self).result(timeout=timeout, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\future\polling.py", line 261, in result
    raise self._exception
google.api_core.exceptions.BadRequest: 400 Duplicate(Case Insensitive) field names: textColor and textcolor. Table: tags_ba46483c_4100_40a6_9ba0_83f05ff01a05_source; reason: invalidQuery, location: query, message: Duplicate(Case Insensitive) field names: textColor and textcolor. Table: tags_ba46483c_4100_40a6_9ba0_83f05ff01a05_source
2025-03-28 10:42:19,356 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:42:19,357 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:42:19,367 - INFO - ✅ BigQuery connection established
2025-03-28 10:42:19,694 - INFO - Página 1: 16 novos itens (Total: 16, Duplicatas: 0)
2025-03-28 10:42:19,695 - INFO - Todas as páginas foram processadas.
2025-03-28 10:42:24,074 - INFO - Dados carregados com sucesso na tabela tags
2025-03-28 10:42:24,075 - INFO - 
📊 Tags Result:
- Items: 16
- Pages: 1
- Status: ❌ FAILURE
2025-03-28 10:54:29,070 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:54:29,071 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 10:54:29,080 - INFO - ✅ BigQuery connection established
2025-03-28 10:54:29,874 - INFO - Página 1: 16 novos itens (Total: 16, Duplicatas: 0)
2025-03-28 10:54:29,875 - INFO - Todas as páginas foram processadas.
2025-03-28 10:54:34,333 - INFO - Dados carregados com sucesso na tabela tags
2025-03-28 10:54:34,333 - INFO - 
📊 Tags Result:
- Items: 16
- Pages: 1
- Status: ❌ FAILURE
2025-03-28 15:42:21,941 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 15:42:21,953 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 15:42:21,953 - INFO - ✅ BigQuery connection established
2025-03-28 15:42:24,083 - INFO - Carregando dados incrementais desde: 2025-03-24 20:04:35.017769+00:00
2025-03-28 15:42:24,084 - CRITICAL - Critical error: EnhancedAPIClient.fetch_paginated_data() got an unexpected keyword argument 'use_pagination'
Traceback (most recent call last):
  File "c:\Users\<USER>\pacto-pydata-eng\pct_gymbot\src\runners\run_tags.py", line 39, in run_tags
    items, stats = api_client.fetch_paginated_data(
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        endpoint=config["path"],
        ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        use_pagination=False
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
TypeError: EnhancedAPIClient.fetch_paginated_data() got an unexpected keyword argument 'use_pagination'
2025-03-28 15:43:39,756 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 15:43:39,765 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-03-28 15:43:39,766 - INFO - ✅ BigQuery connection established
2025-03-28 15:43:41,298 - INFO - Carregando dados incrementais desde: 2025-03-24 20:04:35.017769+00:00
2025-03-28 15:43:41,777 - INFO - Página 1: 16 novos itens (Total: 16, Duplicatas: 0)
2025-03-28 15:43:41,778 - INFO - Todas as páginas foram processadas.
2025-03-28 15:43:43,263 - INFO - Filtrando dados com updatedAt após 2025-03-24 20:04:35.017769+00:00
2025-03-28 15:43:43,264 - INFO - Nenhum dado novo pra carregar na tabela tags
2025-03-28 15:43:43,264 - INFO - 
📊 Tags Result:
- Items: 16
- Pages: 1
- Status: ✅ SUCCESS
2025-04-02 13:12:18,111 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-02 13:12:18,112 - ERROR - Arquivo de credencial não encontrado: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-02 13:12:18,112 - ERROR - ❌ Initialization failed: Credentials file not found at C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-02 13:12:18,112 - CRITICAL - Critical error: BigQuery initialization failed
Traceback (most recent call last):
  File "c:\Users\<USER>\pacto-pydata-eng\pct_gymbot\src\runners\run_tags.py", line 28, in run_tags
    raise Exception("BigQuery initialization failed")
Exception: BigQuery initialization failed
2025-04-02 13:13:51,467 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-02 13:13:51,490 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-02 13:13:51,491 - INFO - ✅ BigQuery connection established
2025-04-02 13:13:53,374 - INFO - Carregando dados incrementais desde: 2025-03-24 20:04:35.017769+00:00 (filtro aplicado localmente)
2025-04-02 13:13:53,748 - INFO - Página 1: 16 novos itens (Total: 16, Duplicatas: 0)
2025-04-02 13:13:53,749 - INFO - Todas as páginas foram processadas.
2025-04-02 13:13:54,938 - INFO - Filtrando dados com updatedAt após 2025-03-24 20:04:35.017769+00:00
2025-04-02 13:13:54,938 - INFO - Nenhum dado novo pra carregar na tabela tags
2025-04-02 13:13:54,939 - INFO - 
📊 Tags Result:
- Items: 16
- Pages: 1
- Status: ✅ SUCCESS
2025-04-03 16:33:32,541 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-03 16:33:32,556 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-03 16:33:32,557 - INFO - ✅ BigQuery connection established
2025-04-03 16:33:34,279 - INFO - Nenhum estado incremental encontrado pra tabela tags
2025-04-03 16:33:34,631 - INFO - Página 1: 17 novos itens (Total: 17, Duplicatas: 0)
2025-04-03 16:33:34,631 - INFO - Todas as páginas foram processadas.
2025-04-03 16:33:34,632 - INFO - Total de itens recebidos da API pra tags: 17
2025-04-03 16:33:34,632 - INFO - Carregando todos os 17 itens (sem filtro incremental)
2025-04-03 16:33:34,633 - INFO - Total de itens sanitizados: 17
2025-04-03 16:33:34,901 - INFO - Tabela tags não existe, criando com autodetect.
2025-04-03 16:33:40,559 - INFO - Dados carregados com sucesso na tabela tags (17 itens)
2025-04-03 16:33:40,560 - INFO - 
📊 Tags Result:
- Items: 17
- Pages: 1
- Status: ✅ SUCCESS
2025-04-04 14:19:06,724 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 14:19:06,734 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 14:19:06,734 - INFO - ✅ BigQuery connection established
2025-04-04 14:19:08,396 - INFO - Nenhum estado incremental encontrado pra tabela tags
2025-04-04 14:19:08,698 - INFO - Página 1: 17 novos itens (Total: 17, Duplicatas: 0)
2025-04-04 14:19:08,698 - INFO - Todas as páginas foram processadas.
2025-04-04 14:19:08,698 - INFO - Total de itens recebidos da API pra tags: 17
2025-04-04 14:19:08,699 - INFO - Parâmetro incremental configurado: None
2025-04-04 14:19:08,699 - INFO - Carregando todos os 17 itens (sem filtro incremental)
2025-04-04 14:19:08,699 - INFO - Total de itens sanitizados: 17
2025-04-04 14:19:08,700 - INFO - Total de itens sanitizados pra carga: 17
2025-04-04 14:19:09,034 - INFO - Tabela tags já existe, usando WRITE_APPEND com esquema existente.
2025-04-04 14:19:13,494 - INFO - Dados carregados com sucesso na tabela tags (17 itens)
2025-04-04 14:19:13,495 - INFO - 
📊 Tags Result:
- Items: 17
- Pages: 1
- Status: ✅ SUCCESS
2025-04-04 17:47:36,454 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 17:47:36,465 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 17:47:36,465 - INFO - ✅ BigQuery connection established
2025-04-04 17:47:36,993 - INFO - Página 1: 17 novos itens (Total: 17, Duplicatas: 0)
2025-04-04 17:47:36,994 - INFO - Todas as páginas foram processadas.
2025-04-04 17:47:36,994 - INFO - Dados brutos retornados pela API: [{"id": "0418f096-b095-49da-964b-952f57b90d69", "createdAt": "2025-02-17T15:13:33.716463Z", "updatedAt": "2025-02-17T15:13:33.716463Z", "name": "Concluindo", "bgColor": "rgb(23, 90, 99)", "textColor": "white"}, {"id": "1cd5a4d4-fd7a-4d9e-9da2-98ba83d9243f", "createdAt": "2025-01-06T15:08:03.389506Z", "updatedAt": "2025-01-06T15:08:15.917352Z", "name": "Suporte Redes", "bgColor": "rgb(64, 22, 148)", "textColor": "white"}, {"id": "58a95e9e-5ced-4e14-8852-bc14121c5544", "createdAt": "2024-09-25T20:03:40.126434Z", "updatedAt": "2024-09-25T20:03:40.126434Z", "name": "Venda", "bgColor": "rgb(0, 200, 117)", "textColor": "white"}, {"id": "b4dbf9c5-de55-40b3-80eb-e03e74c4acb9", "createdAt": "2024-09-25T20:03:40.126438Z", "updatedAt": "2024-09-25T20:03:40.126438Z", "name": "Oportunidade", "bgColor": "rgb(255, 203, 0)", "textColor": "#4F4F4F"}, {"id": "8c09b952-349a-4227-a48d-37a30c0c3cb6", "createdAt": "2024-09-25T20:03:40.126438Z", "updatedAt": "2024-09-25T20:03:40.126438Z", "name": "Venda perdida", "bgColor": "rgb(226, 68, 92)", "textColor": "white"}, {"id": "352a4be5-1a54-410a-b088-db4f773de3f6", "createdAt": "2024-09-25T20:03:40.126439Z", "updatedAt": "2024-09-25T20:03:40.126439Z", "name": "Cliente", "bgColor": "rgb(0, 134, 192)", "textColor": "white"}, {"id": "4470609c-663f-4705-800d-32fd98c03d68", "createdAt": "2024-12-05T18:34:34.313381Z", "updatedAt": "2024-12-05T18:34:34.313381Z", "name": "Lead", "bgColor": "rgb(0, 200, 117)", "textColor": "white"}, {"id": "d60e8c20-a437-44ef-b3bb-b1d724c0c4c6", "createdAt": "2024-12-16T13:56:45.03705Z", "updatedAt": "2024-12-16T13:56:45.03705Z", "name": "Suporte Grandes Contas", "bgColor": "black", "textColor": "white"}, {"id": "706800ea-29d4-445b-a74f-9f753e5e1fa2", "createdAt": "2025-01-15T18:04:47.406208Z", "updatedAt": "2025-01-15T18:04:47.406208Z", "name": "Aclon", "bgColor": "rgb(86, 62, 62)", "textColor": "white"}, {"id": "8694bd6f-bff6-4855-a7e0-450eacc98c03", "createdAt": "2025-02-14T20:00:07.052779Z", "updatedAt": "2025-02-14T20:00:07.052779Z", "name": "Aguardando", "bgColor": "black", "textColor": "white"}, {"id": "ed9a8567-ed17-413d-bc60-10f4597298c1", "createdAt": "2025-02-17T13:58:28.689434Z", "updatedAt": "2025-02-17T13:58:28.689434Z", "name": "Fypay", "bgColor": "rgb(3, 127, 76)", "textColor": "white"}, {"id": "c9026d6d-199d-49b3-bbc4-45086196e3bc", "createdAt": "2024-12-10T21:28:19.691062Z", "updatedAt": "2025-01-28T15:52:58.400336Z", "name": "Suporte Célula Baby", "bgColor": "rgb(255, 153, 255)", "textColor": "white"}, {"id": "91cbe6ed-0398-4f30-a5cf-62469b771b3b", "createdAt": "2024-09-25T20:03:40.126439Z", "updatedAt": "2025-01-28T13:57:51.123176Z", "name": "Pendente", "bgColor": "rgb(255, 173, 173)", "textColor": "#4F4F4F"}, {"id": "6c6ea592-60dc-4c7d-a6bd-caad30d05651", "createdAt": "2024-12-16T13:57:02.848725Z", "updatedAt": "2025-03-24T20:04:35.017769Z", "name": "Suporte N1", "bgColor": "#D2C8F7", "textColor": "#4F4F4F"}, {"id": "e8d0c87a-7b92-439e-892d-ecfeb8a8d647", "createdAt": "2025-01-31T14:13:29.795474Z", "updatedAt": "2025-01-31T14:13:29.795474Z", "name": "aviso-novo-numero-pacto01", "bgColor": "black", "textColor": "white"}, {"id": "b68ff9d4-c72c-49d9-b715-3f1497d48bb9", "createdAt": "2025-03-11T21:02:28.523303Z", "updatedAt": "2025-03-11T21:02:28.523303Z", "name": "Aluno", "bgColor": "rgb(226, 68, 92)", "textColor": "white"}, {"id": "fa066b25-510a-41ef-a317-d091ea610332", "createdAt": "2025-04-03T11:30:12.326162Z", "updatedAt": "2025-04-03T11:30:12.326162Z", "name": "Arnold25 Pré-cadastro", "bgColor": "black", "textColor": "white"}]
2025-04-04 17:47:36,995 - INFO - Total de itens recebidos da API pra tags: 17
2025-04-04 17:47:36,995 - INFO - Parâmetro incremental configurado: None, Modo de escrita: WRITE_TRUNCATE
2025-04-04 17:47:36,995 - INFO - Carregando todos os 17 itens (sem filtro incremental)
2025-04-04 17:47:36,996 - INFO - Total de itens sanitizados: 17
2025-04-04 17:47:36,996 - INFO - Total de itens sanitizados pra carga: 17
2025-04-04 17:47:36,996 - INFO - Tabela tags será truncada e recarregada com WRITE_TRUNCATE.
2025-04-04 17:47:42,571 - INFO - Dados carregados com sucesso na tabela tags (17 itens)
2025-04-04 17:47:42,572 - INFO - 
📊 Tags Result:
- Items: 17
- Pages: 1
- Status: ✅ SUCCESS
2025-04-04 17:48:35,110 - INFO - Tentando usar arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 17:48:35,119 - INFO - Usando arquivo local: C:\Users\<USER>\pacto-pydata-eng\pct_gymbot\service-account.json
2025-04-04 17:48:35,119 - INFO - ✅ BigQuery connection established
2025-04-04 17:48:35,581 - INFO - Página 1: 17 novos itens (Total: 17, Duplicatas: 0)
2025-04-04 17:48:35,581 - INFO - Todas as páginas foram processadas.
2025-04-04 17:48:35,581 - INFO - Total de itens recebidos da API pra tags: 17
2025-04-04 17:48:35,582 - INFO - Parâmetro incremental configurado: None, Modo de escrita: WRITE_TRUNCATE
2025-04-04 17:48:35,582 - INFO - Carregando todos os 17 itens (sem filtro incremental)
2025-04-04 17:48:35,583 - INFO - Total de itens sanitizados: 17
2025-04-04 17:48:35,583 - INFO - Total de itens sanitizados pra carga: 17
2025-04-04 17:48:35,584 - INFO - Tabela tags será truncada e recarregada com WRITE_TRUNCATE.
2025-04-04 17:48:40,624 - INFO - Dados carregados com sucesso na tabela tags (17 itens)
2025-04-04 17:48:40,624 - INFO - 
📊 Tags Result:
- Items: 17
- Pages: 1
- Status: ✅ SUCCESS
