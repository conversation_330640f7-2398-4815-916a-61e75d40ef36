{"service_account_file": "C:/Users/<USER>/pacto-pydata-eng/pct_gymbot/service-account.json", "project_id": "oamd-e-financeiro-pacto", "api": {"base_url": "https://pacto.atlassian.net", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Basic ****************************************************************************************************************************************************************************************************************************************************************************************************"}, "endpoints": {"jira": {"path": "rest/api/3/search", "params": {"jql": "project in (IA, M1) ORDER BY created DESC", "maxResults": 100, "fields": "key,summary,reporter,assignee,created,issuetype,priority,project,status,customfield_10020"}, "columns": [0, 4, 5, 6, 7, 9, 10, 12, 21, 28], "table_id": "dados_jira.jira_issues", "max_items": 1000, "max_duration": 3600, "excluded_fields": [], "incremental_param": null}}}}