{"service_account_file": "C:/Users/<USER>/pacto-pydata-eng/pct_gymbot/service-account.json", "project_id": "oamd-e-financeiro-pacto", "api": {"base_url": "https://api.wts.chat", "endpoints": {"sessions": {"path": "/chat/v1/session", "params": {}, "max_items": null, "max_duration": 120, "incremental_param": "CreatedAt.After", "excluded_fields": []}, "contacts": {"path": "/core/v1/contact", "params": {"details": "basic", "IncludeDetails": ["CustomFields", "Tags", "Portfolios"]}, "max_items": null, "max_duration": 120, "incremental_param": "CreatedAt.After", "excluded_fields": ["metadata"]}, "panels": {"path": "/crm/v1/panel", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30, "excluded_fields": []}, "agents": {"path": "/core/v1/agent", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30}, "channels": {"path": "/chat/v1/channel", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30}, "departments": {"path": "/core/v1/department", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30, "excluded_fields": ["directLinks", "agents"]}, "sessions_full": {"path": "/chat/v1/session", "params": {"details": "full"}, "max_items": null, "max_duration": 120, "incremental_param": "CreatedAt.After"}, "tags": {"path": "/core/v1/tag", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30}, "chatbots": {"path": "/chat/v1/chatbot", "params": {"activeOnly": "true"}, "max_items": 10000, "max_duration": 30, "excluded_fields": ["automationUsage"]}, "panel_card": {"path": "/crm/v1/panel/card", "params": {"IncludeDetails": ["Contacts", "PanelTitle", "<PERSON><PERSON><PERSON><PERSON>", "StepPhase", "ResponsibleUser", "CustomFields"]}, "max_items": 10000, "max_duration": 50, "excluded_fields": []}, "panel_note": {"path": "/crm/v1/panel/card/{cardId}/note", "params": {}, "excluded_fields": []}}, "headers": {"Authorization": "pn_BC1PYkWuQAGxq4KoaabB07SkCWDJbYm5sIjzrs71SY", "accept": "application/json", "X-Request-Source": "PactoGymBot"}}}