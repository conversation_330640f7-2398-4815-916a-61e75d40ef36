# -*- coding: utf-8 -*-
import sys
sys.path.insert(5, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='academia-de-verdade'
DATASET_ID = "producao"


# producao
CHAVES_ESPECIFICAS = "7c5240df40569710cfe2e44f529984b7,cd16aad669f2aa92243b9210b50c5419,f7b07788895d2b2d629c663ff357df8e,cc2bd365ab3e5ae0e2d53a7e33683650,14d8427dc0582233b6db7f47ebcfd4c,2366443b37686f5c012dcca87be2243c,efc2fc0fa6d5926fc12f60fc396a52c3,943c1927c5e14ef533b4d12e27a93136,1785fe760ffec28d848961e6aafc698c,3d3048c33f0666ac4d4898b6efd58961893b3fb3a4f0136c09dda36d812a8c1,854df607c1b70e321ae4e28db5b5e3d4,495afa1552fb8735271a281669f11937,a5cf52fb0ff117dddc5ec6c21ea6039,e94c31514847f3e6ab8b87c0ca4cba02,ff064cc6ac0cf57ec1891a41bfae0c47,fd947c2bd4e611729a250742fccd5974,a98f48e55c4262c602660ef2e9e2fa33,bed05b8f6ac7f2519f94f6a442c146f6,d485e7096869560c388b07b8dc5a006d,fdcb924496e854d35414c1328afe44a,1a02705ddd7953708a3038c7efb7d9bb,a175db64085ec5c3aa7446446ec6adf3,88ea93b577ea3bb3f11eacca675e4d0b,52353a92f09778de8c096788bdda0d48,d302f837a85622cb82f278cc781fc47,aa212223e8b0812a65289a8feb38bf54,ecc3e784c47ec3c73ab81e4bd12f01b5,8ae4a2ea9630d73e6f21cfdfa3fecfa6,2423d15b533e65d7e1678bdd951cabd1,fa969aec6226d1fa5cac1dd57780f52f,d1e6c994d1591ca787d293d88b2b655b,60af85257bcbbea7569312fe6ab602ed,bed9b599b4dd4a32c42b59e81cd677b0,e4218e7aa59efec108e98376426eaebf,4ed18da9b96b5f6aaeb63ae7a1fe067f"
# CHAVES_ESPECIFICAS = "fd947c2bd4e611729a250742fccd5974,7c5240df40569710cfe2e44f529984b7"
# ---------------------------------------------------------------------------------------------------------------------------------------------------------------
CHAVES_EMPRESA_FINANCEIRO = CHAVES_ESPECIFICAS.split(',')
CHAVES_EMPRESA_FINANCEIRO_FORMAT = ",".join(f"'{key}'" for key in CHAVES_EMPRESA_FINANCEIRO)

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '5bb5e37635dba35c47f7d7a6bd09d2496d56a210',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110607617536668275794",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/academia-de-verdade%40academia-de-verdade.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )


# util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path='churn')
# util.verificar_dataset(DATASET_ID)
      

# util.generate_data_all_zones('person', parallel=True)
# util.generate_data_all_zones('acessocliente', parallel=True)
# util.generate_data_all_zones('cliente_sintetico_geral', parallel=True)
# util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)
#util.generate_data_all_zones('churn_acessos', parallel=True)

#############################################################################################################3
DATASET_ID = "gameficacao"
CHAVES_ESPECIFICAS = "25f66506620e09cf610e0e9c8cf20fa6,4baa252fbd2c2b1315d0a7741ab30713,a436babea373321d88f0be56d96b8886,7634e2f319f4ba387cbfe8d53f07f11b,c7c3aa83e2fe52809c484a6087760041,a49c3cfdd8e5df17a81e2abf452af2f"
util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path='powerdata')
util.verificar_dataset(DATASET_ID)

util.generate_data_all_zones('avaliacaofisica', prefix='bdmusc', parallel=True)
util.generate_data_all_zones('dfsinteticodw', parallel=True)
util.generate_data_all_zones('faturamento', parallel=True)
util.generate_data_all_zones('finan_lancamentos', parallel=True)

