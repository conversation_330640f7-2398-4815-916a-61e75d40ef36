# -*- coding: utf-8 -*-
import sys
sys.path.insert(5, '/usr/src/app')

from google.oauth2 import service_account
from extras.Util import *

#Iniciar variáveis importantes
PROJECT_ID='academia-de-verdade'
DATASET_ID = "dados_mra"
CHAVES_ESPECIFICAS = "60af85257bcbbea7569312fe6ab602ed,3ea0060c92d5b348e29a3c571dccc284,7238dcfb244fc931d63bd1aee764767a"
#  ESSENCE GYM, SAUDE E CIA, HOPE ANAPOIS

# ROTA DO DINHEIRO , ACADEMIA PACTO PRODUÇÃO, BRAVA ,
# 83cf8a192ebe8e66a0b71e6614843c7c,aca438e8c9e947e64db2236bb2f1f7a9,495afa1552fb8735271a281669f11937

credentials = service_account.Credentials.from_service_account_info(
                      {
                        'type': 'service_account',
                        f'project_id': '{PROJECT_ID}',
                        'private_key_id': '5bb5e37635dba35c47f7d7a6bd09d2496d56a210',
                        'private_key': '{0}'.format(os.environ.get('KEY_GCP_DATALAKE')).replace('\\n', '\n'),
                        "client_email": "<EMAIL>",
                        "client_id": "110607617536668275794",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/academia-de-verdade%40academia-de-verdade.iam.gserviceaccount.com",
                        "universe_domain": "googleapis.com"
                      }
                      )

location='southamerica-east1'
util = Util(PROJECT_ID, DATASET_ID, CHAVES_ESPECIFICAS, credentials=credentials, base_path='powerdata', location=location)
util.verificar_dataset(DATASET_ID)

if os.environ.get('MRA_TICKET_MEDIO'):
  from projetos_internos.integracao_sistema_pacto.ticket_medio import generate_ticket_medio
  ticket_medio = generate_ticket_medio(util.chaves.split(','))
  util.to_gbq(df=ticket_medio, dataset_id='dados_mra', table_id='ticket_medio', project_id=PROJECT_ID, credentials=credentials)
else:
################## DADOS OAMD-Financeiro rede_empresa_financeiro ##################
  TABLE_ID='rede_empresa_financeiro'
  QUERY=util.getsql('rede_empresa_financeiro_semrede').format(util.chaves_escaped)
  util.generate_data_oamd(TABLE_ID, QUERY, lst_date_times=['finalimplantacao', 'inicioimplantacao', 'inicioproducao'])
      
########################################DADOS ZONAS#################################################
util.generate_data_all_zones('acessos', parallel=True)
util.generate_data_all_zones('dadosgerenciais')
util.generate_data_all_zones('tpv-previsao')
util.generate_data_all_zones('plano')
util.generate_data_all_zones('person')
util.generate_data_all_zones('crm', parallel=True)
util.generate_data_all_zones('situacaoclientesinteticodw', parallel=True)
util.generate_data_all_zones('grupo_de_risco', parallel=True)

util.generate_data_all_zones('qtd_bv', parallel=True)
util.generate_data_all_zones('matricula_rematricula', parallel=True)
util.generate_data_all_zones('qtd_visitas_mensal', parallel=True)
util.generate_data_all_zones('contrato_operacoes', parallel=True)
util.generate_data_all_zones('colab')
util.generate_data_all_zones('marketing')
util.generate_data_all_zones('pagamentosconjuntos')
util.generate_data_all_zones('estorno_cancelamento')
util.generate_data_all_zones('finan_lancamentos')
# util.generate_data_all_zones('agregacao_geral', parallel=True)
util.generate_data_all_zones('treinos_predefinidos', prefix='bdmusc')
util.generate_data_all_zones('faturamento', parallel=True)

util.generate_data_all_zones('mov_contrato_saida')

# util.create_view()